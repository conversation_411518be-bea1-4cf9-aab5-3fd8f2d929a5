#!/usr/bin/env python3
"""
Comparison test for different external turbine filtering methods
Compares computational performance and physical accuracy
"""

import numpy as np
import pandas as pd
import time
import sys
import os
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt

sys.path.append('/project/bii69/ps/ps/optimization')
from test_enhanced_farm_filtering import EnhancedDirectionalFarmManager


class FilteringMethodComparison:
    """
    Compare different external turbine filtering approaches
    """
    
    def __init__(self, internal_layout: pd.DataFrame, external_layout: pd.DataFrame, 
                 max_distance_km: float = 20):
        """
        Initialize comparison with layout data
        
        Args:
            internal_layout: Internal turbines DataFrame
            external_layout: External turbines DataFrame
            max_distance_km: Maximum distance threshold for filtering
        """
        self.internal_layout = internal_layout
        self.external_layout = external_layout
        self.max_distance_km = max_distance_km
        
        # Test wind directions (every 30 degrees)
        self.test_wind_directions = np.arange(0, 360, 30)
        
        # Initialize different filtering methods
        self._setup_filtering_methods()
        
    def _setup_filtering_methods(self):
        """Set up different filtering approaches for comparison"""
        self.methods = {}
        
        # Method 1: No filtering - all external turbines always included
        self.methods['no_filter'] = {
            'name': 'No Filtering',
            'description': 'Include all external turbines for all wind directions',
            'manager': None  # Will use simple approach
        }
        
        # Method 2: Distance-only filtering
        self.methods['distance_only'] = {
            'name': 'Distance Only',
            'description': f'Include all external turbines within {self.max_distance_km}km',
            'manager': self._create_distance_manager()
        }
        
        # Method 3: Individual turbine directional filtering (current smart method)
        self.methods['individual_smart'] = {
            'name': 'Individual Smart',
            'description': 'Filter individual turbines based on upwind direction',
            'manager': self._create_individual_smart_manager()
        }
        
        # Method 4: Farm-level directional filtering with 90° sectors
        self.methods['farm_90deg'] = {
            'name': 'Farm-Level 90°',
            'description': 'Filter entire farms with 90° upwind sectors',
            'manager': EnhancedDirectionalFarmManager(
                self.internal_layout, self.external_layout, 
                self.max_distance_km, sector_width=90, farm_grouping='auto'
            )
        }
        
        # Method 5: Farm-level directional filtering with 120° sectors
        self.methods['farm_120deg'] = {
            'name': 'Farm-Level 120°',
            'description': 'Filter entire farms with 120° upwind sectors',
            'manager': EnhancedDirectionalFarmManager(
                self.internal_layout, self.external_layout, 
                self.max_distance_km, sector_width=120, farm_grouping='auto'
            )
        }
        
        # Method 6: Farm-level directional filtering with 60° sectors
        self.methods['farm_60deg'] = {
            'name': 'Farm-Level 60°',
            'description': 'Filter entire farms with 60° upwind sectors',
            'manager': EnhancedDirectionalFarmManager(
                self.internal_layout, self.external_layout, 
                self.max_distance_km, sector_width=60, farm_grouping='auto'
            )
        }
        
    def _create_distance_manager(self):
        """Create a manager that only filters by distance"""
        # This is essentially the base case - filter by distance but not direction
        from test_enhanced_farm_filtering import SmartExternalLayoutManager
        return SmartExternalLayoutManager(
            self.internal_layout, self.external_layout, self.max_distance_km
        )
        
    def _create_individual_smart_manager(self):
        """Create manager that mimics current individual turbine smart filtering"""
        # Use our enhanced manager with individual grouping
        return EnhancedDirectionalFarmManager(
            self.internal_layout, self.external_layout, 
            self.max_distance_km, sector_width=90, farm_grouping='individual'
        )
    
    def compare_turbine_counts(self) -> Dict:
        """Compare number of external turbines included by each method"""
        results = {}
        
        for method_id, method_info in self.methods.items():
            results[method_id] = {
                'name': method_info['name'],
                'description': method_info['description'],
                'by_direction': {},
                'total_external': len(self.external_layout),
                'avg_included': 0,
                'min_included': float('inf'),
                'max_included': 0
            }
            
            turbine_counts = []
            
            for wind_dir in self.test_wind_directions:
                if method_id == 'no_filter':
                    # Always include all external turbines
                    n_turbines = len(self.external_layout)
                elif method_id == 'distance_only':
                    # Include all within distance (same for all directions)
                    layout = self._get_distance_filtered_layout()
                    n_turbines = len(layout[layout['external'] == True])
                else:
                    # Use the manager's filtering
                    manager = method_info['manager']
                    if hasattr(manager, 'get_layout_with_farm_filtering'):
                        layout = manager.get_layout_with_farm_filtering(wind_dir)
                    else:
                        # Fallback for distance-only manager
                        layout = pd.concat([self.internal_layout, self.external_layout])
                        layout['weight'] = [1 if not ext else 0 for ext in layout['external']]
                    
                    n_turbines = len(layout[layout['external'] == True])
                
                results[method_id]['by_direction'][wind_dir] = n_turbines
                turbine_counts.append(n_turbines)
            
            # Calculate statistics
            results[method_id]['avg_included'] = np.mean(turbine_counts)
            results[method_id]['min_included'] = np.min(turbine_counts)
            results[method_id]['max_included'] = np.max(turbine_counts)
            results[method_id]['std_included'] = np.std(turbine_counts)
        
        return results
    
    def _get_distance_filtered_layout(self):
        """Get layout with only distance filtering"""
        # Calculate distances from internal centroid
        cx = np.mean(self.internal_layout['x'])
        cy = np.mean(self.internal_layout['y'])
        
        distances = np.sqrt(
            (self.external_layout['x'] - cx)**2 + 
            (self.external_layout['y'] - cy)**2
        )
        
        # Filter by distance
        mask = distances <= self.max_distance_km * 1000
        filtered_external = self.external_layout[mask]
        
        # Combine with internal
        combined = pd.concat([self.internal_layout, filtered_external], ignore_index=True)
        combined['weight'] = [1 if not ext else 0 for ext in combined['external']]
        
        return combined
    
    def compare_performance(self) -> Dict:
        """Compare computational performance of different methods"""
        results = {}
        
        for method_id, method_info in self.methods.items():
            print(f"Testing performance for {method_info['name']}...")
            
            # Measure time for multiple iterations
            times = []
            
            for iteration in range(10):  # 10 iterations for averaging
                start_time = time.time()
                
                # Simulate filtering for all wind directions
                for wind_dir in self.test_wind_directions:
                    if method_id == 'no_filter':
                        # Minimal work - just create layout
                        _ = pd.concat([self.internal_layout, self.external_layout])
                    elif method_id == 'distance_only':
                        _ = self._get_distance_filtered_layout()
                    else:
                        manager = method_info['manager']
                        if hasattr(manager, 'get_layout_with_farm_filtering'):
                            _ = manager.get_layout_with_farm_filtering(wind_dir)
                        else:
                            _ = self._get_distance_filtered_layout()
                
                end_time = time.time()
                times.append(end_time - start_time)
            
            results[method_id] = {
                'name': method_info['name'],
                'avg_time': np.mean(times),
                'std_time': np.std(times),
                'min_time': np.min(times),
                'max_time': np.max(times)
            }
        
        return results
    
    def analyze_farm_efficiency(self) -> Dict:
        """Analyze how efficiently farm-level methods group turbines"""
        if not hasattr(self, 'methods'):
            return {}
        
        results = {}
        
        for method_id, method_info in self.methods.items():
            if 'farm' not in method_id.lower():
                continue
                
            manager = method_info['manager']
            if not hasattr(manager, 'external_farms'):
                continue
            
            farms = manager.external_farms
            
            results[method_id] = {
                'name': method_info['name'],
                'n_farms': len(farms),
                'n_turbines': len(self.external_layout),
                'avg_farm_size': np.mean([f['n_turbines'] for f in farms.values()]),
                'largest_farm': max([f['n_turbines'] for f in farms.values()]),
                'smallest_farm': min([f['n_turbines'] for f in farms.values()]),
                'farm_details': {}
            }
            
            # Analyze each farm
            for farm_name, farm_data in farms.items():
                # Count how many directions this farm is active
                active_count = 0
                for wind_dir in self.test_wind_directions:
                    relevant_farms = manager.get_relevant_farms_for_direction(wind_dir)
                    if farm_name in relevant_farms:
                        active_count += 1
                
                results[method_id]['farm_details'][farm_name] = {
                    'n_turbines': farm_data['n_turbines'],
                    'bearing': farm_data['bearing'],
                    'distance_km': farm_data['distance'] / 1000,
                    'active_directions': active_count,
                    'activity_fraction': active_count / len(self.test_wind_directions)
                }
        
        return results
    
    def plot_comparison_results(self, turbine_counts: Dict, output_file: str = "filtering_comparison.png"):
        """Create visualization comparing different filtering methods"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        
        # Plot 1: Turbine counts by wind direction
        wind_dirs = self.test_wind_directions
        
        for method_id, data in turbine_counts.items():
            counts = [data['by_direction'][wd] for wd in wind_dirs]
            ax1.plot(wind_dirs, counts, 'o-', label=data['name'], linewidth=2, markersize=6)
        
        ax1.set_xlabel('Wind Direction (degrees)')
        ax1.set_ylabel('Number of External Turbines Included')
        ax1.set_title('External Turbines Included by Wind Direction')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)
        ax1.set_xlim(0, 360)
        
        # Plot 2: Average turbine counts (bar chart)
        methods = list(turbine_counts.keys())
        names = [turbine_counts[m]['name'] for m in methods]
        avg_counts = [turbine_counts[m]['avg_included'] for m in methods]
        std_counts = [turbine_counts[m]['std_included'] for m in methods]
        
        bars = ax2.bar(range(len(methods)), avg_counts, yerr=std_counts, 
                      capsize=5, alpha=0.7, color=plt.cm.Set3(np.linspace(0, 1, len(methods))))
        ax2.set_xlabel('Filtering Method')
        ax2.set_ylabel('Average External Turbines Included')
        ax2.set_title('Average External Turbines by Method')
        ax2.set_xticks(range(len(methods)))
        ax2.set_xticklabels(names, rotation=45, ha='right')
        ax2.grid(True, alpha=0.3, axis='y')
        
        # Add value labels on bars
        for i, (bar, avg, std) in enumerate(zip(bars, avg_counts, std_counts)):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + std + 1,
                    f'{avg:.1f}', ha='center', va='bottom', fontweight='bold')
        
        # Plot 3: Computational efficiency (if we have performance data)
        # For now, show reduction percentage
        total_external = len(self.external_layout)
        reduction_pcts = [(total_external - data['avg_included']) / total_external * 100 
                         for data in turbine_counts.values()]
        
        bars3 = ax3.bar(range(len(methods)), reduction_pcts, 
                       color=plt.cm.RdYlGn(np.linspace(0.2, 0.8, len(methods))), alpha=0.7)
        ax3.set_xlabel('Filtering Method')
        ax3.set_ylabel('Average Reduction (%)')
        ax3.set_title('Computational Load Reduction')
        ax3.set_xticks(range(len(methods)))
        ax3.set_xticklabels(names, rotation=45, ha='right')
        ax3.grid(True, alpha=0.3, axis='y')
        
        # Add percentage labels
        for bar, pct in zip(bars3, reduction_pcts):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                    f'{pct:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # Plot 4: Polar plot showing directional variation
        ax4 = plt.subplot(2, 2, 4, projection='polar')
        
        theta = np.radians(wind_dirs)
        
        for method_id, data in list(turbine_counts.items())[:4]:  # Limit to 4 methods for clarity
            counts = [data['by_direction'][wd] for wd in wind_dirs]
            ax4.plot(theta, counts, 'o-', label=data['name'], linewidth=2)
        
        ax4.set_theta_zero_location('N')
        ax4.set_theta_direction(-1)
        ax4.set_title('Directional Variation\n(Polar View)', pad=20)
        ax4.legend(loc='upper left', bbox_to_anchor=(1.1, 1.1))
        
        plt.tight_layout()
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Comparison plot saved to: {output_file}")
        plt.close()
    
    def generate_summary_report(self, turbine_counts: Dict, performance: Dict, 
                              farm_efficiency: Dict) -> str:
        """Generate text summary of comparison results"""
        report = []
        report.append("="*80)
        report.append("EXTERNAL TURBINE FILTERING METHOD COMPARISON REPORT")
        report.append("="*80)
        
        # Overview
        report.append(f"\nTest Configuration:")
        report.append(f"  Internal turbines: {len(self.internal_layout)}")
        report.append(f"  External turbines: {len(self.external_layout)}")
        report.append(f"  Distance threshold: {self.max_distance_km} km")
        report.append(f"  Wind directions tested: {len(self.test_wind_directions)}")
        
        # Turbine count analysis
        report.append(f"\nTURBINE INCLUSION ANALYSIS:")
        report.append("-" * 40)
        
        for method_id, data in turbine_counts.items():
            report.append(f"\n{data['name']}:")
            report.append(f"  Description: {data['description']}")
            report.append(f"  Average external turbines: {data['avg_included']:.1f}")
            report.append(f"  Range: {data['min_included']:.0f} - {data['max_included']:.0f}")
            report.append(f"  Standard deviation: {data['std_included']:.1f}")
            reduction = (len(self.external_layout) - data['avg_included']) / len(self.external_layout) * 100
            report.append(f"  Average reduction: {reduction:.1f}%")
        
        # Performance analysis
        if performance:
            report.append(f"\nPERFORMANCE ANALYSIS:")
            report.append("-" * 40)
            
            for method_id, data in performance.items():
                report.append(f"\n{data['name']}:")
                report.append(f"  Average time: {data['avg_time']:.4f} seconds")
                report.append(f"  Time range: {data['min_time']:.4f} - {data['max_time']:.4f} seconds")
                
                # Calculate speedup relative to no filtering
                if 'no_filter' in performance:
                    baseline = performance['no_filter']['avg_time']
                    speedup = baseline / data['avg_time']
                    report.append(f"  Speedup factor: {speedup:.2f}x")
        
        # Farm efficiency analysis
        if farm_efficiency:
            report.append(f"\nFARM GROUPING EFFICIENCY:")
            report.append("-" * 40)
            
            for method_id, data in farm_efficiency.items():
                report.append(f"\n{data['name']}:")
                report.append(f"  Number of farms: {data['n_farms']}")
                report.append(f"  Average farm size: {data['avg_farm_size']:.1f} turbines")
                report.append(f"  Largest farm: {data['largest_farm']} turbines")
                report.append(f"  Smallest farm: {data['smallest_farm']} turbines")
                
                # Most/least active farms
                farm_activities = [(name, details['activity_fraction']) 
                                 for name, details in data['farm_details'].items()]
                farm_activities.sort(key=lambda x: x[1], reverse=True)
                
                if farm_activities:
                    most_active = farm_activities[0]
                    least_active = farm_activities[-1]
                    report.append(f"  Most active farm: {most_active[0]} ({most_active[1]*100:.0f}% of directions)")
                    report.append(f"  Least active farm: {least_active[0]} ({least_active[1]*100:.0f}% of directions)")
        
        # Recommendations
        report.append(f"\nRECOMMENDATIONS:")
        report.append("-" * 40)
        
        # Find best methods
        best_reduction = max(turbine_counts.items(), 
                           key=lambda x: (len(self.external_layout) - x[1]['avg_included']))
        most_stable = min(turbine_counts.items(), key=lambda x: x[1]['std_included'])
        
        report.append(f"\n• Best computational reduction: {best_reduction[1]['name']}")
        report.append(f"  - Reduces external turbines by {(len(self.external_layout) - best_reduction[1]['avg_included'])/len(self.external_layout)*100:.1f}% on average")
        
        report.append(f"\n• Most stable method: {most_stable[1]['name']}")
        report.append(f"  - Standard deviation: {most_stable[1]['std_included']:.1f} turbines")
        
        if farm_efficiency:
            best_grouping = min(farm_efficiency.items(), key=lambda x: x[1]['n_farms'])
            report.append(f"\n• Most efficient grouping: {best_grouping[1]['name']}")
            report.append(f"  - Groups {len(self.external_layout)} turbines into {best_grouping[1]['n_farms']} farms")
        
        report.append("\n" + "="*80)
        
        return "\n".join(report)


def test_comparison():
    """Test comparison with real data if available, otherwise use synthetic"""
    print("="*60)
    print("FILTERING METHOD COMPARISON TEST")
    print("="*60)
    
    try:
        # Try to load real data
        internal_df = pd.read_csv('Input/Initial.layout.csv')
        external_df = pd.read_csv('Input/External.layout.csv')
        
        # Rename columns if needed
        if 'easting' in internal_df.columns:
            internal_df = internal_df.rename(columns={'easting': 'x', 'northing': 'y'})
        if 'easting' in external_df.columns:
            external_df = external_df.rename(columns={'easting': 'x', 'northing': 'y'})
        
        # Set external flags
        internal_df['external'] = False
        external_df['external'] = True
        
        print(f"Using real data: {len(internal_df)} internal, {len(external_df)} external turbines")
        
    except FileNotFoundError:
        print("Real data not found, creating synthetic test case...")
        
        # Create synthetic data with multiple external farms
        internal_df = pd.DataFrame({
            'x': [532000, 533000, 534000, 535000, 536000],
            'y': [5850000, 5850000, 5850000, 5850000, 5850000],
            'external': [False] * 5,
            'turb': ['Internal'] * 5
        })
        
        # Create multiple external farms in different directions
        external_df = pd.DataFrame({
            'x': ([534000]*10 + [528000]*10 + [534000]*10 + [540000]*10),  # N, W, S, E
            'y': ([5855000]*10 + [5850000]*10 + [5845000]*10 + [5850000]*10),
            'external': [True] * 40,
            'turb': (['North Farm']*10 + ['West Farm']*10 + ['South Farm']*10 + ['East Farm']*10)
        })
    
    # Create comparison instance
    comparison = FilteringMethodComparison(internal_df, external_df, max_distance_km=20)
    
    # Run comparisons
    print("\nAnalyzing turbine inclusion patterns...")
    turbine_counts = comparison.compare_turbine_counts()
    
    print("\nMeasuring computational performance...")
    performance = comparison.compare_performance()
    
    print("\nAnalyzing farm grouping efficiency...")
    farm_efficiency = comparison.analyze_farm_efficiency()
    
    # Generate visualizations
    print("\nCreating comparison plots...")
    comparison.plot_comparison_results(turbine_counts, "filtering_method_comparison.png")
    
    # Generate report
    print("\nGenerating summary report...")
    report = comparison.generate_summary_report(turbine_counts, performance, farm_efficiency)
    
    # Save report to file
    with open("filtering_comparison_report.txt", "w") as f:
        f.write(report)
    
    print(report)
    
    return comparison, turbine_counts, performance, farm_efficiency


if __name__ == "__main__":
    # Run comparison test
    comparison_results = test_comparison()
    
    print("\n" + "="*60)
    print("Comparison complete! Check generated files:")
    print("  - filtering_method_comparison.png")
    print("  - filtering_comparison_report.txt")
    print("="*60)