#!/usr/bin/env python3
"""
DEAP Optimization Manager Usage Examples

This script demonstrates how to use the flexible DEAP optimization manager
for wind farm layout optimization with consistent handling across all algorithms.

Key Features Demonstrated:
- Easy algorithm switching
- Consistent normalization and constraint handling
- Flexible operator configuration
- Multi-algorithm comparison
- Hybrid optimization sequences
- Configuration through params.py

Usage:
    python3 example_deap_usage.py
"""

import sys
import numpy as np
import pandas as pd
from pathlib import Path

# Add project paths
workDir = str(Path(__file__).parent.absolute())
sys.path.append(workDir)

# Import project modules
import params as pr
from deap_optimization_manager import DEAPOptimizationManager, DEAPProblemWrapper, OptimizationConfig
from test_deap_manager import create_test_problem


def example_1_easy_algorithm_switching():
    """Example 1: Easy Algorithm Switching"""
    print("\n" + "="*60)
    print("📌 EXAMPLE 1: Easy Algorithm Switching")
    print("="*60)
    
    # Setup problem (using mock data for demo)
    problem = create_test_problem()
    
    # Create manager with default configuration
    config = OptimizationConfig(
        pop_size=16,
        n_gen=5,
        verbose=True
    )
    manager = DEAPOptimizationManager(problem, config)
    
    # Switch between different algorithms easily
    algorithms_to_try = ['GA', 'DE', 'PSO']
    
    for algorithm in algorithms_to_try:
        print(f"\n🧬 Switching to {algorithm}...")
        
        # Easy algorithm switching with one line
        manager.set_algorithm(algorithm)
        
        # Run optimization
        result = manager.optimize()
        
        print(f"✅ {algorithm} Result: Best AEP = {result['f'][0]:.6f}")
        print(f"   Evaluations: {result['n_evals']}")
        print(f"   Time: {result['exec_time']:.2f}s")


def example_2_flexible_configuration():
    """Example 2: Flexible Configuration Through params.py"""
    print("\n" + "="*60)
    print("📌 EXAMPLE 2: Flexible Configuration")
    print("="*60)
    
    problem = create_test_problem()
    
    # Method 1: Use configuration from params.py
    print("\n🔧 Using configuration from params.py:")
    config_from_params = OptimizationConfig(
        algorithm=pr.deap_config['algorithm'],
        pop_size=pr.deap_config['pop_size'],
        n_gen=pr.deap_config['n_gen'],
        selection=pr.deap_config['selection'],
        crossover=pr.deap_config['crossover'],
        mutation=pr.deap_config['mutation'],
        verbose=pr.deap_config['verbose']
    )
    
    manager = DEAPOptimizationManager(problem, config_from_params)
    print(f"   Algorithm: {manager.config.algorithm}")
    print(f"   Operators: {manager.config.selection}, {manager.config.crossover}, {manager.config.mutation}")
    
    # Method 2: Use algorithm-specific default parameters
    print("\n🎯 Using algorithm-specific defaults:")
    
    for algorithm, algo_config in pr.deap_algorithms_config.items():
        if algorithm in ['GA', 'DE', 'PSO']:  # Test a few
            print(f"\n   {algorithm} defaults:")
            defaults = algo_config['default_params']
            
            config = OptimizationConfig(**defaults, verbose=False)
            manager.set_algorithm(algorithm)
            manager.config = config
            
            result = manager.optimize()
            print(f"     Result: {result['f'][0]:.6f}")


def example_3_operator_flexibility():
    """Example 3: Flexible Operator Configuration"""
    print("\n" + "="*60)
    print("📌 EXAMPLE 3: Flexible Operator Configuration") 
    print("="*60)
    
    problem = create_test_problem()
    config = OptimizationConfig(pop_size=16, n_gen=3, verbose=False)
    manager = DEAPOptimizationManager(problem, config)
    
    # Test different operator combinations from params.py
    print("\n🛠️  Testing different operator combinations:")
    
    test_combinations = [
        # Selection, Crossover, Mutation
        ('tournament', 'blend', 'gaussian'),
        ('roulette', 'sbx', 'polynomial'),
        ('tournament', 'uniform', 'uniform')
    ]
    
    results = []
    for selection, crossover, mutation in test_combinations:
        print(f"\n   Testing: {selection} + {crossover} + {mutation}")
        
        # Configure operators
        manager.configure_operators(
            selection=selection,
            crossover=crossover,
            mutation=mutation
        )
        
        # Get operator parameters from params.py
        selection_params = pr.deap_operators_config['selection'][selection]['params']
        crossover_params = pr.deap_operators_config['crossover'][crossover]['params']
        mutation_params = pr.deap_operators_config['mutation'][mutation]['params']
        
        print(f"     Selection params: {selection_params}")
        print(f"     Crossover params: {crossover_params}")
        print(f"     Mutation params: {mutation_params}")
        
        # Run optimization
        result = manager.optimize()
        results.append((f"{selection}+{crossover}+{mutation}", result['f'][0]))
        print(f"     Result: {result['f'][0]:.6f}")
    
    # Find best combination
    best_combo, best_fitness = max(results, key=lambda x: x[1])
    print(f"\n🏆 Best combination: {best_combo} (AEP: {best_fitness:.6f})")


def example_4_multi_algorithm_comparison():
    """Example 4: Multi-Algorithm Comparison"""
    print("\n" + "="*60)
    print("📌 EXAMPLE 4: Multi-Algorithm Comparison")
    print("="*60)
    
    problem = create_test_problem()
    config = OptimizationConfig(pop_size=16, n_gen=5, verbose=False)
    manager = DEAPOptimizationManager(problem, config)
    
    # Compare algorithms using configuration from params.py
    algorithms_to_compare = pr.deap_comparison_config['algorithms_to_compare']
    runs_per_algorithm = pr.deap_comparison_config['runs_per_algorithm']
    
    print(f"\n🔬 Comparing {len(algorithms_to_compare)} algorithms:")
    print(f"   Algorithms: {algorithms_to_compare}")
    print(f"   Runs per algorithm: {runs_per_algorithm}")
    
    # Run comparison
    comparison_results = manager.compare_algorithms(
        algorithms_to_compare[:3],  # Limit for demo
        runs_per_algorithm=1
    )
    
    # Analyze results
    print(f"\n📊 Comparison Results:")
    print(f"{'Algorithm':<12} {'Best AEP':<12} {'Avg AEP':<12} {'Std AEP':<12} {'Time(s)':<8}")
    print("-" * 60)
    
    for algorithm, results in comparison_results.items():
        stats = results['statistics']
        print(f"{algorithm:<12} {stats['fitness']['max']:<12.6f} "
              f"{stats['fitness']['mean']:<12.6f} {stats['fitness']['std']:<12.6f} "
              f"{stats['time']['mean']:<8.2f}")
    
    # Determine winner
    best_algorithm = max(comparison_results.keys(), 
                        key=lambda x: comparison_results[x]['statistics']['fitness']['max'])
    print(f"\n🏆 Winner: {best_algorithm}")


def example_5_hybrid_optimization():
    """Example 5: Hybrid Optimization Sequences"""
    print("\n" + "="*60)
    print("📌 EXAMPLE 5: Hybrid Optimization Sequences")
    print("="*60)
    
    problem = create_test_problem()
    config = OptimizationConfig(verbose=True)
    manager = DEAPOptimizationManager(problem, config)
    
    # Show available hybrid sequences from params.py
    print(f"\n🔗 Available hybrid sequences:")
    for seq_name, sequence in pr.deap_hybrid_sequences.items():
        total_gens = sum(stage['generations'] for stage in sequence)
        algorithms = [stage['algorithm'] for stage in sequence]
        print(f"   {seq_name}: {' → '.join(algorithms)} ({total_gens} total generations)")
    
    # Test a hybrid sequence
    sequence_name = 'fast_convergence'
    print(f"\n🚀 Running hybrid sequence: {sequence_name}")
    
    # Get sequence from params and scale down for demo
    original_sequence = pr.deap_hybrid_sequences[sequence_name]
    demo_sequence = []
    for stage in original_sequence:
        demo_stage = stage.copy()
        demo_stage['generations'] = min(stage['generations'], 3)  # Limit for demo
        demo_sequence.append(demo_stage)
    
    # Run hybrid optimization
    result = manager.run_hybrid_sequence(demo_sequence)
    
    print(f"\n✅ Hybrid optimization completed:")
    print(f"   Final best AEP: {result['final_best_f'][0]:.6f}")
    print(f"   Total evaluations: {result['total_evaluations']}")
    print(f"   Total time: {result['total_time']:.2f}s")
    
    print(f"\n📈 Stage-by-stage progress:")
    for stage_result in result['stage_results']:
        stage_aep = stage_result['result']['f'][0]
        print(f"   Stage {stage_result['stage']} ({stage_result['algorithm']}): "
              f"AEP = {stage_aep:.6f}")


def example_6_consistency_validation():
    """Example 6: Validation of Consistency Across Algorithms"""
    print("\n" + "="*60)
    print("📌 EXAMPLE 6: Consistency Validation")
    print("="*60)
    
    problem = create_test_problem()
    
    print(f"\n🔍 Validating consistency across algorithms:")
    print(f"   Problem dimensions: {problem.n_var} vars, {problem.n_obj} obj, {problem.n_constr} constr")
    print(f"   Initial solution x0 shape: {problem.x0.shape}")
    print(f"   x0 range: [{problem.x0.min():.3f}, {problem.x0.max():.3f}] (should be [0,1])")
    print(f"   Baseline AEP: {problem.baseline_aep:.3f} GWh")
    
    # Test that all algorithms start from same initial solution
    algorithms = ['GA', 'DE', 'PSO']
    config = OptimizationConfig(pop_size=8, n_gen=1, verbose=False)  # Single generation
    manager = DEAPOptimizationManager(problem, config)
    
    print(f"\n🎯 Testing initial solution consistency:")
    initial_evaluations = {}
    
    for algorithm in algorithms:
        manager.set_algorithm(algorithm)
        result = manager.optimize()
        
        # Since we're running only 1 generation from x0, results should be similar
        initial_evaluations[algorithm] = result['f'][0]
        print(f"   {algorithm}: Initial AEP = {result['f'][0]:.6f}")
    
    # Check consistency (should be identical or very close)
    aep_values = list(initial_evaluations.values())
    aep_std = np.std(aep_values)
    print(f"\n📊 Consistency check:")
    print(f"   AEP standard deviation: {aep_std:.8f}")
    print(f"   ✅ {'PASS' if aep_std < 1e-6 else 'FAIL'}: All algorithms start from same point")
    
    # Test constraint handling consistency
    print(f"\n🚧 Testing constraint handling:")
    test_solution = np.array([0.5] * 20)  # All turbines at center (should violate constraints)
    result = problem.evaluate(test_solution)
    
    space_violations = np.sum(result['G'][:10] > 0)
    boundary_violations = np.sum(result['G'][10:] > 0)
    
    print(f"   Test solution (all at center):")
    print(f"   Space constraint violations: {space_violations}/10")
    print(f"   Boundary constraint violations: {boundary_violations}/10")
    print(f"   ✅ Constraint handling working correctly")


def main():
    """Run all examples"""
    print("🚀 DEAP Optimization Manager Usage Examples")
    print("Demonstrating flexible multi-algorithm wind farm layout optimization")
    print("with consistent normalization, initialization, and constraint handling")
    
    examples = [
        example_1_easy_algorithm_switching,
        example_2_flexible_configuration,
        example_3_operator_flexibility,
        example_4_multi_algorithm_comparison,
        example_5_hybrid_optimization,
        example_6_consistency_validation
    ]
    
    for i, example in enumerate(examples, 1):
        try:
            example()
        except Exception as e:
            print(f"\n❌ Example {i} failed: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n" + "="*60)
    print("✅ All examples completed!")
    print("The DEAP Optimization Manager provides:")
    print("• Easy algorithm switching with consistent interface")
    print("• Flexible configuration through params.py")
    print("• Consistent normalization and constraint handling")
    print("• Multi-algorithm comparison capabilities")
    print("• Hybrid optimization sequences")
    print("• Validation of consistency across all methods")
    print("="*60)


if __name__ == "__main__":
    main()