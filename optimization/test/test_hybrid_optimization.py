#!/usr/bin/env python3
"""
Hybrid Optimization Script Test Suite
Comprehensive tests for opt-pymoo-windrose-freq-ts-external_hybrid.py
"""

import unittest
import numpy as np
import pandas as pd
import os
import sys
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import parameters
import params as pr


class TestHybridOptimizationComponents(unittest.TestCase):
    """Test individual components of the hybrid optimization script"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        
        # Create test data
        self.test_layout = pd.DataFrame({
            'x': [531000, 532000, 533000, 534000],
            'y': [5851000, 5851000, 5851000, 5851000],
            'external': [False, False, True, True],
            'turb': ['zeevonk east', 'zeevonk east', 'Zeevonk East', 'external_type']
        })
        
        self.test_wind_data = pd.DataFrame({
            'ws': [6.0, 8.0, 10.0, 12.0] * 4,
            'wd': [0, 90, 180, 270] * 4,
            'freq_val': [0.0625] * 16  # Normalized frequency
        })
        
        # Create test input files
        self.create_test_input_files()
    
    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir)
    
    def create_test_input_files(self):
        """Create minimal test input files"""
        # Initial layout file
        layout_file = os.path.join(self.temp_dir, "Initial.layout.csv")
        internal_layout = self.test_layout[self.test_layout['external'] == False]
        internal_layout.to_csv(layout_file, index=False)
        
        # Wind rose file (time series format)
        wind_file = os.path.join(self.temp_dir, "timeseries.txt")
        # Convert to time series format expected by script
        ts_data = []
        for _, row in self.test_wind_data.iterrows():
            # Simulate multiple time steps for each wind condition
            for i in range(int(row['freq_val'] * 1000)):  # Scale up frequency
                ts_data.extend([row['ws'], row['wd']])
        
        with open(wind_file, 'w') as f:
            for i in range(0, len(ts_data), 2):
                if i+1 < len(ts_data):
                    f.write(f"{ts_data[i]:.1f} {ts_data[i+1]:.1f}\n")
        
        # Boundaries file
        boundaries_file = os.path.join(self.temp_dir, "Boundaries.csv")
        boundaries = pd.DataFrame({
            'x': [530000, 535000, 535000, 530000, 530000],
            'y': [5850000, 5850000, 5855000, 5855000, 5850000]
        })
        boundaries.to_csv(boundaries_file, index=False)
        
        # FLORIS config file (minimal YAML)
        config_file = os.path.join(self.temp_dir, "config.yaml")
        config_content = """
logging:
  console:
    enable: true
    level: WARNING
solver:
  type: turbine_grid
  turbine_grid:
    grid_resolution: 5
    upwind_buffer: 3
    downwind_buffer: 3
wake:
  model_strings:
    combination_model: sosfs
    deflection_model: gauss
    turbulence_model: crespo_hernandez
    velocity_model: gauss
floris_version: v3.1.1
"""
        with open(config_file, 'w') as f:
            f.write(config_content)
        
        # Update params for testing
        pr.inputLayoutFile = layout_file
        pr.windRoseFile = wind_file
        pr.boundariesFile = boundaries_file
        pr.FLORIS_CONFIG = config_file
    
    def test_normalize_turbine_types(self):
        """Test turbine type normalization function"""
        try:
            # Try to import the function
            spec = __import__('importlib.util').util.spec_from_file_location(
                "hybrid_script", 
                "/project/bii69/ps/ps/optimization/opt-pymoo-windrose-freq-ts-external_hybrid.py"
            )
            if spec and spec.loader:
                hybrid_module = __import__('importlib.util').util.module_from_spec(spec)
                spec.loader.exec_module(hybrid_module)
                normalize_func = hybrid_module.normalize_turbine_types
                
                # Test normalization
                input_types = np.array(['Zeevonk East', 'Zeevonk West', 'unknown_type'])
                expected = np.array(['zeevonk east', 'zeevonk west', 'unknown_type'])
                
                result = normalize_func(input_types)
                np.testing.assert_array_equal(result, expected)
            else:
                self.skipTest("Could not import hybrid script normalize function")
        except Exception as e:
            self.skipTest(f"Could not test normalize function: {e}")
    
    def test_parameter_validation(self):
        """Test that hybrid script uses parameters correctly"""
        # Test that all required parameters exist
        required_params = [
            'use_external_turbines',
            'max_distance_km', 
            'plot_iterations',
            'iteration_interval',
            'plot_farm_analysis',
            'MAXGEN',
            'PopSize'
        ]
        
        for param in required_params:
            with self.subTest(parameter=param):
                self.assertTrue(hasattr(pr, param), f"Parameter {param} missing from params.py")
    
    @patch('os.path.exists')
    def test_file_existence_checks(self, mock_exists):
        """Test file existence validation"""
        mock_exists.return_value = True
        
        # Test required files
        required_files = [
            pr.inputLayoutFile,
            pr.windRoseFile, 
            pr.boundariesFile,
            pr.FLORIS_CONFIG
        ]
        
        for file_path in required_files:
            with self.subTest(file=file_path):
                # File should be accessible
                self.assertIsInstance(file_path, str)
                self.assertTrue(len(file_path) > 0)


class TestHybridWorkflowSteps(unittest.TestCase):
    """Test the 4-step hybrid optimization workflow"""
    
    def setUp(self):
        """Set up mock workflow components"""
        self.mock_floris = Mock()
        self.mock_floris.floris.farm.hub_heights = [90.0, 90.0, 90.0]
        self.mock_floris.get_farm_AEP = Mock(return_value=100000.0)
        self.mock_floris.get_turbine_powers = Mock(return_value=np.array([[1000.0, 1000.0, 1000.0]]))
        
        self.test_layout = pd.DataFrame({
            'x': [531000, 532000, 533000],
            'y': [5851000, 5851000, 5851000],
            'external': [False, False, False],
            'turb': ['zeevonk east'] * 3
        })
    
    @patch('core.tools.FlorisInterface')
    def test_step1_initial_simulation(self, mock_floris_class):
        """Test Step 1: Initial FLORIS simulation"""
        mock_floris_class.return_value = self.mock_floris
        
        # This would test the actual step 1 function if imported
        # For now, test the concept
        result_aep = self.mock_floris.get_farm_AEP()
        self.assertEqual(result_aep, 100000.0)
        
        # Verify FLORIS was called with correct parameters
        self.mock_floris.get_farm_AEP.assert_called()
    
    @patch('core.tools.FlorisInterface')
    def test_step2_optimization_setup(self, mock_floris_class):
        """Test Step 2: Optimization algorithm setup"""
        mock_floris_class.return_value = self.mock_floris
        
        # Test that optimization parameters are properly configured
        self.assertIsInstance(pr.MAXGEN, int)
        self.assertIsInstance(pr.PopSize, int)
        self.assertGreater(pr.MAXGEN, 0)
        self.assertGreater(pr.PopSize, 0)
        
        # Test population size is multiple of 4 (NSGA-II requirement)
        self.assertEqual(pr.PopSize % 4, 0)
    
    def test_step3_detailed_simulation(self):
        """Test Step 3: Detailed simulation logic"""
        # Test that detailed simulation preserves layout structure
        optimized_layout = self.test_layout.copy()
        optimized_layout['x'] += np.random.uniform(-100, 100, len(optimized_layout))
        
        # Verify layout maintains required columns
        required_columns = ['x', 'y', 'external', 'turb']
        for col in required_columns:
            self.assertIn(col, optimized_layout.columns)
        
        # Verify external flag preservation
        original_externals = self.test_layout['external'].sum()
        new_externals = optimized_layout['external'].sum()
        self.assertEqual(original_externals, new_externals)
    
    def test_step4_final_evaluation(self):
        """Test Step 4: Final evaluation and reporting"""
        # Test metrics calculation
        initial_aep = 95000.0
        final_aep = 105000.0
        
        improvement = ((final_aep - initial_aep) / initial_aep) * 100
        self.assertAlmostEqual(improvement, 10.526, places=3)
        
        # Test that improvement is positive
        self.assertGreater(improvement, 0)


class TestHybridErrorHandling(unittest.TestCase):
    """Test error handling in hybrid optimization"""
    
    def setUp(self):
        """Set up error test scenarios"""
        self.invalid_layout = pd.DataFrame({'x': [1, 2], 'y': [3, 4]})  # Missing required columns
        self.empty_layout = pd.DataFrame()
    
    def test_invalid_layout_handling(self):
        """Test handling of invalid layout data"""
        # Test missing columns
        required_columns = ['x', 'y', 'external', 'turb']
        missing_columns = []
        
        for col in required_columns:
            if col not in self.invalid_layout.columns:
                missing_columns.append(col)
        
        self.assertGreater(len(missing_columns), 0)
        
        # In actual implementation, this should raise appropriate error
        with self.assertRaises((KeyError, AttributeError)):
            # Simulate accessing missing column
            _ = self.invalid_layout['external']
    
    def test_empty_layout_handling(self):
        """Test handling of empty layouts"""
        self.assertEqual(len(self.empty_layout), 0)
        
        # Empty layout should be handled gracefully
        if len(self.empty_layout) == 0:
            # Should skip optimization or handle appropriately
            self.assertTrue(True)  # Placeholder for actual error handling test
    
    def test_floris_initialization_errors(self):
        """Test FLORIS initialization error handling"""
        # Test invalid turbine types
        invalid_types = ['nonexistent_turbine_type']
        
        # This should be caught and handled in actual implementation
        self.assertIsInstance(invalid_types, list)
    
    def test_optimization_convergence_handling(self):
        """Test handling of optimization convergence issues"""
        # Test very strict convergence criteria
        very_strict_ftol = 1e-12
        self.assertLess(very_strict_ftol, pr.ftol)
        
        # Test very loose convergence criteria
        very_loose_ftol = 1e-2
        self.assertGreater(very_loose_ftol, pr.ftol)


class TestHybridIntegration(unittest.TestCase):
    """Test integration between hybrid script components"""
    
    def test_external_turbine_integration(self):
        """Test external turbine loading and integration"""
        # Test external turbine flag consistency
        test_layout = pd.DataFrame({
            'x': [1, 2, 3, 4],
            'y': [1, 2, 3, 4],
            'external': [False, False, True, True],
            'turb': ['internal'] * 2 + ['external'] * 2
        })
        
        internal_count = (test_layout['external'] == False).sum()
        external_count = (test_layout['external'] == True).sum()
        
        self.assertEqual(internal_count, 2)
        self.assertEqual(external_count, 2)
        self.assertEqual(len(test_layout), internal_count + external_count)
    
    def test_parameter_consistency(self):
        """Test parameter consistency across workflow"""
        # Test that parameters are consistent
        self.assertEqual(pr.nGenerations, pr.MAXGEN)  # Alias consistency
        
        # Test distance parameter consistency
        if hasattr(pr, 'max_distance_km'):
            self.assertIsInstance(pr.max_distance_km, (int, float))
            self.assertGreater(pr.max_distance_km, 0)
    
    def test_plotting_integration(self):
        """Test plotting function integration"""
        # Test plotting parameters
        if hasattr(pr, 'plot_iterations'):
            self.assertIsInstance(pr.plot_iterations, bool)
        
        if hasattr(pr, 'iteration_interval'):
            self.assertIsInstance(pr.iteration_interval, int)
            self.assertGreater(pr.iteration_interval, 0)
    
    def test_enhanced_filtering_integration(self):
        """Test enhanced farm filtering integration"""
        # Test enhanced filtering parameters
        filtering_params = [
            'use_enhanced_filtering',
            'farm_sector_width',
            'farm_grouping'
        ]
        
        for param in filtering_params:
            if hasattr(pr, param):
                with self.subTest(parameter=param):
                    value = getattr(pr, param)
                    self.assertIsNotNone(value)


class TestHybridPerformance(unittest.TestCase):
    """Test performance-related aspects of hybrid optimization"""
    
    def test_parallel_configuration(self):
        """Test parallel processing configuration"""
        # Test worker count configuration
        if hasattr(pr, 'n_workers'):
            self.assertIsInstance(pr.n_workers, int)
            self.assertGreater(pr.n_workers, 0)
            self.assertLessEqual(pr.n_workers, 64)  # Reasonable maximum
        
        # Test timeout configuration
        if hasattr(pr, 'timeout_per_eval'):
            self.assertIsInstance(pr.timeout_per_eval, int)
            self.assertGreater(pr.timeout_per_eval, 0)
    
    def test_optimization_scaling(self):
        """Test optimization parameter scaling"""
        # Test population size scaling
        expected_min_pop = 8  # Minimum for NSGA-II
        self.assertGreaterEqual(pr.PopSize, expected_min_pop)
        
        # Test generation scaling
        expected_min_gen = 1
        self.assertGreaterEqual(pr.MAXGEN, expected_min_gen)
        
        # Test crossover/mutation parameters in valid ranges
        self.assertTrue(0.0 <= pr.pCross_real <= 1.0)
        self.assertGreater(pr.eta_c, 0.0)
        self.assertGreater(pr.eta_m, 0.0)
    
    def test_memory_efficiency(self):
        """Test memory efficiency considerations"""
        # Test that large data structures are handled appropriately
        large_layout = pd.DataFrame({
            'x': np.random.uniform(530000, 540000, 1000),
            'y': np.random.uniform(5850000, 5860000, 1000),
            'external': np.random.choice([True, False], 1000),
            'turb': ['test_type'] * 1000
        })
        
        # Should be able to handle reasonably large layouts
        self.assertEqual(len(large_layout), 1000)
        self.assertIn('x', large_layout.columns)
        
        # Memory footprint should be reasonable
        memory_size = large_layout.memory_usage(deep=True).sum()
        self.assertLess(memory_size, 10**7)  # Less than 10MB for 1000 turbines


def create_hybrid_test_suite():
    """Create comprehensive test suite for hybrid optimization"""
    suite = unittest.TestSuite()
    
    test_classes = [
        TestHybridOptimizationComponents,
        TestHybridWorkflowSteps,
        TestHybridErrorHandling,
        TestHybridIntegration,
        TestHybridPerformance
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    return suite


if __name__ == '__main__':
    print("="*80)
    print("Running Hybrid Optimization Test Suite")
    print("="*80)
    
    # Run tests
    suite = create_hybrid_test_suite()
    runner = unittest.TextTestRunner(verbosity=2, buffer=True)
    result = runner.run(suite)
    
    # Print results
    print("\n" + "="*80)
    print("HYBRID OPTIMIZATION TEST RESULTS")
    print("="*80)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print(f"\nFAILED TESTS ({len(result.failures)}):")
        for test, trace in result.failures:
            print(f"  ❌ {test}")
    
    if result.errors:
        print(f"\nERROR TESTS ({len(result.errors)}):")
        for test, trace in result.errors:
            print(f"  ⚠️  {test}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\nSuccess Rate: {success_rate:.1f}%")
    
    if result.wasSuccessful():
        print("🎉 All hybrid optimization tests passed!")
    else:
        print("⚠️  Some tests need attention.")
    
    print("="*80)