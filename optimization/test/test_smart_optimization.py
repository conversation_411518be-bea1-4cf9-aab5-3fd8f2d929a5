#!/usr/bin/env python3
"""
Smart Optimization Script Test Suite
Comprehensive tests for opt-pymoo-windrose-freq-ts-external_smart.py
"""

import unittest
import numpy as np
import pandas as pd
import os
import sys
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add parent directory to path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import parameters
import params as pr


class TestSmartExternalLayoutManager(unittest.TestCase):
    """Test SmartExternalLayoutManager functionality"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.internal_layout = pd.DataFrame({
            'x': [531000, 532000, 533000],
            'y': [5851000, 5851000, 5851000],
            'external': [False, False, False],
            'turb': ['Internal_Type'] * 3
        })
        
        # Create external turbines at various distances
        distances_km = [5, 10, 15, 25, 30]  # km from internal centroid
        self.external_layout = pd.DataFrame()
        
        for i, dist_km in enumerate(distances_km):
            # Place turbines at different distances (North direction)
            external_turbines = pd.DataFrame({
                'x': [532000] * 2,  # Same x as internal centroid
                'y': [5851000 + dist_km * 1000] * 2,  # Distance in meters north
                'external': [True] * 2,
                'turb': [f'External_Farm_{dist_km}km'] * 2
            })
            self.external_layout = pd.concat([self.external_layout, external_turbines], ignore_index=True)
    
    def test_distance_filtering(self):
        """Test distance-based filtering of external turbines"""
        try:
            # Try to import SmartExternalLayoutManager
            from test_enhanced_farm_filtering import SmartExternalLayoutManager
            
            # Test with 20km threshold
            manager = SmartExternalLayoutManager(
                self.internal_layout, 
                self.external_layout,
                max_distance_km=20
            )
            
            # Should include turbines within 20km (5, 10, 15km) but exclude 25, 30km
            expected_count = 6  # 2 turbines each at 5, 10, 15km
            self.assertEqual(len(manager.external_layout), expected_count)
            
            # Test with 10km threshold
            manager_10km = SmartExternalLayoutManager(
                self.internal_layout,
                self.external_layout, 
                max_distance_km=10
            )
            
            # Should include only 5km and 10km turbines
            expected_count_10km = 4  # 2 turbines each at 5, 10km
            self.assertEqual(len(manager_10km.external_layout), expected_count_10km)
            
        except ImportError:
            self.skipTest("SmartExternalLayoutManager not available for testing")
    
    def test_centroid_calculation(self):
        """Test internal layout centroid calculation"""
        try:
            from test_enhanced_farm_filtering import SmartExternalLayoutManager
            
            manager = SmartExternalLayoutManager(
                self.internal_layout,
                pd.DataFrame(),  # Empty external
                max_distance_km=20
            )
            
            # Check centroid calculation
            expected_x = np.mean(self.internal_layout['x'])
            expected_y = np.mean(self.internal_layout['y'])
            
            self.assertAlmostEqual(manager.internal_centroid[0], expected_x, places=1)
            self.assertAlmostEqual(manager.internal_centroid[1], expected_y, places=1)
            
        except ImportError:
            self.skipTest("SmartExternalLayoutManager not available for testing")
    
    def test_empty_external_handling(self):
        """Test handling of empty external layout"""
        try:
            from test_enhanced_farm_filtering import SmartExternalLayoutManager
            
            empty_external = pd.DataFrame()
            manager = SmartExternalLayoutManager(
                self.internal_layout,
                empty_external,
                max_distance_km=20
            )
            
            self.assertEqual(len(manager.external_layout), 0)
            self.assertEqual(len(manager.internal_layout), 3)
            
        except ImportError:
            self.skipTest("SmartExternalLayoutManager not available for testing")


class TestSmartOptimizationWorkflow(unittest.TestCase):
    """Test smart optimization workflow components"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        
        # Mock FLORIS interface
        self.mock_floris = Mock()
        self.mock_floris.floris.farm.hub_heights = [90.0] * 5
        self.mock_floris.get_farm_AEP = Mock(return_value=[150000.0])
        self.mock_floris.get_turbine_powers = Mock(return_value=np.array([[1500.0] * 5]))
        
        # Test wind data
        self.wind_data = pd.DataFrame({
            'ws': [8.0] * 8,
            'wd': [0, 45, 90, 135, 180, 225, 270, 315],
            'freq_val': [0.125] * 8  # Equal frequency
        })
    
    def tearDown(self):
        """Clean up"""
        shutil.rmtree(self.temp_dir)
    
    def test_hub_height_calculation_fix(self):
        """Test that hub height is properly calculated, not hardcoded"""
        # Test the fix for the hardcoded ref_ht = 90 issue
        
        # Mock a FLORIS interface with different hub heights
        mock_fi = Mock()
        mock_fi.floris.farm.hub_heights = [85.0, 90.0, 95.0]  # Different heights
        
        # Test single turbine type scenario (the fixed code path)
        internal_layout = pd.DataFrame({
            'x': [531000], 'y': [5851000], 
            'external': [False], 'turb': ['test_type']
        })
        
        temp_layout = pd.DataFrame({
            'x': [531000, 532000, 533000],
            'y': [5851000, 5852000, 5853000],
            'external': [False, True, True],
            'turb': ['test_type', 'external1', 'external2']
        })
        
        # Find internal turbine index (should be 0)
        internal_turb_idx = None
        for i, row in internal_layout.iterrows():
            turb_idx = np.where((temp_layout['x'] == row['x']) & (temp_layout['y'] == row['y']))[0]
            if len(turb_idx) > 0:
                internal_turb_idx = turb_idx[0]
                break
        
        self.assertIsNotNone(internal_turb_idx)
        self.assertEqual(internal_turb_idx, 0)
        
        # Hub height should come from FLORIS, not be hardcoded
        expected_hub_height = mock_fi.floris.farm.hub_heights[internal_turb_idx]
        self.assertEqual(expected_hub_height, 85.0)  # Not the hardcoded 90.0
    
    def test_wind_direction_filtering(self):
        """Test wind direction-based external turbine filtering"""
        # Create test layout with external farms in different directions
        internal = pd.DataFrame({
            'x': [532000], 'y': [5852000],
            'external': [False], 'turb': ['internal']
        })
        
        # External farms: North, East, South, West
        external = pd.DataFrame({
            'x': [532000, 536000, 532000, 528000],
            'y': [5856000, 5852000, 5848000, 5852000],
            'external': [True] * 4,
            'turb': ['North_Farm', 'East_Farm', 'South_Farm', 'West_Farm']
        })
        
        try:
            from test_enhanced_farm_filtering import EnhancedDirectionalFarmManager
            
            manager = EnhancedDirectionalFarmManager(
                internal, external,
                sector_width=90
            )
            
            # Test filtering for each cardinal direction
            test_cases = [
                (0, ['North_Farm']),    # Wind from North
                (90, ['East_Farm']),    # Wind from East
                (180, ['South_Farm']),  # Wind from South
                (270, ['West_Farm'])    # Wind from West
            ]
            
            for wind_dir, expected_farms in test_cases:
                with self.subTest(wind_direction=wind_dir):
                    relevant_farms = manager.get_relevant_farms_for_direction(wind_dir)
                    self.assertEqual(set(relevant_farms), set(expected_farms))
                    
        except ImportError:
            self.skipTest("EnhancedDirectionalFarmManager not available")
    
    @patch('core.tools.FlorisInterface')
    def test_aep_calculation_with_filtering(self, mock_floris_class):
        """Test AEP calculation with directional filtering"""
        mock_floris_class.return_value = self.mock_floris
        
        # Test that AEP calculation accounts for different wind directions
        wind_directions = [0, 90, 180, 270]
        frequencies = [0.25, 0.25, 0.25, 0.25]
        
        total_aep = 0
        for wd, freq in zip(wind_directions, frequencies):
            # Each direction contributes proportionally
            direction_aep = self.mock_floris.get_farm_AEP.return_value[0] * freq
            total_aep += direction_aep
        
        expected_total = 150000.0  # All directions contribute equally
        self.assertEqual(total_aep, expected_total)
    
    def test_optimization_parameters_from_params(self):
        """Test that optimization uses parameters from params.py"""
        # Verify key parameters are read from params.py
        optimization_params = {
            'max_distance_km': pr.max_distance_km,
            'use_external_turbines': pr.use_external_turbines,
            'farm_sector_width': getattr(pr, 'farm_sector_width', 90),
            'use_enhanced_filtering': getattr(pr, 'use_enhanced_filtering', False)
        }
        
        # Test parameter types and ranges
        self.assertIsInstance(optimization_params['max_distance_km'], (int, float))
        self.assertGreater(optimization_params['max_distance_km'], 0)
        
        self.assertIsInstance(optimization_params['use_external_turbines'], bool)
        
        self.assertIsInstance(optimization_params['farm_sector_width'], (int, float))
        self.assertTrue(30 <= optimization_params['farm_sector_width'] <= 180)


class TestSmartOptimizationIntegration(unittest.TestCase):
    """Test integration features of smart optimization"""
    
    def test_enhanced_plotting_integration(self):
        """Test enhanced plotting feature integration"""
        # Test plotting parameters
        plotting_params = [
            'plot_farm_analysis',
            'analysis_directions',
            'farm_grouping'
        ]
        
        for param in plotting_params:
            if hasattr(pr, param):
                with self.subTest(parameter=param):
                    value = getattr(pr, param)
                    self.assertIsNotNone(value)
    
    def test_farm_manager_integration(self):
        """Test enhanced farm manager integration"""
        try:
            from utilities import create_enhanced_farm_manager
            
            internal = pd.DataFrame({
                'x': [531000, 532000],
                'y': [5851000, 5851000],
                'external': [False, False],
                'turb': ['internal'] * 2
            })
            
            external = pd.DataFrame({
                'x': [533000, 534000],
                'y': [5853000, 5853000], 
                'external': [True, True],
                'turb': ['external_farm'] * 2
            })
            
            manager = create_enhanced_farm_manager(
                internal, external,
                max_distance_km=pr.max_distance_km,
                sector_width=getattr(pr, 'farm_sector_width', 90)
            )
            
            self.assertIsNotNone(manager)
            self.assertTrue(hasattr(manager, 'get_layout_with_farm_filtering'))
            
        except ImportError:
            self.skipTest("Enhanced farm manager utilities not available")
    
    def test_wind_data_processing(self):
        """Test wind data processing for smart optimization"""
        # Test time series to frequency conversion
        try:
            from utilities import ts_to_freq_df
            
            # Create sample time series data
            ts_data = pd.DataFrame({
                'ws': [6, 8, 10, 8, 6, 10],
                'wd': [0, 90, 180, 270, 0, 180],
                'other_col': [1, 2, 3, 4, 5, 6]  # Should be ignored
            })
            
            freq_data = ts_to_freq_df(ts_data)
            
            # Check output structure
            self.assertIn('ws', freq_data.columns)
            self.assertIn('wd', freq_data.columns)
            self.assertIn('freq_val', freq_data.columns)
            
            # Check frequency normalization
            total_freq = freq_data['freq_val'].sum()
            self.assertAlmostEqual(total_freq, 1.0, places=6)
            
        except ImportError:
            self.skipTest("Wind data utilities not available")


class TestSmartOptimizationErrorHandling(unittest.TestCase):
    """Test error handling in smart optimization"""
    
    def test_invalid_distance_parameters(self):
        """Test handling of invalid distance parameters"""
        # Test negative distance
        with self.assertRaises((ValueError, AssertionError)):
            # This should be caught by parameter validation
            if hasattr(pr, 'max_distance_km'):
                if pr.max_distance_km < 0:
                    raise ValueError("Distance cannot be negative")
        
        # Test zero distance
        if hasattr(pr, 'max_distance_km'):
            self.assertGreater(pr.max_distance_km, 0)
    
    def test_invalid_sector_width(self):
        """Test handling of invalid sector width parameters"""
        if hasattr(pr, 'farm_sector_width'):
            sector_width = pr.farm_sector_width
            
            # Should be within reasonable range
            self.assertTrue(0 < sector_width <= 360)
            
            # Common values should be in practical range
            self.assertTrue(30 <= sector_width <= 180)
    
    def test_missing_external_turbines(self):
        """Test handling when no external turbines are available"""
        internal_only = pd.DataFrame({
            'x': [531000, 532000],
            'y': [5851000, 5851000],
            'external': [False, False],
            'turb': ['internal'] * 2
        })
        
        empty_external = pd.DataFrame()
        
        try:
            from test_enhanced_farm_filtering import EnhancedDirectionalFarmManager
            
            manager = EnhancedDirectionalFarmManager(
                internal_only, empty_external
            )
            
            # Should handle gracefully
            self.assertFalse(manager.has_external)
            
            # Should return internal-only layout for any wind direction
            layout = manager.get_layout_with_farm_filtering(0)
            self.assertEqual(len(layout), 2)
            self.assertFalse(layout['external'].any())
            
        except ImportError:
            self.skipTest("Enhanced farm manager not available")
    
    def test_invalid_wind_directions(self):
        """Test handling of invalid wind directions"""
        try:
            from test_enhanced_farm_filtering import EnhancedDirectionalFarmManager
            
            internal = pd.DataFrame({
                'x': [531000], 'y': [5851000],
                'external': [False], 'turb': ['internal']
            })
            
            external = pd.DataFrame({
                'x': [532000], 'y': [5852000],
                'external': [True], 'turb': ['external']
            })
            
            manager = EnhancedDirectionalFarmManager(internal, external)
            
            # Test extreme wind directions
            extreme_directions = [-180, -90, 360, 450, 720]
            
            for wd in extreme_directions:
                with self.subTest(wind_direction=wd):
                    # Should handle without crashing
                    farms = manager.get_relevant_farms_for_direction(wd)
                    self.assertIsInstance(farms, list)
                    
        except ImportError:
            self.skipTest("Enhanced farm manager not available")


class TestSmartOptimizationPerformance(unittest.TestCase):
    """Test performance aspects of smart optimization"""
    
    def test_filtering_efficiency(self):
        """Test that directional filtering reduces computational load"""
        # Create large external layout
        n_external = 100
        external_layout = pd.DataFrame({
            'x': np.random.uniform(520000, 540000, n_external),
            'y': np.random.uniform(5840000, 5860000, n_external),
            'external': [True] * n_external,
            'turb': [f'External_{i}' for i in range(n_external)]
        })
        
        internal_layout = pd.DataFrame({
            'x': [532000], 'y': [5852000],
            'external': [False], 'turb': ['internal']
        })
        
        try:
            from test_enhanced_farm_filtering import EnhancedDirectionalFarmManager
            
            manager = EnhancedDirectionalFarmManager(
                internal_layout, external_layout,
                max_distance_km=20, sector_width=90
            )
            
            # Test that filtering reduces turbine count for each direction
            for wind_dir in [0, 90, 180, 270]:
                filtered_layout = manager.get_layout_with_farm_filtering(wind_dir)
                external_count = len(filtered_layout[filtered_layout['external'] == True])
                
                # Should be less than total external turbines
                self.assertLess(external_count, len(manager.external_layout))
                
        except ImportError:
            self.skipTest("Enhanced farm manager not available")
    
    def test_memory_usage_optimization(self):
        """Test memory usage optimization features"""
        # Test that large datasets are handled efficiently
        large_layout = pd.DataFrame({
            'x': np.random.uniform(520000, 540000, 500),
            'y': np.random.uniform(5840000, 5860000, 500),
            'external': np.random.choice([True, False], 500),
            'turb': [f'Turbine_{i}' for i in range(500)]
        })
        
        # Should handle reasonably large layouts
        self.assertEqual(len(large_layout), 500)
        
        # Memory footprint check
        memory_usage = large_layout.memory_usage(deep=True).sum()
        self.assertLess(memory_usage, 10**6)  # Less than 1MB for 500 turbines
    
    def test_caching_efficiency(self):
        """Test caching and reuse efficiency"""
        try:
            from test_enhanced_farm_filtering import EnhancedDirectionalFarmManager
            
            internal = pd.DataFrame({
                'x': [532000], 'y': [5852000],
                'external': [False], 'turb': ['internal']
            })
            
            external = pd.DataFrame({
                'x': [533000, 534000, 531000, 530000],
                'y': [5854000, 5852000, 5850000, 5852000],
                'external': [True] * 4,
                'turb': ['N_Farm', 'E_Farm', 'S_Farm', 'W_Farm']
            })
            
            manager = EnhancedDirectionalFarmManager(internal, external)
            
            # Test cache statistics
            cache_stats = manager.get_cache_stats()
            self.assertIsInstance(cache_stats, str)
            self.assertIn('Smart filtering cache statistics', cache_stats)
            
            # Test filtering statistics
            filtering_stats = manager.get_filtering_statistics()
            self.assertIsInstance(filtering_stats, dict)
            self.assertIn('n_external_total', filtering_stats)
            self.assertIn('n_farms', filtering_stats)
            
        except ImportError:
            self.skipTest("Enhanced farm manager not available")


def create_smart_test_suite():
    """Create comprehensive test suite for smart optimization"""
    suite = unittest.TestSuite()
    
    test_classes = [
        TestSmartExternalLayoutManager,
        TestSmartOptimizationWorkflow,
        TestSmartOptimizationIntegration,
        TestSmartOptimizationErrorHandling,
        TestSmartOptimizationPerformance
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    return suite


if __name__ == '__main__':
    print("="*80)
    print("Running Smart Optimization Test Suite")
    print("="*80)
    
    # Run tests
    suite = create_smart_test_suite()
    runner = unittest.TextTestRunner(verbosity=2, buffer=True)
    result = runner.run(suite)
    
    # Print results
    print("\n" + "="*80)
    print("SMART OPTIMIZATION TEST RESULTS")
    print("="*80)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    
    if result.failures:
        print(f"\nFAILED TESTS ({len(result.failures)}):")
        for test, trace in result.failures:
            print(f"  ❌ {test}")
    
    if result.errors:
        print(f"\nERROR TESTS ({len(result.errors)}):")
        for test, trace in result.errors:
            print(f"  ⚠️  {test}")
    
    success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
    print(f"\nSuccess Rate: {success_rate:.1f}%")
    
    if result.wasSuccessful():
        print("🎉 All smart optimization tests passed!")
    else:
        print("⚠️  Some tests need attention.")
    
    print("="*80)