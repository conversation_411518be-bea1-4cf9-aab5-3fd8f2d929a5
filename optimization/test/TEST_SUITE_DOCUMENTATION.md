# Wind Farm Optimization Test Suite Documentation

## Overview

This comprehensive test suite provides thorough testing for both wind farm optimization scripts and utilities. The test suite consists of multiple specialized test modules that validate different aspects of the optimization system.

## Test Suite Structure

### 📁 Test Files

1. **`test_optimization_scripts.py`** - General utilities and optimization components
2. **`test_hybrid_optimization.py`** - Hybrid optimization script testing  
3. **`test_smart_optimization.py`** - Smart optimization script testing
4. **`run_all_tests.py`** - Comprehensive test runner
5. **`TEST_SUITE_DOCUMENTATION.md`** - This documentation

## 🧪 Test Coverage

### General Utilities (`test_optimization_scripts.py`)
- **TestUtilities**: Core utility functions
  - `sanitize_name()` - Name sanitization
  - `memory_footprint()` - Memory usage calculation
  - `write_log()` - Logging functionality
  - `ts_to_freq_df()` - Time series conversion
  - `load_boundaries()` - Boundary file loading
  - `plot_layout_beforeOptim()` - Layout plotting
  - `create_enhanced_farm_manager()` - Farm manager creation
  - Enhanced plotting functions

- **TestEnhancedDirectionalFarmManager**: Advanced farm filtering
  - Farm detection and grouping
  - Directional filtering logic
  - Layout filtering with wind directions
  - Statistics generation

- **TestOptimizationParameters**: Parameter validation
  - Parameter existence checks
  - Type validation
  - Range validation
  - Boolean parameter checks

- **TestOptimizationWorkflow**: Workflow components
  - FLORIS path setup
  - Turbine type normalization
  - Layout data validation
  - Wind data validation

- **TestErrorHandling**: Edge cases and error conditions
  - Empty layout handling
  - Invalid wind directions
  - Missing files
  - Malformed data

### Hybrid Optimization (`test_hybrid_optimization.py`)
- **TestHybridOptimizationComponents**: Individual components
  - Turbine type normalization
  - Parameter validation
  - File existence checks

- **TestHybridWorkflowSteps**: 4-step optimization workflow
  - Step 1: Initial FLORIS simulation
  - Step 2: Optimization setup
  - Step 3: Detailed simulation
  - Step 4: Final evaluation

- **TestHybridErrorHandling**: Error scenarios
  - Invalid layout handling
  - Empty layout handling
  - FLORIS initialization errors
  - Optimization convergence issues

- **TestHybridIntegration**: Integration testing
  - External turbine integration
  - Parameter consistency
  - Plotting integration
  - Enhanced filtering integration

- **TestHybridPerformance**: Performance aspects
  - Parallel configuration
  - Optimization scaling
  - Memory efficiency

### Smart Optimization (`test_smart_optimization.py`)
- **TestSmartExternalLayoutManager**: Smart layout management
  - Distance-based filtering
  - Centroid calculation
  - Empty external handling

- **TestSmartOptimizationWorkflow**: Smart workflow
  - Hub height calculation fix (addresses the hardcoded issue)
  - Wind direction filtering
  - AEP calculation with filtering
  - Parameter usage from params.py

- **TestSmartOptimizationIntegration**: Integration features
  - Enhanced plotting integration
  - Farm manager integration
  - Wind data processing

- **TestSmartOptimizationErrorHandling**: Error handling
  - Invalid distance parameters
  - Invalid sector width
  - Missing external turbines
  - Invalid wind directions

- **TestSmartOptimizationPerformance**: Performance testing
  - Filtering efficiency
  - Memory usage optimization
  - Caching efficiency

## 🚀 Running Tests

### Run All Tests
```bash
# Run complete test suite
python3 run_all_tests.py
```

### Run Specific Test Modules
```bash
# Run only utilities tests
python3 test_optimization_scripts.py

# Run only hybrid optimization tests
python3 test_hybrid_optimization.py

# Run only smart optimization tests  
python3 test_smart_optimization.py
```

### Run Specific Test Patterns
```bash
# Run tests matching pattern
python3 run_all_tests.py hybrid

# Run tests matching pattern
python3 run_all_tests.py smart
```

### Run Individual Test Classes
```bash
# Run specific test class
python3 -m unittest test_optimization_scripts.TestUtilities

# Run specific test method
python3 -m unittest test_optimization_scripts.TestUtilities.test_sanitize_name
```

## 📊 Test Output

### Successful Run Example
```
🧪 Wind Farm Optimization - Comprehensive Test Suite
================================================================================
Testing all optimization scripts and utilities...

============================================================
Running General Utilities
============================================================
....................
----------------------------------------------------------------------
Ran 20 tests in 2.3s

OK

============================================================
Running Hybrid Optimization  
============================================================
....................
----------------------------------------------------------------------
Ran 20 tests in 1.8s

OK

============================================================
Running Smart Optimization
============================================================
....................
----------------------------------------------------------------------
Ran 20 tests in 2.1s

OK

================================================================================
📊 COMPREHENSIVE TEST RESULTS SUMMARY
================================================================================
📈 Overall Statistics:
   Total Tests: 60
   Total Failures: 0
   Total Errors: 0
   Overall Success Rate: 100.0%
   Total Duration: 6.2 seconds

📋 Results by Test Suite:
   ✅ PASS General Utilities             20 tests  100.0%   2.3s
   ✅ PASS Hybrid Optimization           20 tests  100.0%   1.8s
   ✅ PASS Smart Optimization            20 tests  100.0%   2.1s

================================================================================
🎉 ALL TESTS PASSED! Your optimization system is working correctly.
================================================================================
```

### Failed Test Example
```
================================================================================
📊 COMPREHENSIVE TEST RESULTS SUMMARY
================================================================================
📈 Overall Statistics:
   Total Tests: 60
   Total Failures: 2
   Total Errors: 1
   Overall Success Rate: 95.0%
   Total Duration: 6.8 seconds

📋 Results by Test Suite:
   ✅ PASS General Utilities             20 tests  100.0%   2.3s
   ❌ FAIL Hybrid Optimization           20 tests   90.0%   2.2s
   ✅ PASS Smart Optimization            20 tests  100.0%   2.3s

⚠️  DETAILED FAILURE/ERROR REPORT:
--------------------------------------------------------------------------------

🔍 Hybrid Optimization:
   ❌ Failures (2):
      • test_hybrid_optimization.TestHybridWorkflowSteps.test_step2_optimization_setup
        AssertionError: 32 != 36
        
   ⚠️  Errors (1):
      • test_hybrid_optimization.TestHybridIntegration.test_external_turbine_integration
        ImportError: No module named 'hybrid_module'

================================================================================
⚠️  Some tests failed. Please review the failures above.
================================================================================
```

## 🔧 Test Configuration

### Key Features
- **Mocking**: Extensive use of mocks for FLORIS interface and file operations
- **Temporary Files**: Tests create temporary files and clean up automatically
- **Parameterized Tests**: Using `subTest()` for testing multiple scenarios
- **Error Handling**: Comprehensive error condition testing
- **Performance**: Memory and execution time validation
- **Integration**: Cross-component integration testing

### Test Data
Tests use realistic but minimal datasets:
- Sample turbine layouts with internal/external designation
- Representative wind data with proper frequency normalization
- Boundary definitions for site constraints
- Mock FLORIS configurations

## 🐛 Debugging Failed Tests

### Common Issues and Solutions

1. **Import Errors**
   - Ensure all optimization scripts are in the same directory
   - Check that `params.py` exists and contains required parameters
   - Verify FLORIS path configuration

2. **File Not Found Errors**
   - Tests create temporary files - ensure write permissions
   - Check that test data files are properly created in `setUp()`

3. **Parameter Errors**
   - Verify `params.py` contains all required parameters
   - Check parameter types match expected values
   - Ensure boolean parameters are actually boolean

4. **Mock/Patch Errors**
   - Mock objects may need additional method definitions
   - Check that mocked functions return appropriate data types

### Debugging Commands
```bash
# Run with verbose output
python3 -m unittest test_optimization_scripts -v

# Run single failing test with full traceback
python3 -m unittest test_optimization_scripts.TestUtilities.test_failing_function -v

# Debug specific test pattern
python3 run_all_tests.py utilities
```

## 📈 Test Metrics

### Coverage Goals
- **Functionality**: All major functions tested
- **Error Handling**: All error conditions covered
- **Integration**: Cross-component interactions validated
- **Performance**: Memory and speed requirements verified
- **Parameters**: All configuration parameters validated

### Success Criteria
- ✅ **100% Pass Rate**: All tests should pass
- ✅ **Fast Execution**: Full suite runs in < 30 seconds
- ✅ **Comprehensive**: All critical functionality covered
- ✅ **Maintainable**: Tests are easy to update and extend

## 🔄 Maintenance

### Adding New Tests
1. Add test methods to appropriate test class
2. Use descriptive test names: `test_feature_specific_scenario`
3. Include proper setup/teardown
4. Add docstrings explaining test purpose
5. Use assertions that provide meaningful error messages

### Updating Tests for New Features
1. Add new test classes for major new components
2. Update existing tests when interfaces change
3. Add integration tests for new feature combinations
4. Update documentation with new test descriptions

### Regular Maintenance
- Run full test suite before major releases
- Update test data when optimization parameters change
- Review and update mock objects when FLORIS interfaces change
- Keep test documentation current with actual test coverage

## 🎯 Hub Height Fix Verification

The test suite specifically validates the fix for the hub height issue:

**Problem**: `opt-pymoo-windrose-freq-ts-external_smart.py` line 1168 had hardcoded `ref_ht = 90`

**Solution**: Dynamic hub height calculation from turbine configuration

**Test Validation**: `TestSmartOptimizationWorkflow.test_hub_height_calculation_fix()` ensures:
- Hub height is calculated from FLORIS interface, not hardcoded
- Different turbine types use their actual hub heights  
- Fallback behavior works when turbine data unavailable

## 📝 Contributing

When adding new optimization features:
1. Add corresponding tests to appropriate test module
2. Update this documentation
3. Ensure tests pass before submitting changes
4. Add integration tests for cross-component features

For questions about the test suite, refer to the individual test files which contain detailed docstrings and comments explaining the testing approach.