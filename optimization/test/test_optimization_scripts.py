#!/usr/bin/env python3
"""
Comprehensive Test Suite for Wind Farm Optimization Scripts
Tests for both hybrid and smart optimization scripts plus utilities
"""

import unittest
import numpy as np
import pandas as pd
import os
import sys
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import modules to test
import params as pr
import utilities
from test_enhanced_farm_filtering import EnhancedDirectionalFarmManager


class TestUtilities(unittest.TestCase):
    """Test utilities.py functions"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.test_boundaries = pd.DataFrame({
            'x': [530000, 535000, 535000, 530000, 530000],
            'y': [5850000, 5850000, 5855000, 5855000, 5850000]
        })
        
        self.test_layout = pd.DataFrame({
            'x': [531000, 532000, 533000],
            'y': [5851000, 5852000, 5853000],
            'external': [False, False, True],
            'turb': ['Internal_A', 'Internal_B', 'External_C']
        })
        
    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir)
    
    def test_sanitize_name(self):
        """Test name sanitization function"""
        test_cases = [
            ("Test File.csv", "test file.csv"),  # Converts to lowercase, doesn't replace spaces
            ("file-name", "file-name"),
            ("file name with spaces", "file name with spaces"),  # Spaces are not replaced
            ("file/with\\slashes", "file_with_slashes"),  # Slashes are replaced
            ("", ""),
            (None, "")
        ]
        
        for input_name, expected in test_cases:
            with self.subTest(input=input_name):
                result = utilities.sanitize_name(input_name)
                self.assertEqual(result, expected)
    
    def test_memory_footprint(self):
        """Test memory footprint calculation"""
        # Test with simple data structures
        test_data = [1, 2, 3, 4, 5]
        footprint = utilities.memory_footprint(test_data)
        self.assertIsInstance(footprint, (int, float))
        self.assertGreater(footprint, 0)
        
        # Test with pandas DataFrame
        df = pd.DataFrame({'a': [1, 2, 3], 'b': [4, 5, 6]})
        footprint_df = utilities.memory_footprint(df)
        self.assertIsInstance(footprint_df, (int, float))
        self.assertGreater(footprint_df, 0)
    
    def test_write_log(self):
        """Test log writing functionality"""
        log_file = os.path.join(self.temp_dir, "test.log")
        test_message = "Test log message"
        
        utilities.write_log(test_message, log_file)
        
        # Check if file was created and contains message
        self.assertTrue(os.path.exists(log_file))
        with open(log_file, 'r') as f:
            content = f.read()
            self.assertIn(test_message, content)
    
    def test_ts_to_freq_df(self):
        """Test time series to frequency conversion"""
        # Create test time series data
        test_data = pd.DataFrame({
            'ws': [5.0, 6.0, 7.0, 5.0, 6.0],
            'wd': [180, 270, 0, 180, 270],
            'freq': [0.2, 0.3, 0.1, 0.2, 0.2]
        })
        
        freq_df = utilities.ts_to_freq_df(test_data)
        
        self.assertIsInstance(freq_df, pd.DataFrame)
        self.assertIn('ws', freq_df.columns)
        self.assertIn('wd', freq_df.columns) 
        self.assertIn('freq_val', freq_df.columns)
        
        # Check frequency normalization
        total_freq = freq_df['freq_val'].sum()
        self.assertAlmostEqual(total_freq, 1.0, places=6)
    
    def test_load_boundaries(self):
        """Test boundary loading functionality"""
        # Create test boundary file
        boundary_file = os.path.join(self.temp_dir, "boundaries.csv")
        self.test_boundaries.to_csv(boundary_file, index=False)
        
        boundaries = utilities.load_boundaries(boundary_file)
        
        self.assertIsInstance(boundaries, list)
        self.assertEqual(len(boundaries), len(self.test_boundaries))
        # Check that boundaries are list of coordinate tuples
        self.assertIsInstance(boundaries[0], tuple)
        self.assertEqual(len(boundaries[0]), 2)  # (x, y) pair
    
    @patch('matplotlib.pyplot.savefig')
    @patch('matplotlib.pyplot.show')
    def test_plot_layout_beforeOptim(self, mock_show, mock_savefig):
        """Test layout plotting function"""
        output_file = os.path.join(self.temp_dir, "test_plot.png")
        
        # Test with minimal data
        utilities.plot_layout_beforeOptim(
            self.test_layout, 
            boundaries=self.test_boundaries,
            output_file=output_file
        )
        
        # Verify plot functions were called
        mock_savefig.assert_called()
    
    def test_create_enhanced_farm_manager(self):
        """Test enhanced farm manager creation"""
        internal_layout = self.test_layout[self.test_layout['external'] == False]
        external_layout = self.test_layout[self.test_layout['external'] == True]
        
        manager = utilities.create_enhanced_farm_manager(
            internal_layout, 
            external_layout,
            max_distance_km=20,
            sector_width=90
        )
        
        self.assertIsInstance(manager, EnhancedDirectionalFarmManager)
        self.assertEqual(len(manager.internal_layout), 2)
        self.assertTrue(manager.has_external)
    
    @patch('matplotlib.pyplot.savefig')
    def test_plot_farm_sectors(self, mock_savefig):
        """Test farm sector plotting"""
        internal_layout = self.test_layout[self.test_layout['external'] == False]
        external_layout = self.test_layout[self.test_layout['external'] == True]
        
        manager = utilities.create_enhanced_farm_manager(
            internal_layout, external_layout
        )
        
        output_file = os.path.join(self.temp_dir, "farm_sectors.png")
        
        utilities.plot_farm_sectors(
            manager, 
            wind_direction=0, 
            output_file=output_file,
            boundaries=self.test_boundaries
        )
        
        mock_savefig.assert_called()
    
    @patch('matplotlib.pyplot.savefig')
    def test_plot_enhanced_farm_analysis(self, mock_savefig):
        """Test enhanced farm analysis plotting"""
        internal_layout = self.test_layout[self.test_layout['external'] == False]
        external_layout = self.test_layout[self.test_layout['external'] == True]
        
        manager = utilities.create_enhanced_farm_manager(
            internal_layout, external_layout
        )
        
        output_dir = self.temp_dir
        
        utilities.plot_enhanced_farm_analysis(
            manager,
            output_dir=output_dir,
            boundaries=self.test_boundaries
        )
        
        # Should generate multiple plots
        self.assertGreaterEqual(mock_savefig.call_count, 2)


class TestEnhancedDirectionalFarmManager(unittest.TestCase):
    """Test EnhancedDirectionalFarmManager functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.internal_layout = pd.DataFrame({
            'x': [531000, 532000, 533000],
            'y': [5851000, 5851000, 5851000],
            'external': [False, False, False],
            'turb': ['Internal'] * 3
        })
        
        # Create external farms in different directions
        self.external_layout = pd.DataFrame({
            'x': [532000]*3 + [528000]*3 + [532000]*3 + [536000]*3,  # N, W, S, E
            'y': [5855000]*3 + [5851000]*3 + [5847000]*3 + [5851000]*3,
            'external': [True] * 12,
            'turb': ['North_Farm']*3 + ['West_Farm']*3 + ['South_Farm']*3 + ['East_Farm']*3
        })
    
    def test_initialization_with_external(self):
        """Test manager initialization with external turbines"""
        manager = EnhancedDirectionalFarmManager(
            self.internal_layout, 
            self.external_layout,
            max_distance_km=20,
            sector_width=90
        )
        
        self.assertTrue(manager.has_external)
        self.assertEqual(len(manager.internal_layout), 3)
        self.assertEqual(len(manager.external_layout), 12)
        self.assertEqual(len(manager.external_farms), 4)  # 4 farms detected
    
    def test_initialization_without_external(self):
        """Test manager initialization without external turbines"""
        empty_external = pd.DataFrame()
        manager = EnhancedDirectionalFarmManager(
            self.internal_layout,
            empty_external
        )
        
        self.assertFalse(manager.has_external)
        self.assertEqual(len(manager.internal_layout), 3)
        self.assertEqual(len(manager.external_farms), 0)
    
    def test_farm_detection_by_name(self):
        """Test farm detection using turbine names"""
        manager = EnhancedDirectionalFarmManager(
            self.internal_layout,
            self.external_layout,
            farm_grouping='by_name'
        )
        
        expected_farms = {'North_Farm', 'West_Farm', 'South_Farm', 'East_Farm'}
        detected_farms = set(manager.external_farms.keys())
        
        self.assertEqual(detected_farms, expected_farms)
        
        # Check farm sizes
        for farm_name in expected_farms:
            self.assertEqual(manager.external_farms[farm_name]['n_turbines'], 3)
    
    def test_directional_filtering(self):
        """Test directional filtering functionality"""
        manager = EnhancedDirectionalFarmManager(
            self.internal_layout,
            self.external_layout,
            sector_width=90
        )
        
        # Test different wind directions
        test_cases = [
            (0, ['North_Farm']),      # Wind from North -> North farms upwind
            (90, ['East_Farm']),      # Wind from East -> East farms upwind  
            (180, ['South_Farm']),    # Wind from South -> South farms upwind
            (270, ['West_Farm'])      # Wind from West -> West farms upwind
        ]
        
        for wind_dir, expected_farms in test_cases:
            with self.subTest(wind_direction=wind_dir):
                relevant_farms = manager.get_relevant_farms_for_direction(wind_dir)
                self.assertEqual(set(relevant_farms), set(expected_farms))
    
    def test_layout_filtering(self):
        """Test complete layout filtering"""
        manager = EnhancedDirectionalFarmManager(
            self.internal_layout,
            self.external_layout
        )
        
        # Test layout generation for different wind directions
        for wind_dir in [0, 90, 180, 270]:
            layout = manager.get_layout_with_farm_filtering(wind_dir)
            
            # Should always include internal turbines
            internal_count = len(layout[layout['external'] == False])
            self.assertEqual(internal_count, 3)
            
            # Should include some external turbines (3 per relevant farm)
            external_count = len(layout[layout['external'] == True])
            self.assertEqual(external_count, 3)  # One farm per direction
    
    def test_filtering_statistics(self):
        """Test statistics generation"""
        manager = EnhancedDirectionalFarmManager(
            self.internal_layout,
            self.external_layout
        )
        
        stats = manager.get_filtering_statistics()
        
        # Check basic statistics
        self.assertEqual(stats['n_internal'], 3)
        self.assertEqual(stats['n_external_total'], 12)
        self.assertEqual(stats['n_farms'], 4)
        
        # Check that direction statistics exist
        self.assertIn('by_direction', stats)
        self.assertIn('0°', stats['by_direction'])
        
        # Check farm statistics
        self.assertIn('farms', stats)
        self.assertEqual(len(stats['farms']), 4)
    
    def test_cache_stats_string(self):
        """Test cache statistics string generation"""
        manager = EnhancedDirectionalFarmManager(
            self.internal_layout,
            self.external_layout
        )
        
        cache_stats = manager.get_cache_stats()
        
        self.assertIsInstance(cache_stats, str)
        self.assertIn('Smart filtering cache statistics', cache_stats)
        self.assertIn('Total external turbines: 12', cache_stats)
        self.assertIn('Number of farms detected: 4', cache_stats)


class TestOptimizationParameters(unittest.TestCase):
    """Test parameter management and validation"""
    
    def test_params_existence(self):
        """Test that all required parameters exist"""
        required_params = [
            'solver', 'MAXGEN', 'PopSize', 'pCross_real', 'eta_c', 'eta_m',
            'ftol', 'FLORIS_CONFIG', 'inputLayoutFile', 'windRoseFile',
            'boundariesFile', 'min_dist', 'max_distance_km', 'use_external_turbines'
        ]
        
        for param in required_params:
            with self.subTest(parameter=param):
                self.assertTrue(hasattr(pr, param), f"Parameter {param} not found in params.py")
    
    def test_parameter_types(self):
        """Test parameter type validation"""
        # Numeric parameters
        numeric_params = {
            'MAXGEN': int,
            'PopSize': int, 
            'pCross_real': float,
            'eta_c': float,
            'eta_m': float,
            'ftol': float,
            'min_dist': (int, float),
            'max_distance_km': (int, float)
        }
        
        for param, expected_type in numeric_params.items():
            with self.subTest(parameter=param):
                value = getattr(pr, param)
                if isinstance(expected_type, tuple):
                    self.assertIsInstance(value, expected_type)
                else:
                    self.assertIsInstance(value, expected_type)
    
    def test_parameter_ranges(self):
        """Test parameter value ranges"""
        # Test reasonable ranges
        self.assertGreater(pr.MAXGEN, 0)
        self.assertGreater(pr.PopSize, 0)
        self.assertTrue(0.0 <= pr.pCross_real <= 1.0)
        self.assertGreater(pr.eta_c, 0.0)
        self.assertGreater(pr.eta_m, 0.0)
        self.assertGreater(pr.ftol, 0.0)
        self.assertGreater(pr.min_dist, 0.0)
        self.assertGreater(pr.max_distance_km, 0.0)
    
    def test_boolean_parameters(self):
        """Test boolean parameter types"""
        boolean_params = [
            'use_external_turbines',
            'use_enhanced_filtering', 
            'plot_farm_analysis',
            'plot_iterations',
            'plot_layout_before_optim'
        ]
        
        for param in boolean_params:
            with self.subTest(parameter=param):
                if hasattr(pr, param):
                    value = getattr(pr, param)
                    self.assertIsInstance(value, bool)


class TestOptimizationWorkflow(unittest.TestCase):
    """Test optimization workflow components"""
    
    def setUp(self):
        """Set up mock data for workflow testing"""
        self.mock_layout = pd.DataFrame({
            'x': [531000, 532000, 533000],
            'y': [5851000, 5851000, 5851000],
            'external': [False, False, False],
            'turb': ['test_turbine'] * 3
        })
        
        self.mock_wind_data = pd.DataFrame({
            'ws': [8.0] * 10,
            'wd': np.linspace(0, 350, 10),
            'freq_val': [0.1] * 10
        })
    
    @patch('sys.path')
    def test_floris_path_setup(self, mock_path):
        """Test FLORIS path configuration"""
        # This would be tested in actual script imports
        # but we can verify the path structure
        expected_floris_path = os.path.join(os.getcwd(), 'FLORIS_311_VF1_Operational')
        
        # Test path exists or can be constructed
        self.assertIsInstance(expected_floris_path, str)
        self.assertIn('FLORIS_311_VF1_Operational', expected_floris_path)
    
    def test_turbine_type_normalization(self):
        """Test turbine type name normalization"""
        # Import the function from hybrid script if possible
        try:
            sys.path.append('/project/bii69/ps/ps/optimization')
            from opt_pymoo_windrose_freq_ts_external_hybrid import normalize_turbine_types
            
            test_cases = [
                (['Zeevonk East'], ['zeevonk east']),
                (['Zeevonk West'], ['zeevonk west']),
                (['SWT_3.6-107_PP_M102_std_R20121010'], ['swt_3.6-107_pp_m102_std_r20121010']),
                (['unknown_turbine'], ['unknown_turbine'])
            ]
            
            for input_types, expected in test_cases:
                with self.subTest(input=input_types):
                    result = normalize_turbine_types(np.array(input_types))
                    np.testing.assert_array_equal(result, expected)
                    
        except ImportError:
            self.skipTest("Hybrid optimization script not available for testing")
    
    def test_layout_validation(self):
        """Test layout data validation"""
        # Test required columns
        required_columns = ['x', 'y', 'external', 'turb']
        
        for col in required_columns:
            with self.subTest(column=col):
                self.assertIn(col, self.mock_layout.columns)
        
        # Test data types
        self.assertTrue(pd.api.types.is_numeric_dtype(self.mock_layout['x']))
        self.assertTrue(pd.api.types.is_numeric_dtype(self.mock_layout['y']))
        self.assertTrue(pd.api.types.is_bool_dtype(self.mock_layout['external']))
    
    def test_wind_data_validation(self):
        """Test wind data validation"""
        # Test required columns
        required_columns = ['ws', 'wd', 'freq_val']
        
        for col in required_columns:
            with self.subTest(column=col):
                self.assertIn(col, self.mock_wind_data.columns)
        
        # Test value ranges
        self.assertTrue((self.mock_wind_data['ws'] >= 0).all())
        self.assertTrue((self.mock_wind_data['wd'] >= 0).all())
        self.assertTrue((self.mock_wind_data['wd'] < 360).all())
        self.assertTrue((self.mock_wind_data['freq_val'] >= 0).all())
        
        # Test frequency normalization
        total_freq = self.mock_wind_data['freq_val'].sum()
        self.assertAlmostEqual(total_freq, 1.0, places=6)


class TestErrorHandling(unittest.TestCase):
    """Test error handling and edge cases"""
    
    def test_empty_layouts(self):
        """Test handling of empty layouts"""
        empty_df = pd.DataFrame()
        internal_df = pd.DataFrame({
            'x': [531000], 'y': [5851000], 
            'external': [False], 'turb': ['test']
        })
        
        # Test enhanced manager with empty external
        manager = EnhancedDirectionalFarmManager(internal_df, empty_df)
        self.assertFalse(manager.has_external)
        
        # Test layout filtering with no external
        layout = manager.get_layout_with_farm_filtering(0)
        self.assertEqual(len(layout), 1)
        self.assertFalse(layout['external'].iloc[0])
    
    def test_invalid_wind_directions(self):
        """Test handling of invalid wind directions"""
        internal_df = pd.DataFrame({
            'x': [531000], 'y': [5851000],
            'external': [False], 'turb': ['test']
        })
        external_df = pd.DataFrame({
            'x': [532000], 'y': [5852000],
            'external': [True], 'turb': ['external']
        })
        
        manager = EnhancedDirectionalFarmManager(internal_df, external_df)
        
        # Test edge wind directions
        test_directions = [-10, 360, 450, -180]
        for wd in test_directions:
            with self.subTest(wind_direction=wd):
                # Should handle without crashing
                farms = manager.get_relevant_farms_for_direction(wd)
                self.assertIsInstance(farms, list)
    
    def test_missing_files(self):
        """Test handling of missing input files"""
        non_existent_file = "/path/that/does/not/exist.csv"
        
        # Test boundary loading with missing file - should return default boundaries
        boundaries = utilities.load_boundaries(non_existent_file)
        self.assertIsInstance(boundaries, list)
        self.assertGreater(len(boundaries), 0)  # Should return default boundary
    
    def test_malformed_data(self):
        """Test handling of malformed input data"""
        # Test with missing required columns
        incomplete_layout = pd.DataFrame({'x': [1, 2, 3]})
        
        with self.assertRaises((KeyError, AttributeError)):
            # This should fail due to missing columns
            EnhancedDirectionalFarmManager(incomplete_layout, pd.DataFrame())


def create_test_suite():
    """Create comprehensive test suite"""
    suite = unittest.TestSuite()
    
    # Add all test cases
    test_classes = [
        TestUtilities,
        TestEnhancedDirectionalFarmManager, 
        TestOptimizationParameters,
        TestOptimizationWorkflow,
        TestErrorHandling
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)
    
    return suite


if __name__ == '__main__':
    # Run the complete test suite
    print("="*80)
    print("Running Comprehensive Wind Farm Optimization Test Suite")
    print("="*80)
    
    # Create and run test suite
    suite = create_test_suite()
    runner = unittest.TextTestRunner(verbosity=2, buffer=True)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "="*80)
    print("TEST SUMMARY")
    print("="*80)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.failures:
        print(f"\nFAILURES ({len(result.failures)}):")
        for test, traceback in result.failures:
            print(f"  - {test}")
    
    if result.errors:
        print(f"\nERRORS ({len(result.errors)}):")
        for test, traceback in result.errors:
            print(f"  - {test}")
    
    if result.wasSuccessful():
        print("\n🎉 All tests passed successfully!")
    else:
        print("\n⚠️  Some tests failed. Please check the output above.")
    
    print("="*80)