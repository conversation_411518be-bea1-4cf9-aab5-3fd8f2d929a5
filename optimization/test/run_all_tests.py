#!/usr/bin/env python3
"""
Comprehensive Test Runner for Wind Farm Optimization
Runs all test suites and generates detailed reports
"""

import unittest
import sys
import os
import time
from pathlib import Path
import importlib.util

# Add parent directory to path  
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def import_test_module(module_name, file_path):
    """Dynamically import a test module"""
    try:
        spec = importlib.util.spec_from_file_location(module_name, file_path)
        if spec and spec.loader:
            module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(module)
            return module
        return None
    except Exception as e:
        print(f"Warning: Could not import {module_name}: {e}")
        return None

def run_test_suite(test_module, suite_name):
    """Run a specific test suite and return results"""
    print(f"\n{'='*60}")
    print(f"Running {suite_name}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    # Get test suite from module
    if hasattr(test_module, f'create_{suite_name.lower().replace(" ", "_")}_suite'):
        suite_func = getattr(test_module, f'create_{suite_name.lower().replace(" ", "_")}_suite')
        suite = suite_func()
    elif hasattr(test_module, 'create_test_suite'):
        suite = test_module.create_test_suite()
    else:
        # Fallback: load all tests from module
        loader = unittest.TestLoader()
        suite = loader.loadTestsFromModule(test_module)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=1, buffer=True, stream=sys.stdout)
    result = runner.run(suite)
    
    end_time = time.time()
    duration = end_time - start_time
    
    return {
        'name': suite_name,
        'tests_run': result.testsRun,
        'failures': len(result.failures),
        'errors': len(result.errors),
        'success_rate': ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0,
        'duration': duration,
        'result': result
    }

def print_detailed_results(results):
    """Print detailed test results"""
    print("\n" + "="*80)
    print("📊 COMPREHENSIVE TEST RESULTS SUMMARY")
    print("="*80)
    
    total_tests = sum(r['tests_run'] for r in results)
    total_failures = sum(r['failures'] for r in results)
    total_errors = sum(r['errors'] for r in results)
    total_duration = sum(r['duration'] for r in results)
    overall_success_rate = ((total_tests - total_failures - total_errors) / total_tests * 100) if total_tests > 0 else 0
    
    print(f"📈 Overall Statistics:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Total Failures: {total_failures}")
    print(f"   Total Errors: {total_errors}")
    print(f"   Overall Success Rate: {overall_success_rate:.1f}%")
    print(f"   Total Duration: {total_duration:.1f} seconds")
    
    print(f"\n📋 Results by Test Suite:")
    for result in results:
        status = "✅ PASS" if result['failures'] == 0 and result['errors'] == 0 else "❌ FAIL"
        print(f"   {status} {result['name']:<30} {result['tests_run']:>3} tests  {result['success_rate']:>5.1f}%  {result['duration']:>5.1f}s")
    
    # Detailed failure/error reporting
    if total_failures > 0 or total_errors > 0:
        print(f"\n⚠️  DETAILED FAILURE/ERROR REPORT:")
        print("-" * 80)
        
        for result in results:
            if result['failures'] > 0 or result['errors'] > 0:
                print(f"\n🔍 {result['name']}:")
                
                if result['failures'] > 0:
                    print(f"   ❌ Failures ({result['failures']}):")
                    for test, trace in result['result'].failures:
                        print(f"      • {test}")
                        # Print first few lines of traceback
                        trace_lines = trace.strip().split('\n')
                        for line in trace_lines[-3:]:  # Last 3 lines of traceback
                            print(f"        {line}")
                        print()
                
                if result['errors'] > 0:
                    print(f"   ⚠️  Errors ({result['errors']}):")
                    for test, trace in result['result'].errors:
                        print(f"      • {test}")
                        # Print first few lines of traceback
                        trace_lines = trace.strip().split('\n')
                        for line in trace_lines[-3:]:  # Last 3 lines of traceback
                            print(f"        {line}")
                        print()
    
    print("\n" + "="*80)
    if overall_success_rate == 100.0:
        print("🎉 ALL TESTS PASSED! Your optimization system is working correctly.")
    elif overall_success_rate >= 90.0:
        print("✅ Most tests passed! Minor issues may need attention.")
    elif overall_success_rate >= 75.0:
        print("⚠️  Some tests failed. Please review the failures above.")
    else:
        print("❌ Many tests failed. Significant issues need to be addressed.")
    print("="*80)

def main():
    """Main test runner function"""
    print("🧪 Wind Farm Optimization - Comprehensive Test Suite")
    print("="*80)
    print("Testing all optimization scripts and utilities...")
    
    # Define test modules to run
    test_modules = [
        {
            'name': 'General Utilities',
            'file': 'test_optimization_scripts.py',
            'module': 'test_optimization_scripts'
        },
        {
            'name': 'Hybrid Optimization',
            'file': 'test_hybrid_optimization.py', 
            'module': 'test_hybrid_optimization'
        },
        {
            'name': 'Smart Optimization',
            'file': 'test_smart_optimization.py',
            'module': 'test_smart_optimization'
        }
    ]
    
    results = []
    
    # Run each test suite
    for test_info in test_modules:
        file_path = os.path.join(os.path.dirname(__file__), test_info['file'])
        
        if os.path.exists(file_path):
            test_module = import_test_module(test_info['module'], file_path)
            if test_module:
                result = run_test_suite(test_module, test_info['name'])
                results.append(result)
            else:
                print(f"⚠️  Could not import {test_info['name']} test module")
        else:
            print(f"⚠️  Test file not found: {test_info['file']}")
    
    # Print comprehensive results
    if results:
        print_detailed_results(results)
    else:
        print("❌ No test results to display. Check that test files exist and are importable.")
    
    # Return exit code based on results
    if results:
        total_failures = sum(r['failures'] for r in results)
        total_errors = sum(r['errors'] for r in results)
        return 1 if (total_failures > 0 or total_errors > 0) else 0
    else:
        return 1

def run_specific_tests(test_pattern=""):
    """Run specific tests matching a pattern"""
    if not test_pattern:
        return main()
    
    print(f"🔍 Running tests matching pattern: '{test_pattern}'")
    print("="*80)
    
    # Discover and run matching tests
    loader = unittest.TestLoader()
    start_dir = os.path.dirname(__file__)
    
    # Discover all tests
    suite = loader.discover(start_dir, pattern=f"*{test_pattern}*.py")
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2, buffer=True)
    result = runner.run(suite)
    
    # Print summary
    print(f"\n📊 Pattern '{test_pattern}' Results:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    
    success_rate = ((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100) if result.testsRun > 0 else 0
    print(f"   Success rate: {success_rate:.1f}%")
    
    return 1 if (len(result.failures) > 0 or len(result.errors) > 0) else 0

if __name__ == '__main__':
    # Check for command line arguments
    if len(sys.argv) > 1:
        test_pattern = sys.argv[1]
        exit_code = run_specific_tests(test_pattern)
    else:
        exit_code = main()
    
    sys.exit(exit_code)