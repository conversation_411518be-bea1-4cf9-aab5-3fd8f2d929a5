Best solution(s):\nIt holds the decision variables of the best found solution(s).:
None
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
None
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
None
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
None
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.53344741 0.42020473 0.61554039 0.13845582 0.49608822 0.28506326
 0.27594686 0.80993531 0.53479646 0.40956554 0.32638612 0.74377293
 0.83401527 0.37763288 0.06832894 0.70943254 0.27042698 0.39641866
 0.17721648 0.16997604]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17352214]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[-1356.56381602 -1434.78857383 -2439.84953268 -1833.77103249
  -962.99873488 -1434.78857383 -1742.62430904 -3438.45144977
  -962.99873488 -1250.67379257     0.             0.
     0.             0.             0.             0.
     0.             0.             0.             0.        ]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
None
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
None
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
None
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
None
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
None
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
None
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.38822258 0.36105971 0.03090625 0.21203643 0.46102681 0.34695658
 0.04893212 0.17502604 0.03219633 0.00577267 0.59527413 0.90637986
 0.80362057 0.61012125 0.82920138 0.94478086 0.76066618 0.56895625
 0.23038345 0.18611437]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17235662]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[-1.96199325e+03 -3.02762632e+01 -1.03529798e+02 -2.39089994e+02
 -1.19650226e+03 -3.02762632e+01 -1.03529798e+02 -2.39089994e+02
 -1.75054603e+02 -1.75054603e+02  0.00000000e+00  0.00000000e+00
 -1.00000000e+00  0.00000000e+00  0.00000000e+00 -1.00000000e+00
 -1.00000000e+00 -1.00000000e+00  0.00000000e+00 -1.00000000e+00]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.79448702 0.54017559 0.61572776 0.75860261 0.97428406 0.75486643
 0.83725641 0.48625359 0.20827436 0.68050708 0.17600685 0.38098011
 0.31043526 0.09572596 0.55920945 0.55752529 0.21336299 0.12158638
 0.81678677 0.13628777]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17282148]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[-2.67981892e+02 -8.77650929e+02 -8.77650929e+02 -6.25181629e+02
 -2.55309348e+03 -2.55309348e+03 -2.67981892e+02 -2.21110496e+03
 -6.58273744e+03 -7.08579713e+02 -1.00000000e+00  0.00000000e+00
  0.00000000e+00 -1.00000000e+00 -1.00000000e+00  0.00000000e+00
 -1.00000000e+00  0.00000000e+00 -1.00000000e+00 -1.00000000e+00]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.33716271 0.52750842 0.34404501 0.31689498 0.47072325 0.39696372
 0.64384757 0.48702991 0.29975575 0.61954066 0.12880022 0.84922028
 0.08663428 0.48267917 0.78853059 0.12363462 0.82488306 0.83873127
 0.10939553 0.79525163]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17045846]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[-4.66876203e+01 -9.30379313e+01 -4.66876203e+01 -3.84603154e+03
 -1.76869188e+02 -3.47987054e+02 -1.47883427e+01 -9.30379313e+01
 -8.84119327e+01 -1.47883427e+01  0.00000000e+00  0.00000000e+00
 -1.00000000e+00  0.00000000e+00  0.00000000e+00  0.00000000e+00
  0.00000000e+00  0.00000000e+00  0.00000000e+00  0.00000000e+00]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.64537598 0.7903121  0.38554656 0.31699245 0.48240199 0.72537623
 0.85494841 0.15978561 0.53329006 0.62338392 0.76280082 0.07805515
 0.46362269 0.35462275 0.29445716 0.87472978 0.19755383 0.3065948
 0.53363066 0.37510965]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17296575]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[-286.56840893 -240.99622489 -162.45927244 -162.45927244 -706.90482648
 -286.56840893 -240.99622489 -771.74985415 -736.39201057 -706.90482648
    0.           -1.            0.            0.            0.
   -1.           -1.            0.            0.            0.        ]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.46547532 0.50979366 0.76918046 0.42381553 0.26191351 0.73432304
 0.64829794 0.36842754 0.64042536 0.68093478 0.05167971 0.56570044
 0.38125079 0.35260901 0.47240283 0.62792282 0.38313614 0.73617742
 0.56237132 0.55024679]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17285857]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[    0.             0.             0.             0.
     0.             0.             0.             0.
     0.             0.           -83.17228062 -3935.78799288
  -756.56127518 -3411.79188375  -971.72407682 -1753.8420107
 -2108.36311123 -1242.62168108 -3091.72058112 -2949.33693257]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
None
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
None
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
None
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
None
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
None
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
None
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
None
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
None
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
None
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
None
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.44419821 0.94707969 0.55340873 0.28108929 0.71264827 0.03993096
 0.49466442 0.84319565 0.26376789 0.16010193 0.43929599 0.46195925
 0.07433166 0.97327002 0.61992766 0.38067196 0.79761626 0.17929385
 0.19046912 0.65529774]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17484192]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[  0.           0.           0.           0.           0.
   0.           0.           0.           0.           0.
  -0.56096776  -1.9651498   -0.09788928  -9.85631901  -1.89581783
 -21.73017372  -1.23023904 -61.84667896  -2.85412391 -17.94225289]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.44419821 0.94707969 0.55340873 0.28108929 0.71264827 0.03993096
 0.49466442 0.84319565 0.26376789 0.16010193 0.43929599 0.46195925
 0.07433166 0.97327002 0.61992766 0.38067196 0.79761626 0.17929385
 0.19046912 0.65529774]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17484192]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[  0.           0.           0.           0.           0.
   0.           0.           0.           0.           0.
  -0.56096776  -1.9651498   -0.09788928  -9.85631901  -1.89581783
 -21.73017372  -1.23023904 -61.84667896  -2.85412391 -17.94225289]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.44419821 0.94707969 0.55340873 0.28108929 0.71264827 0.03993096
 0.49466442 0.84319565 0.26376789 0.16010193 0.43929599 0.46195925
 0.07433166 0.97327002 0.61992766 0.38067196 0.79761626 0.17929385
 0.19046912 0.65529774]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17484192]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[  0.           0.           0.           0.           0.
   0.           0.           0.           0.           0.
  -0.56096776  -1.9651498   -0.09788928  -9.85631901  -1.89581783
 -21.73017372  -1.23023904 -61.84667896  -2.85412391 -17.94225289]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.44419821 0.94707969 0.55340873 0.28108929 0.71264827 0.03993096
 0.49466442 0.84319565 0.26376789 0.16010193 0.43929599 0.46195925
 0.07433166 0.97327002 0.61992766 0.38067196 0.79761626 0.17929385
 0.19046912 0.65529774]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17484192]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[  0.           0.           0.           0.           0.
   0.           0.           0.           0.           0.
  -0.56096776  -1.9651498   -0.09788928  -9.85631901  -1.89581783
 -21.73017372  -1.23023904 -61.84667896  -2.85412391 -17.94225289]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.44419821 0.94707969 0.55340873 0.28108929 0.71264827 0.03993096
 0.49466442 0.84319565 0.26376789 0.16010193 0.43929599 0.46195925
 0.07433166 0.97327002 0.61992766 0.38067196 0.79761626 0.17929385
 0.19046912 0.65529774]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17484192]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[  0.           0.           0.           0.           0.
   0.           0.           0.           0.           0.
  -0.56096776  -1.9651498   -0.09788928  -9.85631901  -1.89581783
 -21.73017372  -1.23023904 -61.84667896  -2.85412391 -17.94225289]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.44419821 0.94707969 0.55340873 0.28108929 0.71264827 0.03993096
 0.49466442 0.84319565 0.26376789 0.16010193 0.43929599 0.46195925
 0.07433166 0.97327002 0.61992766 0.38067196 0.79761626 0.17929385
 0.19046912 0.65529774]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17484192]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[  0.           0.           0.           0.           0.
   0.           0.           0.           0.           0.
  -0.56096776  -1.9651498   -0.09788928  -9.85631901  -1.89581783
 -21.73017372  -1.23023904 -61.84667896  -2.85412391 -17.94225289]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
Best solution(s):\nIt holds the decision variables of the best found solution(s).:
[0.44419821 0.94707969 0.55340873 0.28108929 0.71264827 0.03993096
 0.49466442 0.84319565 0.26376789 0.16010193 0.43929599 0.46195925
 0.07433166 0.97327002 0.61992766 0.38067196 0.79761626 0.17929385
 0.19046912 0.65529774]
\nObjective space values F:\nIt contains the objective space values of the best found solution(s):
[0.17484192]
Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[  0.           0.           0.           0.           0.
   0.           0.           0.           0.           0.
  -0.56096776  -1.9651498   -0.09788928  -9.85631901  -1.89581783
 -21.73017372  -1.23023904 -61.84667896  -2.85412391 -17.94225289]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[0.]
