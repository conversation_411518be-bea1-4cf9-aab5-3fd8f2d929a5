name: <PERSON> (Update November 2023)
description: Scout tuning, derived by comparison with AM results for 6 projects.
floris_version: v3.1.1
logging:
  console:
    enable: true
    level: WARNING
  file:
    enable: false
    level: WARNING
solver:
  type: turbine_grid
  turbine_grid_points: 1
  flowfield_turbine_grid: 3
# We can ignore the farm inputs below, as we will read the layout separately
farm:
  layout_x:
  - 0.0
  layout_y:
  - 0.0
  turbine_type:
  - nrel_5MW
flow_field:
  air_density: 1.23
  WP_density_correction_method: True
  reference_wind_height: -1 # Since multiple defined turbines, must specify explicitly the reference wind height
  # WD/WS/TI will be overwritten in timeseries operation
  use_HH_vel_to_power: True # if true, the input to the power curve is the hub-height velocity
  turbulence_intensity: 0.045
  wind_directions:
  - 270.0
  wind_shear:  0.08
  wind_speeds:
  - 12.8
  wind_veer: 0.0
wake:
  model_strings:
    combination_model: analytical
    deflection_model: none
    turbulence_model: crespo_hernandez
    velocity_model: navarro
  enable_secondary_steering: false
  enable_yaw_added_recovery: false
  enable_transverse_velocities: false
  use_local_vel: true
  
  wake_deflection_parameters:
    gauss:
      ad: 0.0
      alpha: 0.58
      bd: 0.0
      beta: 0.077
      dm: 1.0
      ka: 0.38
      kb: 0.004
    jimenez:
      ad: 0.0
      bd: 0.0
      kd: 0.05
  wake_velocity_parameters:
    gauss:
      alpha: 0.58
      beta: 0.077
      ka: 0.38
      kb: 0.004
    jensen:
      wec_TI_multpl: 0.8
      
    navarro:
        alpha: 0.476
        a: 0.333
        beta: 2
        wec_TI_multpl: 0.082782006
        alpha2: 0.56
        a2: 0.2
        weCoeff: 0.5
        alphaCoeff: 0
        farDistance:  21
        lateralDistance: 3
        gaussianProfile: False
        
  wake_turbulence_parameters:
    crespo_hernandez:
      initial: 0
      constant: 0.81
      ai: 0.81
      downstream: -0.32

