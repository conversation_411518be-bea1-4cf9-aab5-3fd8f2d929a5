Best solution(s):
It holds the decision variables of the best found solution(s).:
[0.27276764 0.915195   0.29609138 0.13057437 0.54669845 0.
 0.2036411  0.75215056 0.         0.         0.65464778 0.67629843
 0.10962293 1.         0.90723356 0.55746397 1.         0.2630679
 0.2794051  0.95894132]

Objective space values F:
It contains the objective space values of the best found solution(s):
[-0.99684861]
Constraint violation values G:
It includes the constraint violation of the best found solution(s), if there are any constraints.
Each value in the array represents the degree to which the corresponding constraint is violated. 
A value of 0 or less indicates that the constraint is not violated
while a positive value indicates the degree of violation
[   0.            0.            0.            0.            0.
    0.            0.            0.            0.            0.
 -598.42842261  -14.96643691  -10.09788929 3230.9060216   -11.89581783
 2071.87119241 2355.5363355   -71.84667897  359.59231039 4542.50637445]
Aggregated constraint violation values CV:
If the aggregated constraint violation is zero, it means that there are no constraint violations, 
and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.
The larger the aggregated constraint violation, the more the solution violates the constraints.
[12560.41223434]
