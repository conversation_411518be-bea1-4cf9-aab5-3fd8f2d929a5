#!/usr/bin/env python3
"""
Simple test of DEAP turbine weights with available layout files
"""

import sys
import os
from pathlib import Path

# Set up paths
workDir = str(Path(__file__).parent.absolute())
floris_path = f"{workDir}/FLORIS_311_VF1_Operational"
sys.path.append(floris_path)

import numpy as np
import pandas as pd
import params as pr
from datetime import datetime
from time import perf_counter as timerpc
from core.tools import FlorisInterface
from deap_optimization_manager import DEAPOptimizationManager, DEAPProblemWrapper, OptimizationConfig
from utilities import ts_to_freq_df, write_log, sanitize_name

# Set up output file
OverAllProgress = f"{workDir}/OverAllProgress.txt"

print("DEAP Turbine Weights Test")
print("="*60)

# Load internal layout
print("\n1. Loading layouts...")
internal_layout = pd.read_csv(f"{workDir}/Input/Initial.layout.csv")
internal_layout = internal_layout.rename(columns={'easting': 'x', 'northing': 'y'})
internal_layout['turb'] = internal_layout['turb'].apply(sanitize_name)
internal_layout['weight'] = 1  # Internal turbines have weight=1

# Check if external layout exists
external_layout_path = f"{workDir}/Input/External.layout.csv"
if os.path.exists(external_layout_path):
    print("  Loading external layout...")
    external_layout = pd.read_csv(external_layout_path)
    external_layout = external_layout.rename(columns={'easting': 'x', 'northing': 'y'})
    external_layout['turb'] = external_layout['turb'].apply(sanitize_name)
    external_layout['external'] = True
    external_layout['weight'] = 0  # External turbines have weight=0
    
    # FOR TESTING: Use only first 10 external turbines
    print("  ⚠️  FOR TESTING: Using only first 10 external turbines")
    external_layout = external_layout.head(10)
    
    # Combine layouts
    combined_layout = pd.concat([internal_layout, external_layout], ignore_index=True)
    turbines_weights = combined_layout['weight'].values
else:
    print("  No external layout found, using internal only")
    combined_layout = internal_layout
    turbines_weights = None

print(f"\nLayout statistics:")
print(f"  Internal turbines: {len(internal_layout)}")
if turbines_weights is not None:
    print(f"  External turbines: {np.sum(turbines_weights == 0)}")
print(f"  Total turbines: {len(combined_layout)}")

# Load frequency data
print("\n2. Loading frequency data...")
ts = pd.read_csv(f"{workDir}/Input/timeseries.txt", sep=' ')
WR = ts_to_freq_df(ts)
wd_array = np.array(sorted(WR["wd"].unique()), dtype=float)
ws_array = np.array(sorted(WR["ws"].unique()), dtype=float)
freq = WR.set_index(['wd','ws']).unstack().values

# Initialize FLORIS
print("\n3. Initializing FLORIS...")
fi = FlorisInterface(pr.FLORIS_CONFIG)
fi.reinitialize(layout=(combined_layout['x'], combined_layout['y']),
                turbine_type=combined_layout['turb'],
                wind_directions=wd_array,
                wind_speeds=ws_array)

# Set reference height
ref_ht = fi.floris.farm.hub_heights[0]
fi.floris.flow_field.reference_wind_height = ref_ht

# Calculate baseline AEP
print("\n4. Calculating baseline AEP...")
baseline_aep = fi.get_farm_AEP(freq=freq) / 1E9
print(f"Baseline AEP: {baseline_aep:.3f} GWh")

# Load boundaries
boundaries = pd.read_csv(pr.boundariesFile)

# Create DEAP problem
print("\n5. Creating DEAP problem...")
problem = DEAPProblemWrapper(
    fi=fi,
    freq=freq,
    layout=combined_layout,
    boundaries=boundaries,
    baseline_aep=baseline_aep,
    turbines_weights=turbines_weights  # Can be None or array
)

# Verify problem setup
print(f"\nProblem configuration:")
print(f"  Total turbines: {problem.n_turbines}")
if turbines_weights is not None:
    print(f"  Internal turbines to optimize: {problem.numberOfInternalWTGs}")
    print(f"  External turbines (fixed): {problem.numberOfExternalWTGs}")
    print(f"  Optimization variables: {problem.n_var}")
    print(f"  Constraints: {problem.n_constr}")
else:
    print(f"  All turbines optimizable: {problem.n_turbines}")
    print(f"  Optimization variables: {problem.n_var}")
    print(f"  Constraints: {problem.n_constr}")

# Configure optimization
print("\n6. Running DEAP-GA optimization...")
config = OptimizationConfig(
    algorithm='GA',
    pop_size=pr.PopSize,
    n_gen=pr.MAXGEN,
    crossover_prob=0.9,
    eta_c=10.0,
    eta_m=50.0,
    verbose=True
)

optimizer = DEAPOptimizationManager(problem, config)
start_time = timerpc()
best_solution, best_fitness, stats = optimizer.optimize()
opt_time = timerpc() - start_time

print(f"\n7. Optimization Results:")
print(f"  Optimization time: {opt_time:.2f} seconds")
print(f"  Best fitness: {best_fitness:.4f}")
print(f"  Final AEP: {best_fitness * baseline_aep:.3f} GWh")
print(f"  Improvement: {(best_fitness - 1.0) * 100:.2f}%")

# Test evaluate function
print("\n8. Testing evaluate function...")
result = problem.evaluate(best_solution)
print(f"  Objective value: {result['F'][0]:.4f}")
print(f"  Constraint violations: {np.sum(result['G'] > 0)}")

print("\n" + "="*60)
print("TEST COMPLETE")
if turbines_weights is not None:
    print("✅ Successfully tested DEAP with turbine weights (internal/external separation)")
else:
    print("✅ Successfully tested DEAP without turbine weights (all turbines optimized)")
print("="*60)

write_log(OverAllProgress, f"DEAP test complete - Final AEP: {best_fitness * baseline_aep:.3f} GWh")