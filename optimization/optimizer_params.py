
#------------------------------
#setting for the optimizer
#-----------------------------

wdIntervalOptim = 5
mdistOpim = 1500  # 3481.5
min_dist = mdistOpim  # 5D spacing for 126m rotor diameter (reasonable wind farm spacing)

Ws_min = 3
Ws_max = 21.85

#--------------------------------------------------------
#
# NSGA-II (Non-dominated Sorting Genetic Algorithm - II)
# Global optimizer
#
#
# Pymoo: Multi-objective Optimization in Python
#
#--------------------------------------------------------
solver     ='NSGA2'
MAXGEN     =100   #Integer, max number of generations
PopSize    =32   #Population size a multiple of 4
pCross_real=0.9  #float,Probability of crossover(0.6-1.0)
eta_c      =10.0 #float,Distribution index for crossover(5.0-20.0)
eta_m      =50.0 #float,Distribution index for mutation(5.0-50.0)
ftol       =1E-08#Convergence Accurancy, 1E-06 <-> fast
#ftol=1E-08 <-> accurate 
#--------------------------------------------------------

# Additional parameters for hybrid optimization
nGenerations = MAXGEN  # Alias for compatibility
n_workers = 32  # Number of parallel workers
timeout_per_eval = 300  # Timeout in seconds per evaluation

# File paths (to be set based on actual directory structure)
FLORIS_CONFIG = './Input/config_Jensen_FLS_Validation_FastAEP.yaml'
inputLayoutFile = './Input/Initial.layout.csv'
windRoseFile = './Input/timeseries.txt'
#boundariesFile = './Input/Boundaries.csv'
boundariesFile = './Input/Boundaries-genPoints.csv'

# Smart external turbine filtering parameters
max_distance_km = 13 # 12  # Maximum distance for external turbine inclusion (km)

# Enhanced plotting and visualization parameters
plot_farm_analysis = True #  False  # Enable enhanced farm analysis plots
farm_sector_width = 90 #  90  # Angular width for directional sectors (degrees)
farm_grouping = 'auto'  # Farm grouping method: 'auto', 'by_name', 'by_cluster', 'individual'
analysis_directions = [0, 90, 180, 240, 270]  # Wind directions for analysis plots (degrees)

# External turbine configuration
use_external_turbines = True   # Enable external turbine loading and optimization
use_enhanced_filtering = True  # False  # Enable enhanced directional farm filtering (experimental)

# Optimization plotting parameters
plot_iterations = True  # Enable iteration plotting during optimization
iteration_interval = 2  # Plot every N generations (1 = every generation)

# Additional visualization parameters  
plot_layout_before_optim = True  # Plot initial layout before optimization
plot_external_comparison = True  # Plot comparison of internal vs external turbines
create_enhanced_plots = True  # Create enhanced visualization plots

#--------------------------------------------------------
# Multi-Framework Optimization Configuration
#--------------------------------------------------------

# Framework selection: 'pymoo', 'deap', 'pagmo2', 'hybrid'
optimization_framework = 'hybrid'

# Available algorithms per framework
pymoo_algorithms = ['NSGA2', 'NSGA3', 'GA', 'DE', 'PSO']
deap_algorithms = ['GA', 'NSGA2', 'NSGA3', 'DE', 'PSO', 'CMA-ES']
pagmo2_algorithms = ['DE', 'PSO', 'SADE', 'IRACE']  # Will be implemented later

# Hybrid sequence configuration (list of stages)
hybrid_sequence = [
    {'framework': 'pymoo', 'algorithm': 'GA', 'generations': 7},
    {'framework': 'deap', 'algorithm': 'DE', 'generations': 8}, 
    {'framework': 'pymoo', 'algorithm': 'PSO', 'generations': 5}
]

# Alternative single-framework configurations
selected_algorithm = 'GA'  # Used when optimization_framework is not 'hybrid'

#--------------------------------------------------------
# DEAP-Specific Extended Configuration
#--------------------------------------------------------

# Core DEAP algorithms with their configurations
deap_algorithms_config = {
    'GA': {
        'description': 'Genetic Algorithm',
        'variant': 'standard',  # 'standard', 'steady_state', 'generational'
        'default_params': {
            'pop_size': 32,
            'n_gen': 10,
            'crossover_prob': 0.9,
            'mutation_prob': 0.1,
            'tournament_size': 2,
            'eta_c': 10.0,  # Crossover distribution index
            'eta_m': 50.0   # Mutation distribution index
        }
    },
    'DE': {
        'description': 'Differential Evolution',
        'variant': 'rand/1/bin',  # 'rand/1/bin', 'best/1/bin', 'current-to-best/1'
        'default_params': {
            'pop_size': 32,
            'n_gen': 10,
            'de_F': 0.5,    # Differential weight
            'de_CR': 0.7    # Crossover rate
        }
    },
    'PSO': {
        'description': 'Particle Swarm Optimization',
        'variant': 'standard',  # 'standard', 'canonical', 'adaptive'
        'default_params': {
            'pop_size': 32,
            'n_gen': 10,
            'pso_w': 0.5,    # Inertia weight
            'pso_c1': 2.0,   # Cognitive coefficient
            'pso_c2': 2.0,   # Social coefficient
            'pso_smin': -0.1, # Min velocity
            'pso_smax': 0.1   # Max velocity
        }
    },
    'CMA-ES': {
        'description': 'Covariance Matrix Adaptation Evolution Strategy',
        'variant': 'standard',  # 'standard', 'bi-pop', 'active'
        'default_params': {
            'pop_size': None,     # Auto-calculated
            'n_gen': 20,
            'cmaes_sigma': 0.1,   # Initial step size
            'cmaes_lambda': None  # Population size (auto if None)
        }
    },
    'NSGA-II': {
        'description': 'Non-dominated Sorting Genetic Algorithm II',
        'variant': 'standard',
        'default_params': {
            'pop_size': 32,
            'n_gen': 10,
            'crossover_prob': 0.9,
            'mutation_prob': 0.1,
            'crowding_factor': 2.0
        }
    },
    'NSGA-III': {
        'description': 'Non-dominated Sorting Genetic Algorithm III',
        'variant': 'standard',
        'default_params': {
            'pop_size': 32,
            'n_gen': 10,
            'crossover_prob': 0.9,
            'mutation_prob': 0.1,
            'nsga3_ref_points': 12  # Reference points per objective
        }
    },
    'SPEA2': {
        'description': 'Strength Pareto Evolutionary Algorithm 2',
        'variant': 'standard',
        'default_params': {
            'pop_size': 32,
            'n_gen': 10,
            'crossover_prob': 0.9,
            'mutation_prob': 0.1,
            'archive_size': 32
        }
    },
    'ES': {
        'description': 'Evolution Strategies',
        'variant': '(μ,λ)',  # '(μ,λ)', '(μ+λ)', 'one_fifth_rule'
        'default_params': {
            'pop_size': 32,    # λ (offspring size)
            'n_gen': 10,
            'mu_ratio': 0.25,  # μ/λ ratio (parent size / offspring size)
            'mutation_prob': 1.0
        }
    }
}

# Genetic operators configuration
deap_operators_config = {
    'selection': {
        'tournament': {
            'params': {'tournament_size': 2},
            'description': 'Tournament selection'
        },
        'roulette': {
            'params': {},
            'description': 'Roulette wheel selection'
        },
        'nsga2': {
            'params': {},
            'description': 'NSGA-II selection'
        },
        'nsga3': {
            'params': {'nsga3_ref_points': 12},
            'description': 'NSGA-III selection with reference points'
        },
        'spea2': {
            'params': {},
            'description': 'SPEA2 selection'
        },
        'lexicase': {
            'params': {},
            'description': 'Lexicase selection'
        }
    },
    'crossover': {
        'blend': {
            'params': {'alpha': 0.5},
            'description': 'Blend crossover (BLX-α)'
        },
        'sbx': {
            'params': {'eta_c': 10.0},
            'description': 'Simulated Binary Crossover'
        },
        'uniform': {
            'params': {'indpb': 0.5},
            'description': 'Uniform crossover'
        },
        'onepoint': {
            'params': {},
            'description': 'One-point crossover'
        },
        'twopoint': {
            'params': {},
            'description': 'Two-point crossover'
        },
        'arithmetic': {
            'params': {'alpha': 0.5},
            'description': 'Arithmetic crossover'
        }
    },
    'mutation': {
        'gaussian': {
            'params': {'mu': 0, 'sigma': 0.1, 'indpb': 0.1},
            'description': 'Gaussian mutation'
        },
        'polynomial': {
            'params': {'eta_m': 50.0, 'indpb': 0.1},
            'description': 'Polynomial bounded mutation'
        },
        'uniform': {
            'params': {'low': 0.0, 'up': 1.0, 'indpb': 0.1},
            'description': 'Uniform mutation'
        },
        'es_lognormal': {
            'params': {'tau': 0.1, 'indpb': 0.1},
            'description': 'Evolution Strategies log-normal mutation'
        }
    }
}

# Enhanced DEAP configuration
deap_config = {
    # Parallel processing
    'use_scoop': False,  # Use SCOOP for distributed computing (requires scoop install)
    'parallel_mode': 'multiprocessing',  # 'multiprocessing', 'scoop'
    'n_workers': None,   # Auto-detect if None
    'timeout_per_eval': 300,  # Timeout in seconds per evaluation
    
    # Algorithm selection and configuration
    'algorithm': 'GA',   # Default algorithm
    'variant': 'standard',  # Algorithm variant
    'auto_configure': True,  # Use default parameters for algorithm
    
    # Population and generation settings
    'pop_size': 32,      # Population size
    'n_gen': 10,         # Number of generations
    
    # Operator configuration
    'selection': 'tournament',   # Selection method
    'crossover': 'blend',        # Crossover method
    'mutation': 'gaussian',      # Mutation method
    
    # Operator parameters
    'crossover_prob': 0.9,       # Crossover probability
    'mutation_prob': 0.1,        # Mutation probability
    'tournament_size': 2,        # Tournament selection size
    'eta_c': 10.0,              # Crossover distribution index
    'eta_m': 50.0,              # Mutation distribution index
    
    # Differential Evolution parameters
    'de_F': 0.5,                # Differential weight
    'de_CR': 0.7,               # Crossover rate
    
    # PSO parameters
    'pso_w': 0.5,               # Inertia weight
    'pso_c1': 2.0,              # Cognitive coefficient
    'pso_c2': 2.0,              # Social coefficient
    'pso_smin': -0.1,           # Minimum velocity
    'pso_smax': 0.1,            # Maximum velocity
    
    # CMA-ES parameters
    'cmaes_sigma': 0.1,         # Initial step size
    'cmaes_lambda': None,       # Population size (auto if None)
    
    # Multi-objective parameters
    'nsga3_ref_points': 12,     # Reference points per objective for NSGA-III
    'crowding_factor': 2.0,     # Crowding distance factor
    
    # Monitoring and logging
    'hall_of_fame_size': 10,    # Number of best solutions to keep
    'track_statistics': ['min', 'max', 'avg', 'std'],  # Statistics to track
    'verbose': True,            # Print progress information
    'log_frequency': 1,         # Print stats every N generations (1 = every generation)
    
    # Advanced features
    'enable_caching': True,     # Enable evaluation caching
    'adaptive_parameters': False,  # Enable adaptive parameter control
    'constraint_handling': 'penalty',  # 'penalty', 'death_penalty', 'feasibility_rules'
    'diversity_maintenance': True,      # Enable diversity maintenance mechanisms
}

# Algorithm comparison and benchmarking configuration  
deap_comparison_config = {
    #'algorithms_to_compare': ['GA', 'DE', 'PSO', 'CMA-ES'],  # Algorithms for comparison
    'algorithms_to_compare': ['GA', 'CMA-ES'],  # Algorithms for comparison
    'runs_per_algorithm': 3,                                 # Statistical runs per algorithm
    'enable_convergence_analysis': True,                     # Analyze convergence behavior
    'save_detailed_results': True,                           # Save comprehensive results
    'create_comparison_plots': True,                         # Generate comparison visualizations
    'statistical_tests': True,                               # Perform statistical significance tests
}

# Hybrid optimization sequences
deap_hybrid_sequences = {
    'exploration_exploitation': [
        {'algorithm': 'DE', 'generations': 15, 'purpose': 'exploration'},
        {'algorithm': 'GA', 'generations': 10, 'purpose': 'exploitation'},
        {'algorithm': 'PSO', 'generations': 5, 'purpose': 'fine-tuning'}
    ],
    'diverse_search': [
        {'algorithm': 'GA', 'generations': 8},
        {'algorithm': 'PSO', 'generations': 7}, 
        {'algorithm': 'DE', 'generations': 8},
        {'algorithm': 'CMA-ES', 'generations': 7}
    ],
    'fast_convergence': [
        {'algorithm': 'PSO', 'generations': 20},
        {'algorithm': 'CMA-ES', 'generations': 10}
    ],
    'robust_search': [
        {'algorithm': 'NSGA-II', 'generations': 15},
        {'algorithm': 'GA', 'generations': 15}
    ]
}

#--------------------------------------------------------
# Framework Comparison and Benchmarking
#--------------------------------------------------------

# Framework performance comparison
enable_framework_comparison = False  # Run multiple frameworks and compare
comparison_frameworks = ['pymoo', 'deap']  # Frameworks to compare
comparison_algorithms = ['GA', 'DE']       # Algorithms to compare
comparison_runs = 3                        # Number of runs per combination

# Benchmarking configuration
benchmark_config = {
    'save_results': True,           # Save detailed benchmark results
    'statistical_tests': True,     # Perform statistical significance tests
    'convergence_analysis': True,   # Analyze convergence behavior
    'resource_monitoring': True,   # Monitor CPU/memory usage
}

#--------------------------------------------------------
# Advanced Parallel Configuration
#--------------------------------------------------------

# Parallel strategy: 'evaluation', 'population', 'framework', 'hybrid'
parallel_strategy = 'evaluation'

# Population-level parallelization (for archipelago methods)
archipelago_config = {
    'num_islands': 4,              # Number of parallel islands
    'migration_frequency': 10,     # Migrate every N generations
    'migration_rate': 0.1,         # Fraction of population to migrate
    'topology': 'ring',            # Migration topology: 'ring', 'star', 'full'
}

# Framework-level parallelization
framework_parallel_config = {
    'run_frameworks_parallel': False,  # Run multiple frameworks simultaneously
    'shared_evaluation_cache': True,   # Share evaluation cache between frameworks
    'result_aggregation': 'best',      # How to combine results: 'best', 'pareto', 'ensemble'
}

