#!/usr/bin/env python3
"""
Smart Initialization Strategies for Wind Farm Layout Optimization
Using your established ProcessPoolExecutor parallelization patterns
"""

import numpy as np
import pandas as pd
from scipy.spatial import Voronoi, voronoi_plot_2d
from scipy.spatial.distance import cdist
from shapely.geometry import Point, Polygon, LineString
from concurrent.futures import ProcessPoolExecutor, as_completed, TimeoutError
import matplotlib.pyplot as plt
import time
import warnings
warnings.filterwarnings('ignore')

# Import wind direction data
import wind_dir_dominant as winddir


class SmartInitializationManager:
    """
    Advanced initialization methods using your ProcessPoolExecutor parallelization patterns
    """
    
    def __init__(self, n_workers=32, timeout_per_eval=300, use_monitoring=True):
        """
        Initialize using your established parameter patterns
        
        Args:
            n_workers: Number of parallel workers (your standard)
            timeout_per_eval: Timeout per evaluation (your standard)
            use_monitoring: Enable performance monitoring (your standard)
        """
        self.n_workers = n_workers
        self.timeout_per_eval = timeout_per_eval
        self.use_monitoring = use_monitoring
        
        # Initialize monitoring following your pattern
        if self.use_monitoring:
            self.monitor_data = {
                'eval_times': [],
                'cache_hits': 0,
                'cache_misses': 0,
                'timeouts': 0,
                'initialization_stats': {}
            }
    
    def voronoi_initialization_with_external(self, boundary, internal_positions, external_positions, 
                                           turbines_weights, min_distance, n_layouts=10):
        """
        Voronoi tessellation initialization respecting your external turbine framework
        Only optimizes internal turbines, keeps external turbines fixed
        
        Args:
            boundary: Site boundary polygon
            internal_positions: Current internal turbine positions
            external_positions: Fixed external turbine positions  
            turbines_weights: Your weight array (1=internal, 0=external)
            min_distance: Minimum turbine spacing
            n_layouts: Number of diverse layouts to generate
        """
        print(f"🔄 Generating {n_layouts} Voronoi-based layouts...")
        
        def _safe_voronoi_generation_external(seed):
            """Follow your _safe_obj_func pattern for external turbines"""
            try:
                np.random.seed(seed)
                
                # Get internal turbine indices
                weights = np.array(turbines_weights)
                internal_indices = np.where(weights > 0)[0]
                external_indices = np.where(weights == 0)[0]
                
                # Generate random seed points within boundary for Voronoi cells
                n_seeds = len(internal_indices) * 2  # More seeds than turbines for better coverage
                
                # Convert boundary to polygon if needed
                if isinstance(boundary, list):
                    boundary_poly = Polygon(boundary)
                else:
                    boundary_poly = boundary
                
                # Generate random points within boundary
                minx, miny, maxx, maxy = boundary_poly.bounds
                seed_points = []
                attempts = 0
                max_attempts = n_seeds * 10
                
                while len(seed_points) < n_seeds and attempts < max_attempts:
                    x = np.random.uniform(minx, maxx)
                    y = np.random.uniform(miny, maxy)
                    point = Point(x, y)
                    
                    if boundary_poly.contains(point):
                        seed_points.append([x, y])
                    attempts += 1
                
                if len(seed_points) < n_seeds:
                    print(f"Warning: Only generated {len(seed_points)} seed points out of {n_seeds}")
                
                # Create Voronoi diagram
                seed_points = np.array(seed_points)
                if len(seed_points) < 4:  # Need minimum points for Voronoi
                    raise ValueError("Insufficient seed points for Voronoi diagram")
                
                vor = Voronoi(seed_points)
                
                # Extract Voronoi cell centroids within boundary
                centroids = []
                for i, region in enumerate(vor.regions):
                    if len(region) > 0 and -1 not in region:  # Valid finite region
                        # Calculate centroid of Voronoi cell
                        vertices = vor.vertices[region]
                        if len(vertices) > 2:  # Valid polygon
                            cell_poly = Polygon(vertices)
                            if boundary_poly.intersects(cell_poly):
                                # Get intersection centroid
                                intersection = boundary_poly.intersection(cell_poly)
                                if hasattr(intersection, 'centroid'):
                                    cx, cy = intersection.centroid.x, intersection.centroid.y
                                    centroids.append([cx, cy])
                
                # Select best centroids for internal turbines
                centroids = np.array(centroids)
                if len(centroids) < len(internal_indices):
                    # Fill remaining with random points
                    needed = len(internal_indices) - len(centroids)
                    additional_points = self._generate_random_points_in_boundary(boundary_poly, needed)
                    centroids = np.vstack([centroids, additional_points])
                
                # Select subset of centroids for turbines
                selected_centroids = centroids[:len(internal_indices)]
                
                # Apply minimum distance constraints
                optimized_positions = self._apply_minimum_distance_constraints(
                    selected_centroids, external_positions, min_distance, boundary_poly
                )
                
                # Create full layout: optimized internal + fixed external
                full_layout_x = np.zeros(len(internal_indices) + len(external_indices))
                full_layout_y = np.zeros(len(internal_indices) + len(external_indices))
                
                # Place optimized internal turbines
                for i, turb_idx in enumerate(internal_indices):
                    full_layout_x[turb_idx] = optimized_positions[i, 0]
                    full_layout_y[turb_idx] = optimized_positions[i, 1]
                
                # Keep external turbines fixed
                for i, turb_idx in enumerate(external_indices):
                    full_layout_x[turb_idx] = external_positions[i, 0]
                    full_layout_y[turb_idx] = external_positions[i, 1]
                
                return full_layout_x, full_layout_y
                
            except Exception as e:
                print(f"Warning: Voronoi generation failed: {e}")
                # Fallback to random layout
                return self._random_layout_with_external(boundary, internal_positions, external_positions, 
                                                       turbines_weights, min_distance)
        
        # Use your ProcessPoolExecutor pattern
        if self.use_monitoring:
            start_time = time.time()
        
        try:
            with ProcessPoolExecutor(max_workers=min(self.n_workers, n_layouts)) as executor:
                futures = [executor.submit(_safe_voronoi_generation_external, seed) for seed in range(n_layouts)]
                
                layouts = []
                for future in as_completed(futures, timeout=self.timeout_per_eval):
                    try:
                        layout_x, layout_y = future.result()
                        layouts.append((layout_x, layout_y))
                    except Exception as e:
                        print(f"Warning: Voronoi layout generation timeout: {e}")
                        # Add fallback layout
                        fallback = self._random_layout_with_external(boundary, internal_positions, 
                                                                   external_positions, turbines_weights, min_distance)
                        layouts.append(fallback)
                
                if self.use_monitoring:
                    eval_time = time.time() - start_time
                    self.monitor_data['eval_times'].append(eval_time)
                    self.monitor_data['initialization_stats']['voronoi'] = {
                        'n_layouts': len(layouts),
                        'time': eval_time,
                        'success_rate': len(layouts) / n_layouts
                    }
                
                print(f"✅ Generated {len(layouts)} Voronoi layouts in {eval_time:.1f}s")
                return layouts
                
        except Exception as e:
            print(f"Warning: Parallel Voronoi generation failed: {e}")
            # Fallback to serial generation
            layouts = []
            for seed in range(n_layouts):
                layout = _safe_voronoi_generation_external(seed)
                layouts.append(layout)
            return layouts
    
    def wind_rose_weighted_initialization(self, wind_data, boundary, internal_positions, external_positions,
                                        turbines_weights, min_distance, n_layouts=5):
        """
        Wind-rose weighted grid initialization using your monitoring framework
        Aligns internal turbines with wind patterns, keeps external turbines fixed
        """
        print(f"🔄 Generating {n_layouts} wind-rose weighted layouts...")
        
        if self.use_monitoring:
            start_time = time.time()
        
        # Use dominant wind direction from wind_dir_dominant.py (240°)
        dominant_wd = winddir.wind_dir_dominant
        
        def _safe_wind_rose_generation(seed):
            """Generate wind-optimized layout"""
            try:
                np.random.seed(seed)
                
                # Get internal turbine indices
                weights = np.array(turbines_weights)
                internal_indices = np.where(weights > 0)[0]
                external_indices = np.where(weights == 0)[0]
                
                # Convert boundary to polygon
                if isinstance(boundary, list):
                    boundary_poly = Polygon(boundary)
                else:
                    boundary_poly = boundary
                
                # Create grid aligned perpendicular to dominant wind
                # Wind direction 240° means wind comes from SW (240°), so align turbines NW-SE
                grid_angle = dominant_wd + 90  # Perpendicular to wind direction
                grid_angle_rad = np.radians(grid_angle)
                
                # Calculate grid spacing
                minx, miny, maxx, maxy = boundary_poly.bounds
                site_width = maxx - minx
                site_height = maxy - miny
                
                # Grid spacing based on minimum distance
                grid_spacing = min_distance * 1.5  # 50% larger than minimum for flexibility
                
                # Calculate number of grid points
                n_x = int(site_width / grid_spacing) + 1
                n_y = int(site_height / grid_spacing) + 1
                
                # Generate grid points
                x_coords = np.linspace(minx, maxx, n_x)
                y_coords = np.linspace(miny, maxy, n_y)
                
                # Rotate grid to align with wind
                grid_points = []
                center_x, center_y = (minx + maxx) / 2, (miny + maxy) / 2
                
                for x in x_coords:
                    for y in y_coords:
                        # Translate to origin
                        x_rel, y_rel = x - center_x, y - center_y
                        
                        # Rotate
                        x_rot = x_rel * np.cos(grid_angle_rad) - y_rel * np.sin(grid_angle_rad)
                        y_rot = x_rel * np.sin(grid_angle_rad) + y_rel * np.cos(grid_angle_rad)
                        
                        # Translate back
                        x_final, y_final = x_rot + center_x, y_rot + center_y
                        
                        # Check if point is within boundary
                        point = Point(x_final, y_final)
                        if boundary_poly.contains(point):
                            grid_points.append([x_final, y_final])
                
                grid_points = np.array(grid_points)
                
                # Weight grid points by wind energy potential
                weighted_points = self._calculate_wind_energy_weights(grid_points, wind_data, external_positions)
                
                # Select best points for internal turbines
                n_internal = len(internal_indices)
                if len(weighted_points) >= n_internal:
                    # Sort by weight and select top points
                    sorted_indices = np.argsort(weighted_points[:, 2])[::-1]  # Descending order
                    selected_points = weighted_points[sorted_indices[:n_internal], :2]
                else:
                    # Use all points and fill with random
                    selected_points = weighted_points[:, :2]
                    needed = n_internal - len(selected_points)
                    additional = self._generate_random_points_in_boundary(boundary_poly, needed)
                    selected_points = np.vstack([selected_points, additional])
                
                # Apply minimum distance constraints
                optimized_positions = self._apply_minimum_distance_constraints(
                    selected_points, external_positions, min_distance, boundary_poly
                )
                
                # Create full layout
                full_layout_x = np.zeros(len(internal_indices) + len(external_indices))
                full_layout_y = np.zeros(len(internal_indices) + len(external_indices))
                
                # Place optimized internal turbines
                for i, turb_idx in enumerate(internal_indices):
                    full_layout_x[turb_idx] = optimized_positions[i, 0]
                    full_layout_y[turb_idx] = optimized_positions[i, 1]
                
                # Keep external turbines fixed
                for i, turb_idx in enumerate(external_indices):
                    full_layout_x[turb_idx] = external_positions[i, 0]
                    full_layout_y[turb_idx] = external_positions[i, 1]
                
                return full_layout_x, full_layout_y
                
            except Exception as e:
                print(f"Warning: Wind-rose generation failed: {e}")
                return self._random_layout_with_external(boundary, internal_positions, external_positions,
                                                       turbines_weights, min_distance)
        
        # Generate layouts in parallel using your pattern
        try:
            with ProcessPoolExecutor(max_workers=min(self.n_workers, n_layouts)) as executor:
                futures = [executor.submit(_safe_wind_rose_generation, seed) for seed in range(n_layouts)]
                
                layouts = []
                for future in as_completed(futures, timeout=self.timeout_per_eval):
                    try:
                        layout = future.result()
                        layouts.append(layout)
                    except Exception as e:
                        print(f"Warning: Wind-rose layout timeout: {e}")
                
                if self.use_monitoring:
                    eval_time = time.time() - start_time
                    self.monitor_data['eval_times'].append(eval_time)
                    self.monitor_data['initialization_stats']['wind_rose'] = {
                        'n_layouts': len(layouts),
                        'time': eval_time,
                        'dominant_wind': dominant_wd
                    }
                
                print(f"✅ Generated {len(layouts)} wind-rose layouts in {eval_time:.1f}s")
                return layouts
                
        except Exception as e:
            print(f"Warning: Parallel wind-rose generation failed: {e}")
            layouts = []
            for seed in range(n_layouts):
                layout = _safe_wind_rose_generation(seed)
                layouts.append(layout)
            return layouts
    
    def dominant_wind_alignment_initialization(self, boundary, internal_positions, external_positions,
                                             turbines_weights, min_distance, n_layouts=5):
        """
        Dominant wind alignment initialization (240° from wind_dir_dominant.py)
        Creates staggered rows perpendicular to dominant wind direction
        """
        print(f"🔄 Generating {n_layouts} dominant wind aligned layouts...")
        
        dominant_wd = winddir.wind_dir_dominant  # 240°
        
        def _safe_dominant_wind_generation(seed):
            """Generate layout aligned with dominant wind"""
            try:
                np.random.seed(seed)
                
                # Get internal turbine indices
                weights = np.array(turbines_weights)
                internal_indices = np.where(weights > 0)[0]
                external_indices = np.where(weights == 0)[0]
                
                # Convert boundary to polygon
                if isinstance(boundary, list):
                    boundary_poly = Polygon(boundary)
                else:
                    boundary_poly = boundary
                
                # Create rows perpendicular to dominant wind (240°)
                # Rows run in direction 240° + 90° = 330° (NW-SE)
                row_angle = dominant_wd + 90
                row_angle_rad = np.radians(row_angle)
                
                # Calculate site dimensions
                minx, miny, maxx, maxy = boundary_poly.bounds
                
                # Row spacing (distance between rows in wind direction)
                row_spacing = min_distance * 8  # 8D spacing in wind direction
                
                # Turbine spacing within rows
                turbine_spacing = min_distance * 3  # 3D spacing within rows
                
                # Calculate number of rows needed
                site_depth = np.sqrt((maxx - minx)**2 + (maxy - miny)**2)
                n_rows = max(1, int(site_depth / row_spacing))
                
                # Generate row positions
                positions = []
                n_internal = len(internal_indices)
                turbines_per_row = max(1, n_internal // n_rows)
                
                for row_idx in range(n_rows):
                    if len(positions) >= n_internal:
                        break
                    
                    # Calculate row center line
                    row_offset = row_idx * row_spacing
                    
                    # Generate turbine positions along row
                    for turb_idx in range(turbines_per_row):
                        if len(positions) >= n_internal:
                            break
                        
                        # Position along row
                        turb_offset = (turb_idx - turbines_per_row/2) * turbine_spacing
                        
                        # Add staggering for alternate rows
                        if row_idx % 2 == 1:
                            turb_offset += turbine_spacing / 2
                        
                        # Calculate position in site coordinates
                        x_local = turb_offset * np.cos(row_angle_rad) - row_offset * np.sin(row_angle_rad)
                        y_local = turb_offset * np.sin(row_angle_rad) + row_offset * np.cos(row_angle_rad)
                        
                        # Translate to site center
                        center_x, center_y = (minx + maxx) / 2, (miny + maxy) / 2
                        x_final = center_x + x_local
                        y_final = center_y + y_local
                        
                        # Check if position is within boundary
                        point = Point(x_final, y_final)
                        if boundary_poly.contains(point):
                            positions.append([x_final, y_final])
                
                # Fill remaining positions if needed
                while len(positions) < n_internal:
                    additional = self._generate_random_points_in_boundary(boundary_poly, 1)
                    positions.extend(additional)
                
                positions = np.array(positions[:n_internal])
                
                # Apply minimum distance constraints
                optimized_positions = self._apply_minimum_distance_constraints(
                    positions, external_positions, min_distance, boundary_poly
                )
                
                # Create full layout
                full_layout_x = np.zeros(len(internal_indices) + len(external_indices))
                full_layout_y = np.zeros(len(internal_indices) + len(external_indices))
                
                # Place optimized internal turbines
                for i, turb_idx in enumerate(internal_indices):
                    full_layout_x[turb_idx] = optimized_positions[i, 0]
                    full_layout_y[turb_idx] = optimized_positions[i, 1]
                
                # Keep external turbines fixed
                for i, turb_idx in enumerate(external_indices):
                    full_layout_x[turb_idx] = external_positions[i, 0]
                    full_layout_y[turb_idx] = external_positions[i, 1]
                
                return full_layout_x, full_layout_y
                
            except Exception as e:
                print(f"Warning: Dominant wind alignment failed: {e}")
                return self._random_layout_with_external(boundary, internal_positions, external_positions,
                                                       turbines_weights, min_distance)
        
        # Generate layouts
        layouts = []
        for seed in range(n_layouts):
            layout = _safe_dominant_wind_generation(seed)
            layouts.append(layout)
        
        print(f"✅ Generated {n_layouts} dominant wind aligned layouts")
        return layouts
    
    def _calculate_wind_energy_weights(self, grid_points, wind_data, external_positions):
        """Calculate wind energy potential weights for grid points"""
        try:
            # Simple wind energy weighting based on:
            # 1. Distance from external turbines (avoid wake shadows)
            # 2. Exposure to dominant wind direction
            
            weights = np.ones(len(grid_points))
            
            # Penalize points in wake shadows of external turbines
            if len(external_positions) > 0:
                for i, point in enumerate(grid_points):
                    # Calculate minimum distance to external turbines
                    distances = np.sqrt(np.sum((external_positions - point)**2, axis=1))
                    min_distance = np.min(distances)
                    
                    # Apply penalty based on distance (closer = more penalty)
                    weight_factor = min(1.0, min_distance / (5 * 126))  # Assume 126m rotor diameter
                    weights[i] *= weight_factor
            
            # Combine points with weights
            weighted_points = np.column_stack([grid_points, weights])
            return weighted_points
            
        except Exception as e:
            print(f"Warning: Wind energy weighting failed: {e}")
            # Return equal weights
            weights = np.ones(len(grid_points))
            return np.column_stack([grid_points, weights])
    
    def _apply_minimum_distance_constraints(self, positions, external_positions, min_distance, boundary_poly):
        """Apply minimum distance constraints to positions"""
        try:
            optimized_positions = positions.copy()
            max_iterations = 100
            
            for iteration in range(max_iterations):
                # Check distances between internal turbines
                distances = cdist(optimized_positions, optimized_positions)
                np.fill_diagonal(distances, np.inf)  # Ignore self-distances
                
                # Check distances to external turbines
                if len(external_positions) > 0:
                    external_distances = cdist(optimized_positions, external_positions)
                    min_external_dist = np.min(external_distances, axis=1)
                else:
                    min_external_dist = np.full(len(optimized_positions), np.inf)
                
                # Find violations
                violations = []
                for i in range(len(optimized_positions)):
                    # Check internal distances
                    min_internal_dist = np.min(distances[i])
                    if min_internal_dist < min_distance:
                        violations.append(i)
                    # Check external distances
                    elif min_external_dist[i] < min_distance:
                        violations.append(i)
                
                if not violations:
                    break  # No violations, we're done
                
                # Move violating turbines
                for i in violations:
                    # Try random perturbation
                    for _ in range(10):  # Limited attempts
                        perturbation = np.random.uniform(-min_distance, min_distance, 2)
                        new_pos = optimized_positions[i] + perturbation
                        
                        # Check if new position is within boundary
                        if boundary_poly.contains(Point(new_pos)):
                            optimized_positions[i] = new_pos
                            break
            
            return optimized_positions
            
        except Exception as e:
            print(f"Warning: Constraint application failed: {e}")
            return positions
    
    def _generate_random_points_in_boundary(self, boundary_poly, n_points):
        """Generate random points within boundary polygon"""
        points = []
        minx, miny, maxx, maxy = boundary_poly.bounds
        
        attempts = 0
        max_attempts = n_points * 50
        
        while len(points) < n_points and attempts < max_attempts:
            x = np.random.uniform(minx, maxx)
            y = np.random.uniform(miny, maxy)
            point = Point(x, y)
            
            if boundary_poly.contains(point):
                points.append([x, y])
            attempts += 1
        
        # Fill remaining with boundary centroid if needed
        while len(points) < n_points:
            center = boundary_poly.centroid
            points.append([center.x, center.y])
        
        return np.array(points)
    
    def _random_layout_with_external(self, boundary, internal_positions, external_positions, 
                                   turbines_weights, min_distance):
        """Fallback random layout generation"""
        try:
            weights = np.array(turbines_weights)
            internal_indices = np.where(weights > 0)[0]
            external_indices = np.where(weights == 0)[0]
            
            if isinstance(boundary, list):
                boundary_poly = Polygon(boundary)
            else:
                boundary_poly = boundary
            
            # Generate random positions for internal turbines
            random_positions = self._generate_random_points_in_boundary(boundary_poly, len(internal_indices))
            
            # Create full layout
            full_layout_x = np.zeros(len(internal_indices) + len(external_indices))
            full_layout_y = np.zeros(len(internal_indices) + len(external_indices))
            
            # Place random internal turbines
            for i, turb_idx in enumerate(internal_indices):
                full_layout_x[turb_idx] = random_positions[i, 0]
                full_layout_y[turb_idx] = random_positions[i, 1]
            
            # Keep external turbines fixed
            for i, turb_idx in enumerate(external_indices):
                full_layout_x[turb_idx] = external_positions[i, 0]
                full_layout_y[turb_idx] = external_positions[i, 1]
            
            return full_layout_x, full_layout_y
            
        except Exception as e:
            print(f"Warning: Random layout generation failed: {e}")
            # Return original positions
            return internal_positions[:, 0], internal_positions[:, 1]
    
    def get_monitoring_stats(self):
        """Get monitoring statistics following your pattern"""
        if not self.use_monitoring:
            return "Monitoring disabled"
        
        return {
            'total_evaluations': len(self.monitor_data['eval_times']),
            'avg_eval_time': np.mean(self.monitor_data['eval_times']) if self.monitor_data['eval_times'] else 0,
            'cache_hit_rate': self.monitor_data['cache_hits'] / max(1, self.monitor_data['cache_hits'] + self.monitor_data['cache_misses']),
            'timeouts': self.monitor_data['timeouts'],
            'initialization_stats': self.monitor_data['initialization_stats']
        }