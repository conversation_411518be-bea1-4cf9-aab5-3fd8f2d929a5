#!/usr/bin/env python3
"""
SMART External layout optimization with wind direction-based filtering
Based on opt-pymoo-windrose-freq-ts-external_corrected.py with performance optimizations
Only includes external turbines that are upwind and within 20km for each wind direction
"""

import inspect
import sys
import copy
import os
import re
import shutil
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from multiprocessing import Process
from numpy import genfromtxt
from scipy.spatial.distance import cdist
from scipy.interpolate import NearestNDInterpolator
from datetime import datetime
from time import perf_counter as timerpc
from shapely.geometry import Polygon, LineString
import matplotlib.ticker as ticker
import matplotlib.patches as patches
from matplotlib.lines import Line2D
from pathlib import Path

# Optimization imports
from pymoo.algorithms.moo.nsga2 import NSGA2
from pymoo.core.crossover import Crossover
from pymoo.core.mutation import Mutation
from pymoo.operators.crossover.sbx import SBX
from pymoo.operators.mutation.pm import PolynomialMutation
from pymoo.operators.sampling.rnd import FloatRandomSampling
from pymoo.termination.default import DefaultSingleObjectiveTermination
from pymoo.termination.ftol import MultiObjectiveSpaceTermination
from pymoo.optimize import minimize

# Project-specific imports
import params as pr
import optimizer_params as optimizer_etc
import wind_dir_dominant as winddir

# Import core utilities (including new visualization functions)
from utilities import (
    sanitize_name,
    GetAEPParallel,
    memory_footprint,
    write_log,
    ts_to_freq_df,
    plot_layout_beforeOptim,
    plot_external_comparison,
    plot_external_comparison_enhanced,
    plot_iteration_solution,
    load_boundaries,
    process_multi_polygon_boundaries,
    create_enhanced_farm_manager,
    plot_enhanced_farm_analysis,
    plot_farm_sectors,
    plot_directional_coverage
)

# Set up paths - matches original exactly
print(f"RunFloris.py      Path: {os.getcwd()}")
workDir = str(Path('.').absolute())
floris_path = workDir + '/FLORIS_311_VF1_Operational'
turb_lib_path = workDir + '/FLORIS_311_VF1_Operational/core/turbine_library'
sys.path.append(floris_path)

# Import FLORIS after path setup
from core.tools import FlorisInterface
from core.tools.optimization.layout_optimization.LayoutOptimizationPymooExternal import EnhancedLayoutOptimizationPymoo

# Try to import parallel interface
try:
    from tools.parallel_tools.parallel_tools_MPI import ParFlorisInterface
    PARALLEL_AVAILABLE = True
except ImportError:
    print("Warning: ParFlorisInterface not available - using standard parallel methods")
    PARALLEL_AVAILABLE = False

# Track overall progress
OverAllProgress = workDir + '/OverAllProgress.txt'


class SmartExternalLayoutManager:
    """
    Manages smart filtering of external turbines based on wind direction and distance
    Only includes external turbines that can affect internal turbines for each wind direction
    """
    
    def __init__(self, internal_layout, external_layout, max_distance_km=20):
        """
        Initialize smart layout manager with distance pre-filtering
        
        Args:
            internal_layout: DataFrame with internal turbines (external=False)
            external_layout: DataFrame with external turbines (external=True)
            max_distance_km: Maximum distance to consider external turbines (default: 20km)
        """
        self.internal_layout = internal_layout.copy()
        self.external_layout = external_layout.copy()
        self.max_distance_m = max_distance_km * 1000
        
        # Calculate internal layout centroid
        self.internal_centroid = self._calculate_centroid(internal_layout)
        
        # Pre-filter external turbines by distance
        self.external_layout = self._filter_by_distance()
        
        # Pre-compute geometric relationships
        self._precompute_geometries()
        
        # Cache for filtered layouts per wind direction
        self.layout_cache = {}
        
        print(f"Smart Layout Manager initialized:")
        print(f"  Internal turbines: {len(self.internal_layout)}")
        print(f"  External turbines (total): {len(external_layout)}")
        print(f"  External turbines (within {max_distance_km}km): {len(self.external_layout)}")
        print(f"  Internal centroid: ({self.internal_centroid[0]:.0f}, {self.internal_centroid[1]:.0f})")
    
    def _calculate_centroid(self, layout):
        """Calculate centroid of turbine layout"""
        center_x = np.mean(layout['x'].values)
        center_y = np.mean(layout['y'].values)
        return (center_x, center_y)
    
    def _filter_by_distance(self):
        """Filter external turbines within max_distance from internal centroid"""
        # Handle empty external layout
        if len(self.external_layout) == 0:
            print("Distance filtering: No external turbines to filter")
            return pd.DataFrame()
        
        # Calculate distances from external turbines to internal centroid
        external_coords = self.external_layout[['x', 'y']].values
        distances = np.sqrt(
            (external_coords[:, 0] - self.internal_centroid[0])**2 + 
            (external_coords[:, 1] - self.internal_centroid[1])**2
        )
        
        # Filter turbines within distance threshold
        mask = distances <= self.max_distance_m
        filtered_external = self.external_layout[mask].copy()
        
        print(f"Distance filtering: {np.sum(~mask)} external turbines removed (>{self.max_distance_m/1000:.0f}km)")
        
        return filtered_external
    
    def _precompute_geometries(self):
        """Pre-compute geometric relationships for efficiency"""
        # Handle empty external layout
        if len(self.external_layout) == 0:
            self.external_coords = np.array([]).reshape(0, 2)
            self.vectors_to_internal = np.array([]).reshape(0, 2)
            self.unit_vectors_to_internal = np.array([]).reshape(0, 2)
            return
        
        # Calculate vectors from external turbines to internal centroid
        self.external_coords = self.external_layout[['x', 'y']].values
        self.vectors_to_internal = np.array([
            self.internal_centroid[0] - self.external_coords[:, 0],
            self.internal_centroid[1] - self.external_coords[:, 1]
        ]).T
        
        # Normalize vectors
        distances = np.linalg.norm(self.vectors_to_internal, axis=1)
        self.unit_vectors_to_internal = self.vectors_to_internal / distances[:, np.newaxis]
    
    def _is_upwind(self, wind_direction_deg):
        """
        Determine which external turbines are upwind for given wind direction
        
        Args:
            wind_direction_deg: Wind direction in degrees (0=North, 90=East, etc.)
            
        Returns:
            Boolean array indicating which external turbines are upwind
        """
        # Convert wind direction to radians and create unit vector
        # Note: Meteorological convention - direction wind is coming FROM
        # FIXME im not sure 
        #wind_rad = np.radians(270 - wind_direction_deg)  # Convert to mathematical angle
        #wind_vector = np.array([np.cos(wind_rad), np.sin(wind_rad)])
        # convert “from” wd to “towards” flow in (x-east, y-north)
        theta = np.radians(270 - wind_direction_deg)
        flow_vec = np.array([np.cos(theta), np.sin(theta)])

        # Calculate dot product to determine if external turbine is upwind
        # Positive dot product means external turbine is upwind of internal
        #dot_products = np.dot(self.unit_vectors_to_internal, wind_vector)
        
        # External turbine is upwind if dot product > 0
        # Add some tolerance for turbines roughly perpendicular to wind
        #upwind_mask = dot_products > -0.1  # Small negative tolerance
        
        #return upwind_mask
        rel = self.internal_centroid - self.external_coords
        rel_unit = rel / np.linalg.norm(rel, axis=1)[:, None]
        return (rel_unit @ flow_vec) > 0

    
    def get_relevant_layout(self, wind_direction, include_all=False):
        """
        Get combined layout with internal turbines and relevant external turbines
        
        Args:
            wind_direction: Wind direction in degrees
            include_all: If True, include all external turbines (for baseline comparison)
            
        Returns:
            DataFrame with combined layout and updated weights
        """
        # Check cache first
        cache_key = f"{wind_direction:.1f}"
        if not include_all and cache_key in self.layout_cache:
            return self.layout_cache[cache_key]
        
        if include_all:
            # Return all turbines (internal + filtered external)
            combined_layout = pd.concat([self.internal_layout, self.external_layout], 
                                      ignore_index=True)
        else:
            # Filter external turbines based on wind direction
            upwind_mask = self._is_upwind(wind_direction)
            relevant_external = self.external_layout[upwind_mask].copy()
            
            # Combine internal and relevant external turbines
            combined_layout = pd.concat([self.internal_layout, relevant_external], 
                                      ignore_index=True)
            
            # Cache the result
            self.layout_cache[cache_key] = combined_layout
            
            # Log filtering info periodically
            if len(self.layout_cache) <= 5 or wind_direction % 30 == 0:
                print(f"Wind direction {wind_direction}°: "
                      f"{len(relevant_external)}/{len(self.external_layout)} external turbines included")
        
        # Ensure weights are set correctly
        combined_layout.loc[combined_layout['external'] == False, 'weight'] = 1
        combined_layout.loc[combined_layout['external'] == True, 'weight'] = 0
        
        return combined_layout
    
    def get_cache_stats(self):
        """Get statistics about layout caching"""
        if not self.layout_cache:
            return "No cached layouts yet"
        
        cached_dirs = list(self.layout_cache.keys())
        external_counts = [len(layout[layout['external'] == True]) 
                          for layout in self.layout_cache.values()]
        
        return {
            'cached_directions': len(cached_dirs),
            'avg_external_turbines': np.mean(external_counts),
            'min_external_turbines': np.min(external_counts),
            'max_external_turbines': np.max(external_counts)
        }


# Helper functions from original
def find_closest_points(shape1, shape2):
    """Find the two closest points between two shapes."""
    shape1_points = shape1[['X', 'Y']].values
    shape2_points = shape2[['X', 'Y']].values
    
    min_dist = np.inf
    closest_pair = None
    
    for i, p1 in enumerate(shape1_points):
        for j, p2 in enumerate(shape2_points):
            dist = np.linalg.norm(p1 - p2)
            if dist < min_dist:
                min_dist = dist
                closest_pair = (shape1.iloc[i], shape2.iloc[j])
    
    return closest_pair


def concatenate_polygons(shape1, shape2, point1, point2):
    """Concatenate two polygons at their closest points."""
    points1 = list(zip(shape1['X'], shape1['Y']))
    points2 = list(zip(shape2['X'], shape2['Y']))
    
    start_idx1 = points1.index((point1['X'], point1['Y']))
    start_idx2 = points2.index((point2['X'], point2['Y']))
    
    connecting_line = [(point1['X'], point1['Y']), (point2['X'], point2['Y'])]
    
    concatenated_points = points1[:start_idx1] + connecting_line + points2[start_idx2:] + points2[:start_idx2] + connecting_line[::-1] + points1[start_idx1:]
    return concatenated_points


def run_layoutOpt(
        inputs_loc=f"{workDir}/Input/",
        a_file='a_par.txt',
        freq_file='freq_table.txt',
        k_file='k_par.txt',
        layout_file='layout_SB.csv',
        input_config='config_Jensen_FLS_Validation_FastAEP.yaml',
        logfile_name="mylogfile.txt",
        optLog="optim_logfile.txt",
        finaloptLog="finalOptim_logfile.txt",
        outputs_loc=f"{workDir}/Output/",
        shp_loc=f"{workDir}/shp/",
        parallel=True,
        PlotFlowfield=False,
        GeneratingTurbEffContourPlots=False,
        ref_ht=None,
        max_distance_km=None,
        plot_iterations=None,
        iteration_interval=None,
        plot_farm_analysis=None,
        farm_sector_width=None,
        farm_grouping=None,
        analysis_directions=None
    ):
    """
    SMART External layout optimization with wind direction-based filtering
    Handles both internal (optimizable) and external (fixed) turbines
    Only includes relevant external turbines per wind direction
    """
    print("========================================")
    print("SMART External Layout Optimization")
    print("========================================")
    
    # Read all parameters from params.py if not provided
    if max_distance_km is None:
        max_distance_km = pr.max_distance_km
    if plot_iterations is None:
        plot_iterations = pr.plot_iterations
    if iteration_interval is None:
        iteration_interval = pr.iteration_interval
    if plot_farm_analysis is None:
        plot_farm_analysis = pr.plot_farm_analysis
    if farm_sector_width is None:
        farm_sector_width = pr.farm_sector_width
    if farm_grouping is None:
        farm_grouping = pr.farm_grouping
    if analysis_directions is None:
        analysis_directions = pr.analysis_directions
    
    print("User given inputs:")
    print(f"inputs_loc: {inputs_loc}")
    print(f"outputs_loc: {outputs_loc}")
    print(f"layout_file: {layout_file}")
    print(f"input_config: {input_config}")
    print(f"logfile_name: {logfile_name}")
    print("Parameters from params.py:")
    print(f"  max_distance_km: {max_distance_km}")
    print(f"  plot_farm_analysis: {plot_farm_analysis}")
    print(f"  farm_sector_width: {farm_sector_width}°")
    print(f"  farm_grouping: {farm_grouping}")
    print(f"  plot_iterations: {plot_iterations}")
    print(f"  iteration_interval: {iteration_interval}")
    print("========================================")
    
    # Performance tracking
    start_total = timerpc()
    
    os.chdir(inputs_loc)
    
    # Setup turbine library
    if os.path.exists(outputs_loc+'turbine_library'):
        shutil.rmtree(outputs_loc+'turbine_library')
    shutil.copytree(turb_lib_path, outputs_loc+'turbine_library')
    
    # Setup logging
    log_loc = f'{outputs_loc}{logfile_name}'
    optLog = f'{outputs_loc}{optLog}'
    finaloptLog = f'{outputs_loc}{finaloptLog}'
    
    for log_file in [log_loc, optLog, finaloptLog]:
        if os.path.exists(log_file):
            with open(log_file, "w") as log:
                log.truncate()
    
    print("\n🔄 Starting SMART external layout optimization...")
    
    # Read and process wind data
    ts = pd.read_csv(inputs_loc + "timeseries.txt", sep=' ')
    
    # Process timestamp format properly
    if 'time' in ts.columns and ts.index.name is None:
        ts.index.name = 'date'
        ts.reset_index(inplace=True)
        ts['timestamp'] = pd.to_datetime(ts['date'].astype(str) + ' ' + ts['time'])
        ts = ts[['timestamp', 'wd', 'ws']]
    elif 'timestamp' not in ts.columns:
        if pd.api.types.is_datetime64_any_dtype(ts.index):
            ts['timestamp'] = ts.index
            ts.reset_index(drop=True, inplace=True)
        else:
            ts = ts[['time', 'wd', 'ws']].rename(columns={'time': 'timestamp'})
    
    WR = ts_to_freq_df(ts)
    WR['wd'] = pd.to_numeric(WR['wd'], errors='coerce')
    WR['ws'] = pd.to_numeric(WR['ws'], errors='coerce')
    print(f"Size of WR: {len(WR.index)}")
    
    # Extract wind data arrays - FIXED: Ensure proper frequency array formatting
    wd_array = np.array(sorted(WR["wd"].unique()), dtype=float)
    ws_array = np.array(sorted(WR["ws"].unique()), dtype=float)
    
    # Create frequency matrix that matches FLORIS interface expectations
    freq = WR.set_index(['wd','ws']).unstack().values
    
    # Initialize FLORIS interface
    fi = FlorisInterface(inputs_loc + input_config)
    
    # Load layout data - KEY DIFFERENCE: Load multiple files for external turbines
    from glob import glob
    csv_files = glob(f"{workDir}/Input/External.layout.csv") + glob(f"{workDir}/Input/Initial.layout.csv")
    
    extra_file_path = os.path.join(workDir, 'Input', 'ExtraExternal.csv')
    if os.path.exists(extra_file_path):
        csv_files.append(extra_file_path)
    
    # Load and filter valid dataframes
    df_list = [pd.read_csv(file) for file in csv_files]
    filtered_df_list = [df for df in df_list if not df.empty and not df.isna().all().all()]
    all_layout = pd.concat(filtered_df_list, ignore_index=True)
    
    # Prepare layout data
    all_layout = all_layout.rename(columns={'easting': 'x', 'northing': 'y'})
    all_layout['turb'] = all_layout['turb'].apply(sanitize_name)
    
    # Separate internal and external layouts
    internal_layout = all_layout[all_layout['external'] == False].copy()
    external_layout = all_layout[all_layout['external'] == True].copy()
    
    print(f"\nLayout statistics:")
    print(f"  Internal turbines: {len(internal_layout)}")
    print(f"  External turbines: {len(external_layout)}")
    
    # Initialize enhanced farm manager
    print(f"  🔧 Creating enhanced directional farm manager...")
    print(f"     Max distance: {max_distance_km} km")
    print(f"     Sector width: {farm_sector_width}°")
    print(f"     Farm grouping: {farm_grouping}")
    smart_manager = create_enhanced_farm_manager(
        internal_layout, external_layout, 
        max_distance_km=max_distance_km,
        sector_width=farm_sector_width,
        farm_grouping=farm_grouping
    )
    
    # CRITICAL FIX: Initialize FLORIS with internal turbines only for optimization
    print(f"🎯 Initializing FLORIS with internal turbines only:")
    print(f"🎯 Internal turbines for optimization: {len(internal_layout)}")
    print(f"🎯 External turbines (used for baseline only): {len(external_layout)}")
    
    fi.reinitialize(
        layout=(internal_layout['x'], internal_layout['y']),
        turbine_type=internal_layout['turb'].tolist(),
        wind_directions=wd_array,
        wind_speeds=ws_array
    )
    
    print(f"Number of wind directions: {fi.floris.flow_field.n_wind_directions}")
    print(f"Number of wind speeds: {fi.floris.flow_field.n_wind_speeds}")
    
    # Enhanced hub height logic to eliminate reference height warnings
    internal_turb = internal_layout['turb'].unique()
    if ref_ht is None:
        if len(internal_turb) > 1:
            hhs = []
            for i, turb in enumerate(internal_turb):
                hhs.append(fi.floris.farm.hub_heights[i])
            ref_ht = sum(hhs) / len(hhs)
            message = f'Multiple internal turbine types detected. Reference wind height set to average hub height: {ref_ht:.1f}m'
            print(f"   {message}")
            write_log(message, log_loc)
        else:
            internal_turb_idx = 0  # First turbine in internal layout
            ref_ht = fi.floris.farm.hub_heights[internal_turb_idx]
            message = f'Single internal turbine type detected. Reference wind height set to hub height: {ref_ht:.1f}m'
            print(f"   {message}")
            write_log(message, log_loc)

    fi.floris.flow_field.reference_wind_height = ref_ht
    print(f"   ✓ Reference wind height updated to {ref_ht:.1f}m")
    
    # Calculate no-wake conditions
    start_init = timerpc()
    fi.calculate_no_wake()
    print(f'calculate_no_wake runtime: {timerpc() - start_init:.2f} sec')
    
    # Load boundaries from params.py using enhanced processing
    # Use absolute path since we may be in a different working directory
    boundary_file_path = os.path.join(workDir, pr.boundariesFile.lstrip('./'))
    boundaries3 = load_boundaries(boundary_file_path)
    
    # Plot initial layout before optimization starts
    print("   Creating initial layout visualization...")
    # Use internal_layout coordinates for optimization visualization
    x0 = internal_layout['x'].values
    y0 = internal_layout['y'].values
    print("ploting ..")
    plot_layout_beforeOptim(x0, y0, boundaries3, os.path.join(inputs_loc, "0.png"), "Initial Layout Before Optimization")
    print("ploting  done")
    
    # Plot external turbine comparison with enhanced visualization
    if len(external_layout) > 0:
        internal_x = internal_layout['x'].values
        internal_y = internal_layout['y'].values
        all_external_x = external_layout['x'].values
        all_external_y = external_layout['y'].values
        
        # Get filtered external turbines from smart manager
        filtered_external = smart_manager.external_layout  # This contains only turbines within distance
        if len(filtered_external) > 0:
            filtered_external_x = filtered_external['x'].values
            filtered_external_y = filtered_external['y'].values
        else:
            filtered_external_x = np.array([])
            filtered_external_y = np.array([])
        
        # Create enhanced comparison plot
        plot_external_comparison_enhanced(
            internal_x, internal_y, 
            all_external_x, all_external_y,
            filtered_external_x, filtered_external_y,
            boundaries3, inputs_loc, "Pre-Optimization",
            max_distance_km=max_distance_km
        )
        
        # Also create standard comparison plot for compatibility
        plot_external_comparison(internal_x, internal_y, all_external_x, all_external_y, boundaries3, inputs_loc, "Pre-Optimization")
        
        # Add enhanced farm analysis if enabled
        if plot_farm_analysis:
            print("   Creating enhanced farm analysis visualizations...")
            plot_enhanced_farm_analysis(
                internal_layout, external_layout,
                boundaries=boundaries3,
                output_dir=inputs_loc,
                max_distance_km=max_distance_km,
                sector_width=farm_sector_width,
                farm_grouping=farm_grouping,
                analysis_directions=analysis_directions
            )
            print("   Enhanced farm analysis plots created")
        
        print(f"   Enhanced layout comparison plots created:")
        print(f"     - Internal turbines: {len(internal_x)}")
        print(f"     - All external turbines: {len(all_external_x)}") 
        print(f"     - External within {max_distance_km}km: {len(filtered_external_x)}")
    
    # Setup optimization parameters from optimizer_params.py
    n_gen = pr.MAXGEN
    pop_size = pr.PopSize
    problem_name = "smartExternalLayoutOptimization"
    
    # Define operators using parameters from optimizer_params.py
    crossover = SBX(eta=pr.eta_c, prob=pr.pCross_real)
    mutation = PolynomialMutation(prob=1.0/len(internal_layout), eta=pr.eta_m)
    sampling = FloatRandomSampling()
    
    # Set up termination criteria based on optimizer_params.py settings
    from pymoo.termination import get_termination
    if pr.ftol < 1e-6:  # If ftol is very strict, use generation-based to avoid early termination
        termination = get_termination("n_gen", n_gen)
        print(f"   Using generation-based termination (ftol={pr.ftol} too strict)")
    else:
        # Use convergence-based termination with relaxed tolerances
        from pymoo.termination import DefaultSingleObjectiveTermination
        termination = DefaultSingleObjectiveTermination(
            #xtol=1e-4,
            #cvtol=1e-4,
            xtol=1e-8,
            cvtol=1e-8,
            ftol=pr.ftol,  # Ensure ftol is not too strict
            period=50,
            n_max_gen=n_gen,
            n_max_evals=100000
        )
        print(f"   Using convergence-based termination (ftol={pr.ftol})")
    
    # Alternative with relaxed tolerances (comment above and uncomment below if needed):
    # termination = DefaultSingleObjectiveTermination(
    #     xtol=1e-4,      # Relaxed from 1e-8
    #     cvtol=1e-4,     # Relaxed from 1e-6  
    #     ftol=1e-4,      # Relaxed from 1e-08
    #     period=50,      # Increased from 20
    #     n_max_gen=n_gen,
    #     n_max_evals=100000
    # )
    
    # Print optimizer parameters
    print("-------------------------------------")
    print("SMART External Optimizer Parameters:")
    print("-------------------------------------")
    print(f"mdistOpim: {optimizer_etc.mdistOpim}")
    print(f"wdIntervalOptim: {optimizer_etc.wdIntervalOptim}")
    print(f"Number of internal turbines: {len(internal_layout)}")
    print(f"Number of external turbines (filtered): {len(smart_manager.external_layout)}")
    print(f"Distance threshold: {max_distance_km}km")
    print("layoutOptimization")
    print(f"Solver: {pr.solver}")
    print(f"MAXGEN: {pr.MAXGEN}")
    print("-------------------------------------")
    
    # Initialize layout optimization with external turbine handling and iteration plotting
    # For now, use standard EnhancedLayoutOptimizationPymoo with filtered turbines
    # Smart per-direction filtering would require deeper integration
    layout_opt = EnhancedLayoutOptimizationPymoo(
        fi,
        boundaries=boundaries3,
        freq=freq,
        min_dist=optimizer_etc.mdistOpim,
        problem_name=problem_name,
        n_gen=n_gen,
        pop_size=pop_size,
        ftol=pr.ftol,
        # turbines_weights not needed - optimizing internal turbines only
        n_workers=32,  # Use all 32 cores
        use_monitoring=True,
        timeout_per_eval=300,
        crossover=crossover,
        mutation=mutation,
        sampling=sampling
    )
    
    # Add iteration plotting capability if requested
    if hasattr(layout_opt, 'add_callback'):
        from hybrid_layout_optimization_pymoo_external import IterationPlottingCallback
        callback = IterationPlottingCallback(boundaries3, "Output/iterations")
        layout_opt.add_callback(callback)
    
    # Run optimization with potential iteration plotting
    print("🚀 Starting SMART NSGA-II optimization with filtered external turbines...")
    start_opt = timerpc()
    sol = layout_opt.optimize()
    opt_time = timerpc() - start_opt
    
    result = sol
    
    # Log optimization results
    write_log("Best solution(s) from SMART optimization:", optLog)
    write_log(result.X, optLog)
    write_log("\nObjective space values F:", optLog)
    write_log(result.F, optLog)
    
    # Log smart filtering statistics
    cache_stats = smart_manager.get_cache_stats()
    write_log("\nSmart filtering statistics:", optLog)
    write_log(cache_stats, optLog)
    
    # Get optimized layout
    optimized_locs = layout_opt.get_optimized_locs()
    x_opt, y_opt = optimized_locs
    
    # Calculate optimized AEP with smart filtering
    optimized_internal = internal_layout.copy()
    optimized_internal['x'] = x_opt[:len(internal_layout)]
    optimized_internal['y'] = y_opt[:len(internal_layout)]
    
    # Create new enhanced manager with optimized internal layout
    optimized_smart_manager = create_enhanced_farm_manager(
        optimized_internal, external_layout, 
        max_distance_km=max_distance_km,
        sector_width=farm_sector_width,
        farm_grouping=farm_grouping
    )
    
    # Calculate AEP with wind direction-specific external turbines
    print("\n🔄 Calculating optimized AEP with smart filtering...")
    optimized_aep = 0
    
    for wd_idx, wd in enumerate(wd_array):
        # Get relevant layout for this wind direction
        relevant_layout = optimized_smart_manager.get_layout_with_farm_filtering(wd)
        
        # Reinitialize FLORIS with relevant turbines only
        fi.reinitialize(
            layout=(relevant_layout['x'].values, relevant_layout['y'].values),
            turbine_type=relevant_layout['turb'].values,
            wind_directions=np.array([wd]),
            wind_speeds=ws_array
        )
        
        # Calculate wake for this wind direction
        fi.calculate_wake()
        turbine_powers = fi.get_turbine_powers() / 1E6  # MW
        
        # Calculate AEP contribution from this wind direction
        for ws_idx, ws in enumerate(ws_array):
            freq_val = freq[wd_idx, ws_idx]
            powers = turbine_powers[0, ws_idx, :]  # Single wind direction
            
            # Apply weight factors - only count internal turbine power
            weights = relevant_layout['weight'].values
            weighted_powers = powers * weights
            optimized_aep += np.sum(weighted_powers) * freq_val * 8760
    
    optimized_aep = optimized_aep / 1000  # Convert to GWh
    
    # Plot final optimized layout using properly scaled coordinates
    print("   Creating final optimized layout visualization...")
    
    # Ensure Output directory exists
    output_dir = os.path.join(workDir, 'Output')
    os.makedirs(output_dir, exist_ok=True)
    
    # Use the coordinates from the updated layout to ensure correct scaling
    final_x_coords = optimized_internal['x'].values
    final_y_coords = optimized_internal['y'].values
    plot_output_path = os.path.join(output_dir, "optimized_layout.png")
    print(f"   Saving plot to: {plot_output_path}")
    plot_layout_beforeOptim(final_x_coords, final_y_coords, boundaries3, plot_output_path, "Final Optimized Layout")
    
    # Plot final external comparison with enhanced visualization
    if len(external_layout) > 0:
        # Use properly scaled coordinates for comparison plot
        final_internal_x = optimized_internal['x'].values
        final_internal_y = optimized_internal['y'].values
        all_external_x = external_layout['x'].values  # All external turbines
        all_external_y = external_layout['y'].values
        
        # Get filtered external turbines from optimized smart manager
        filtered_external = optimized_smart_manager.external_layout
        if len(filtered_external) > 0:
            filtered_external_x = filtered_external['x'].values
            filtered_external_y = filtered_external['y'].values
        else:
            filtered_external_x = np.array([])
            filtered_external_y = np.array([])
        
        # Create enhanced comparison plot
        plot_external_comparison_enhanced(
            final_internal_x, final_internal_y,
            all_external_x, all_external_y,
            filtered_external_x, filtered_external_y,
            boundaries3, output_dir, "Post-Optimization",
            max_distance_km=max_distance_km
        )
        
        # Also create standard comparison plot
        plot_external_comparison(final_internal_x, final_internal_y, all_external_x, all_external_y, boundaries3, output_dir, "Post-Optimization")
        
        print(f"   Final layout comparison plots created")
        print(f"     - Optimized internal turbines: {len(final_internal_x)}")
        print(f"     - All external turbines: {len(all_external_x)}")
        print(f"     - External within {max_distance_km}km: {len(filtered_external_x)}")
    
    total_time = timerpc() - start_total
    print(f"\n✅ SMART external layout optimization complete: {optimized_aep:.2f} GWh")
    print(f"   Optimization time: {opt_time:.1f} seconds")
    print(f"   Total runtime: {total_time:.1f} seconds")
    print(f"   Cache statistics: {cache_stats}")
    print(f"   All outputs saved to: {outputs_loc}")
    
    return optimized_aep


def run_florisInitial(
        inputs_loc=f"{workDir}/Input/",
        a_file='a_par.txt',
        freq_file='freq_table.txt',
        k_file='k_par.txt',
        layout_file='Initial.layout.csv',
        input_config='config_Jensen_FLS_Validation_FastAEP.yaml',
        logfile_name="mylogfile.txt",
        outputs_loc=f"{workDir}/OutputInitial/",
        parallel=True,
        PlotFlowfield=False,
        GeneratingTurbEffContourPlots=False
    ):
    """
    Calculate baseline AEP for initial layout (internal + external turbines)
    Uses corrected FLORIS interface approach
    """
    print("========================================")
    print("SMART External Initial Layout Baseline")
    print("========================================")
    print("User given inputs:")
    print(f"inputs_loc: {inputs_loc}")
    print(f"outputs_loc: {outputs_loc}")
    print(f"layout_file: {layout_file}")
    print(f"input_config: {input_config}")
    print(f"logfile_name: {logfile_name}")
    print("========================================")
    
    # Performance tracking
    start_total = timerpc()
    
    os.chdir(inputs_loc)
    
    # Setup turbine library
    if os.path.exists(outputs_loc+'turbine_library'):
        shutil.rmtree(outputs_loc+'turbine_library')
    shutil.copytree(turb_lib_path, outputs_loc+'turbine_library')
    
    # Setup logging
    log_loc = f'{outputs_loc}{logfile_name}'
    if os.path.exists(log_loc):
        with open(log_loc, "w") as log:
            log.truncate()
    
    print("\n🔄 Calculating baseline AEP for initial layout...")
    
    # Read and process wind data
    ts = pd.read_csv(inputs_loc + "timeseries.txt", sep=' ')
    
    # Process timestamp format properly
    if 'time' in ts.columns and ts.index.name is None:
        ts.index.name = 'date'
        ts.reset_index(inplace=True)
        ts['timestamp'] = pd.to_datetime(ts['date'].astype(str) + ' ' + ts['time'])
        ts = ts[['timestamp', 'wd', 'ws']]
    elif 'timestamp' not in ts.columns:
        if pd.api.types.is_datetime64_any_dtype(ts.index):
            ts['timestamp'] = ts.index
            ts.reset_index(drop=True, inplace=True)
        else:
            ts = ts[['time', 'wd', 'ws']].rename(columns={'time': 'timestamp'})
    
    WR = ts_to_freq_df(ts)
    WR['wd'] = pd.to_numeric(WR['wd'], errors='coerce')
    WR['ws'] = pd.to_numeric(WR['ws'], errors='coerce')
    print(f"Size of WR: {len(WR.index)}")
    
    # Load layout data - KEY DIFFERENCE: Load multiple files for external turbines
    from glob import glob
    csv_files = glob(f"{workDir}/Input/External.layout.csv") + glob(f"{workDir}/Input/Initial.layout.csv")
    
    extra_file_path = os.path.join(workDir, 'Input', 'ExtraExternal.csv')
    if os.path.exists(extra_file_path):
        csv_files.append(extra_file_path)
    
    # Load and filter valid dataframes
    df_list = [pd.read_csv(file) for file in csv_files]
    filtered_df_list = [df for df in df_list if not df.empty and not df.isna().all().all()]
    layout = pd.concat(filtered_df_list, ignore_index=True)
    
    # Handle different column names and standardize
    if 'easting' in layout.columns:
        layout = layout.rename(columns={'easting':'x','northing':'y'})
    if 'turb' in layout.columns:
        layout['turb'] = layout['turb'].apply(sanitize_name)
    
    # Set turbine weights for external turbines
    if 'external' in layout.columns:
        layout.loc[layout['external'] == False, 'weight'] = 1
        layout.loc[layout['external'] == True, 'weight'] = 0
        turbines_weights = layout['weight'].values
        print(f"Internal turbines: {np.sum(turbines_weights)}")
        print(f"External turbines: {len(layout) - np.sum(turbines_weights)}")
    else:
        turbines_weights = np.ones(len(layout))
        print(f"All turbines treated as internal: {len(layout)}")
    
    # Initialize FLORIS interface
    fi = FlorisInterface(inputs_loc + input_config)
    wd_array = np.array(sorted(WR["wd"].unique()), dtype=float)
    ws_array = np.array(sorted(WR["ws"].unique()), dtype=float)
    
    # Enhanced logging for wind conditions
    print(f"   Time series length: {len(ts)} timestamps")
    print(f"   Frequency table: {len(WR)} unique wind conditions")
    print(f"   Wind direction range: [{wd_array.min():.1f}°, {wd_array.max():.1f}°]")
    print(f"   Wind speed range: [{ws_array.min():.1f}, {ws_array.max():.1f}] m/s")
    
    fi.reinitialize(layout=(layout['x'], layout['y']),
                    turbine_type=layout['turb'] if 'turb' in layout.columns else None,
                    wind_directions=wd_array,
                    wind_speeds=ws_array)
    
    # Enhanced hub height logic to eliminate reference height warnings
    log_file = f'{outputs_loc}Initial.summary.txt'
    
    # Check if we have external turbine information
    if 'external' in layout.columns:
        # Use external column logic
        internal_turb = layout.loc[layout['external']==False,'turb'].unique()
    else:
        # If no external column, treat all turbines as internal
        internal_turb = layout['turb'].unique() if 'turb' in layout.columns else ['default']
    
    if len(internal_turb) > 1:
        # Multiple internal turbine types - calculate average hub height
        hhs = []
        for turb in internal_turb:
            if 'turb' in layout.columns:
                idx = layout.loc[layout['turb']==turb].index[0]
            else:
                idx = 0  # Use first turbine if no turbine type column
            hhs.append(fi.floris.farm.hub_heights[idx])
        ref_ht = sum(hhs)/len(hhs)
        message = f'Multiple turbine types detected as internal. Reference wind height set to average hub height: {ref_ht:.1f}m'
        print(f"   {message}")
        write_log(message, log_file)
    else:
        # Single internal turbine type - use its hub height
        if 'external' in layout.columns:
            internal_turb_idx = layout.loc[layout['external'] == False].index[0]
        else:
            internal_turb_idx = 0  # Use first turbine
        ref_ht = fi.floris.farm.hub_heights[internal_turb_idx]
        message = f'Single turbine type detected. Reference wind height set to hub height: {ref_ht:.1f}m'
        print(f"   {message}")
        write_log(message, log_file)
    
    # Apply the reference height to FLORIS
    fi.floris.flow_field.reference_wind_height = ref_ht
    print(f"   ✓ Reference wind height updated to {ref_ht:.1f}m")
    
    # Calculate baseline AEP
    total_calcs = len(wd_array) * len(ws_array) * len(layout)
    if total_calcs < 10_000_000:
        print("Using serial wake calculation (optimal for this problem size)")
        fi.calculate_wake()
        turbine_powers = fi.get_turbine_powers() / 1E6
    else:
        print("Using parallel wake calculation")
        turbine_powers = GetAEPParallel(fi, max_workers=4, n_wind_direction_splits=2, n_wind_speed_splits=2, use_mpi4py=False)
    
    # Calculate baseline AEP from powers and frequencies
    freq = WR.set_index(['wd','ws']).unstack().values
    baseline_aep = 0
    for wd_idx, wd in enumerate(wd_array):
        for ws_idx, ws in enumerate(ws_array):
            powers = turbine_powers[wd_idx, ws_idx, :] if total_calcs < 10_000_000 else turbine_powers[wd_idx, ws_idx, :]
            # Apply weight factors for external turbines
            weighted_powers = powers * turbines_weights
            baseline_aep += np.sum(weighted_powers) * freq[wd_idx, ws_idx] * 8760
    
    baseline_aep = baseline_aep / 1000  # Convert to GWh
    
    # Enhanced performance metrics
    if total_calcs < 10_000_000:
        # Calculate performance metrics from turbine powers
        avg_total_power = np.mean(np.sum(turbine_powers, axis=2))  # Sum across turbines, average across conditions
        avg_power_per_turbine = np.mean(turbine_powers)
        capacity_factor = avg_power_per_turbine / 3.6 if avg_power_per_turbine > 0 else 0  # Assuming 3.6 MW rated
        
        print(f"   🌀 Farm AEP: {baseline_aep:.2f} GWh")
        print(f"   ⚡ Average farm power: {avg_total_power:.1f} MW")
        print(f"   📊 Average power per turbine: {avg_power_per_turbine:.2f} MW")
        print(f"   📈 Estimated capacity factor: {capacity_factor:.3f}")
        print(f"   🎯 AEP per turbine: {baseline_aep/len(layout):.3f} GWh")
    else:
        print(f"   🌀 Farm AEP: {baseline_aep:.2f} GWh")
        print(f"   🎯 AEP per turbine: {baseline_aep/len(layout):.3f} GWh")
    
    total_time = timerpc() - start_total
    print(f"✅ Baseline AEP complete: {baseline_aep:.2f} GWh")
    print(f"   Runtime: {total_time:.1f} seconds")
    print(f"   All outputs saved to: {outputs_loc}")
    
    return baseline_aep


def run_florisInternal(
        inputs_loc=f"{workDir}/Input/",
        a_file='a_par.txt',
        freq_file='freq_table.txt',
        k_file='k_par.txt',
        layout_file='Optimized.layout.csv',
        input_config='config_Jensen_FLS_Validation_FastAEP.yaml',
        logfile_name="mylogfile.txt",
        outputs_loc=f"{workDir}/OutputInternal/",
        parallel=True,
        PlotFlowfield=False,
        GeneratingTurbEffContourPlots=False
    ):
    """
    Calculate AEP for internal turbines only (optimized layout without external turbines)
    Uses corrected FLORIS interface approach
    """
    print("========================================")
    print("SMART External Internal-Only Simulation")
    print("========================================")
    print("User given inputs:")
    print(f"inputs_loc: {inputs_loc}")
    print(f"outputs_loc: {outputs_loc}")
    print(f"layout_file: {layout_file}")
    print(f"input_config: {input_config}")
    print(f"logfile_name: {logfile_name}")
    print("========================================")
    
    # Performance tracking
    start_total = timerpc()
    
    os.chdir(inputs_loc)
    
    # Setup turbine library
    if os.path.exists(outputs_loc+'turbine_library'):
        shutil.rmtree(outputs_loc+'turbine_library')
    shutil.copytree(turb_lib_path, outputs_loc+'turbine_library')
    
    # Setup logging
    log_loc = f'{outputs_loc}{logfile_name}'
    if os.path.exists(log_loc):
        with open(log_loc, "w") as log:
            log.truncate()
    
    print("\n🔄 Calculating AEP for internal turbines only...")
    
    # Read and process wind data
    ts = pd.read_csv(inputs_loc + "timeseries.txt", sep=' ')
    
    # Process timestamp format properly
    if 'time' in ts.columns and ts.index.name is None:
        ts.index.name = 'date'
        ts.reset_index(inplace=True)
        ts['timestamp'] = pd.to_datetime(ts['date'].astype(str) + ' ' + ts['time'])
        ts = ts[['timestamp', 'wd', 'ws']]
    elif 'timestamp' not in ts.columns:
        if pd.api.types.is_datetime64_any_dtype(ts.index):
            ts['timestamp'] = ts.index
            ts.reset_index(drop=True, inplace=True)
        else:
            ts = ts[['time', 'wd', 'ws']].rename(columns={'time': 'timestamp'})
    
    WR = ts_to_freq_df(ts)
    WR['wd'] = pd.to_numeric(WR['wd'], errors='coerce')
    WR['ws'] = pd.to_numeric(WR['ws'], errors='coerce')
    print(f"Size of WR: {len(WR.index)}")
    
    # Load optimized layout (internal turbines only)
    layout = pd.read_csv(inputs_loc + layout_file)
    
    # Handle different column names and standardize
    if 'easting' in layout.columns:
        layout = layout.rename(columns={'easting':'x','northing':'y'})
    if 'turb' in layout.columns:
        layout['turb'] = layout['turb'].apply(sanitize_name)
    
    # Filter only internal turbines if external column exists
    if 'external' in layout.columns:
        layout = layout[layout['external'] == False].copy()
        print(f"Filtered to {len(layout)} internal turbines only")
    
    # Initialize FLORIS interface
    fi = FlorisInterface(inputs_loc + input_config)
    wd_array = np.array(sorted(WR["wd"].unique()), dtype=float)
    ws_array = np.array(sorted(WR["ws"].unique()), dtype=float)
    
    fi.reinitialize(layout=(layout['x'], layout['y']),
                    turbine_type=layout['turb'] if 'turb' in layout.columns else None,
                    wind_directions=wd_array,
                    wind_speeds=ws_array)
    
    # Calculate internal-only AEP
    total_calcs = len(wd_array) * len(ws_array) * len(layout)
    if total_calcs < 10_000_000:
        print("Using serial wake calculation (optimal for this problem size)")
        fi.calculate_wake()
        turbine_powers = fi.get_turbine_powers() / 1E6
    else:
        print("Using parallel wake calculation")
        turbine_powers = GetAEPParallel(fi, max_workers=4, n_wind_direction_splits=2, n_wind_speed_splits=2, use_mpi4py=False)
    
    # Calculate internal AEP from powers and frequencies
    freq = WR.set_index(['wd','ws']).unstack().values
    internal_aep = 0
    for wd_idx, wd in enumerate(wd_array):
        for ws_idx, ws in enumerate(ws_array):
            powers = turbine_powers[wd_idx, ws_idx, :] if total_calcs < 10_000_000 else turbine_powers[wd_idx, ws_idx, :]
            internal_aep += np.sum(powers) * freq[wd_idx, ws_idx] * 8760
    
    internal_aep = internal_aep / 1000  # Convert to GWh
    
    total_time = timerpc() - start_total
    print(f"✅ Internal-only AEP complete: {internal_aep:.2f} GWh")
    print(f"   Runtime: {total_time:.1f} seconds")
    print(f"   All outputs saved to: {outputs_loc}")
    
    return internal_aep


def run_floris(
        inputs_loc=f"{workDir}/Input/",
        a_file='a_par.txt',
        freq_file='freq_table.txt',
        k_file='k_par.txt',
        layout_file='Optimized.layout.csv',
        input_config='config_Jensen_FLS_Validation_FastAEP.yaml',
        logfile_name="mylogfile.txt",
        outputs_loc=f"{workDir}/Output/",
        parallel=True,
        PlotFlowfield=False,
        GeneratingTurbEffContourPlots=False,
        ref_ht=None,
        max_distance_km=20
    ):
    """
    Calculate combined AEP for internal + external turbines with SMART filtering
    Uses wind direction-specific external turbine inclusion
    """
    print("========================================")
    print("SMART External Combined Internal+External Simulation")
    print("========================================")
    print("User given inputs:")
    print(f"inputs_loc: {inputs_loc}")
    print(f"outputs_loc: {outputs_loc}")
    print(f"layout_file: {layout_file}")
    print(f"input_config: {input_config}")
    print(f"logfile_name: {logfile_name}")
    print(f"max_distance_km: {max_distance_km}")
    print("========================================")
    
    # Performance tracking
    start_total = timerpc()
    
    os.chdir(inputs_loc)
    
    # Setup turbine library
    if os.path.exists(outputs_loc+'turbine_library'):
        shutil.rmtree(outputs_loc+'turbine_library')
    shutil.copytree(turb_lib_path, outputs_loc+'turbine_library')
    
    # Setup logging
    log_loc = f'{outputs_loc}{logfile_name}'
    if os.path.exists(log_loc):
        with open(log_loc, "w") as log:
            log.truncate()
    
    print("\n🔄 Calculating combined AEP with SMART external turbine filtering...")
    
    # Read and process wind data
    ts = pd.read_csv(inputs_loc + "timeseries.txt", sep=' ')
    
    # Process timestamp format properly
    if 'time' in ts.columns and ts.index.name is None:
        ts.index.name = 'date'
        ts.reset_index(inplace=True)
        ts['timestamp'] = pd.to_datetime(ts['date'].astype(str) + ' ' + ts['time'])
        ts = ts[['timestamp', 'wd', 'ws']]
    elif 'timestamp' not in ts.columns:
        if pd.api.types.is_datetime64_any_dtype(ts.index):
            ts['timestamp'] = ts.index
            ts.reset_index(drop=True, inplace=True)
        else:
            ts = ts[['time', 'wd', 'ws']].rename(columns={'time': 'timestamp'})
    
    WR = ts_to_freq_df(ts)
    WR['wd'] = pd.to_numeric(WR['wd'], errors='coerce')
    WR['ws'] = pd.to_numeric(WR['ws'], errors='coerce')
    print(f"Size of WR: {len(WR.index)}")
    
    # Load combined layout data (internal + external turbines)
    from glob import glob
    csv_files = []
    
    # Try to load optimized layout first, fallback to external + initial
    optimized_path = inputs_loc + layout_file
    if os.path.exists(optimized_path):
        csv_files.append(optimized_path)
    else:
        csv_files = glob(f"{workDir}/Input/External.layout.csv") + glob(f"{workDir}/Input/Initial.layout.csv")
        extra_file_path = os.path.join(workDir, 'Input', 'ExtraExternal.csv')
        if os.path.exists(extra_file_path):
            csv_files.append(extra_file_path)
    
    # Load and filter valid dataframes
    df_list = [pd.read_csv(file) for file in csv_files]
    filtered_df_list = [df for df in df_list if not df.empty and not df.isna().all().all()]
    all_layout = pd.concat(filtered_df_list, ignore_index=True)
    
    # Prepare layout data
    all_layout = all_layout.rename(columns={'easting': 'x', 'northing': 'y'})
    if 'turb' in all_layout.columns:
        all_layout['turb'] = all_layout['turb'].apply(sanitize_name)
    
    # Separate internal and external layouts
    internal_layout = all_layout[all_layout['external'] == False].copy()
    external_layout = all_layout[all_layout['external'] == True].copy()
    
    print(f"\nLayout statistics:")
    print(f"  Internal turbines: {len(internal_layout)}")
    print(f"  External turbines: {len(external_layout)}")
    
    # Initialize enhanced farm manager (using defaults for initial calculation)
    smart_manager = create_enhanced_farm_manager(
        internal_layout, external_layout, 
        max_distance_km=max_distance_km,
        sector_width=90,
        farm_grouping='auto'
    )
    
    # Initialize FLORIS interface
    fi = FlorisInterface(inputs_loc + input_config)
    wd_array = np.array(sorted(WR["wd"].unique()), dtype=float)
    ws_array = np.array(sorted(WR["ws"].unique()), dtype=float)
    freq = WR.set_index(['wd','ws']).unstack().values
    
    # Set reference height for external turbines
    internal_turb = internal_layout['turb'].unique()
    if ref_ht is None:
        if len(internal_turb) > 1:
            # Use first layout to get hub heights
            temp_layout = smart_manager.get_layout_with_farm_filtering(wd_array[0])
            fi.reinitialize(
                layout=(temp_layout['x'].values, temp_layout['y'].values),
                turbine_type=temp_layout['turb'].values,
                wind_directions=np.array([wd_array[0]]),
                wind_speeds=ws_array
            )
            
            hhs = []
            for i, row in internal_layout.iterrows():
                turb_idx = np.where((temp_layout['x'] == row['x']) & (temp_layout['y'] == row['y']))[0][0]
                hhs.append(fi.floris.farm.hub_heights[turb_idx])
            ref_ht = sum(hhs) / len(hhs)
            write_log('Multiple internal turbine types - using average hub height as reference.', log_loc)
        else:
            # Single internal turbine type - get its hub height from first internal turbine
            # Use first layout to get hub heights
            temp_layout = smart_manager.get_layout_with_farm_filtering(wd_array[0])
            fi.reinitialize(
                layout=(temp_layout['x'].values, temp_layout['y'].values),
                turbine_type=temp_layout['turb'].values,
                wind_directions=np.array([wd_array[0]]),
                wind_speeds=ws_array
            )
            
            # Find first internal turbine index in the temp layout
            internal_turb_idx = None
            for i, row in internal_layout.iterrows():
                turb_idx = np.where((temp_layout['x'] == row['x']) & (temp_layout['y'] == row['y']))[0]
                if len(turb_idx) > 0:
                    internal_turb_idx = turb_idx[0]
                    break
            
            if internal_turb_idx is not None:
                ref_ht = fi.floris.farm.hub_heights[internal_turb_idx]
                write_log(f'Single internal turbine type - using hub height: {ref_ht:.1f}m as reference.', log_loc)
            else:
                ref_ht = 90  # Fallback only if no internal turbine found
                write_log('Warning: No internal turbine found, using default hub height: 90m', log_loc)

    # Calculate combined AEP with smart filtering
    print("\n🔄 Calculating combined AEP with wind direction-specific external turbines...")
    combined_aep = 0
    
    for wd_idx, wd in enumerate(wd_array):
        # Get relevant layout for this wind direction
        relevant_layout = smart_manager.get_layout_with_farm_filtering(wd)
        
        # Reinitialize FLORIS with relevant turbines only
        fi.reinitialize(
            layout=(relevant_layout['x'].values, relevant_layout['y'].values),
            turbine_type=relevant_layout['turb'].values,
            wind_directions=np.array([wd]),
            wind_speeds=ws_array
        )
        
        if ref_ht is not None:
            fi.floris.flow_field.reference_wind_height = ref_ht
        
        # Calculate wake for this wind direction
        fi.calculate_wake()
        turbine_powers = fi.get_turbine_powers() / 1E6  # MW
        
        # Calculate AEP contribution from this wind direction
        for ws_idx, ws in enumerate(ws_array):
            freq_val = freq[wd_idx, ws_idx]
            powers = turbine_powers[0, ws_idx, :]  # Single wind direction
            
            # Apply weight factors - only count internal turbine power
            weights = relevant_layout['weight'].values
            weighted_powers = powers * weights
            combined_aep += np.sum(weighted_powers) * freq_val * 8760
        
        # Progress indicator
        if wd_idx % 10 == 0:
            print(f"  Progress: {wd_idx+1}/{len(wd_array)} wind directions processed")
    
    combined_aep = combined_aep / 1000  # Convert to GWh
    
    # Get final cache statistics
    cache_stats = smart_manager.get_cache_stats()
    
    total_time = timerpc() - start_total
    print(f"\n✅ SMART combined AEP complete: {combined_aep:.2f} GWh")
    print(f"   Runtime: {total_time:.1f} seconds")
    print(f"   Cache statistics: {cache_stats}")
    print(f"   All outputs saved to: {outputs_loc}")
    
    return combined_aep


def main(plot_iterations=None, iteration_interval=None):
    """Main execution function with iteration plotting support"""
    print("========================================")  
    print("SMART External Layout Optimization Workflow")
    print("4-Step Process with Wind Direction-Based Filtering")
    print("========================================")
    
    # Read parameters from params.py if not provided
    if plot_iterations is None:
        plot_iterations = pr.plot_iterations
    if iteration_interval is None:
        iteration_interval = pr.iteration_interval
    
    print(f"Plot iterations: {plot_iterations} (from params.py)")
    print(f"Iteration interval: {iteration_interval} (from params.py)")
    print("========================================")
    
    # Performance tracking for entire workflow
    workflow_start = timerpc()
    
    try:
        # Step 1: Calculate baseline AEP for initial layout with external turbines
        print("\n🔄 STEP 1: Initial layout baseline...")
        baseline_aep = run_florisInitial()
        
        # Step 2: Run layout optimization with smart external turbine filtering
        print("\n🔄 STEP 2: SMART layout optimization...")
        # Pass plotting parameters to optimization function
        optimized_aep = run_layoutOpt()  # All parameters now from params.py
        
        # Step 3: Calculate AEP for internal turbines only (no external wake effects)
        print("\n🔄 STEP 3: Internal turbines only...")
        # Try optimized layout first, fallback to initial layout if not found
        optimized_layout_file = '../Output/Optimized.layout.csv'
        initial_layout_file = 'Initial.layout.csv'
        
        # Check if optimized layout exists in the expected location
        optimized_path = os.path.join('Output', 'Optimized.layout.csv')
        initial_path = os.path.join('Input', initial_layout_file)
        
        if os.path.exists(optimized_path):
            internal_aep = run_florisInternal(layout_file='Optimized.layout.csv')
        elif os.path.exists(initial_path):
            print("   ⚠️  Optimized layout not found, using initial layout for internal AEP calculation")
            internal_aep = run_florisInternal(layout_file=initial_layout_file)
        else:
            print("   ❌ No layout file found for internal AEP calculation")
            internal_aep = None
        
        # Step 4: Calculate combined AEP with smart filtering
        print("\n🔄 STEP 4: SMART combined internal+external turbines...")
        full_aep = run_floris()
        
        # Final summary
        workflow_time = timerpc() - workflow_start
        
        print("\n" + "="*60)
        print("🎯 SMART EXTERNAL OPTIMIZATION WORKFLOW COMPLETE")
        print("="*60)
        print(f"Baseline AEP (initial layout):     {baseline_aep:.2f} GWh")
        print(f"Optimized AEP (layout opt):        {optimized_aep:.2f} GWh")
        # Handle case where internal_aep is None
        if internal_aep is not None:
            print(f"Internal-only AEP:                 {internal_aep:.2f} GWh")
            print(f"Combined AEP (smart filtering):    {full_aep:.2f} GWh")
            print(f"\nImprovement from optimization:     {optimized_aep - baseline_aep:.2f} GWh ({((optimized_aep - baseline_aep) / baseline_aep * 100):.1f}%)")
            print(f"External wake impact:              {internal_aep - full_aep:.2f} GWh ({((internal_aep - full_aep) / internal_aep * 100):.1f}%)")
        else:
            print(f"Internal-only AEP:                 ❌ Could not calculate (layout file not found)")
            print(f"Combined AEP (smart filtering):    {full_aep:.2f} GWh")
            print(f"\nImprovement from optimization:     {optimized_aep - baseline_aep:.2f} GWh ({((optimized_aep - baseline_aep) / baseline_aep * 100):.1f}%)")
            print(f"External wake impact:              N/A (internal AEP not available)")
        print(f"\nTotal workflow runtime:            {workflow_time:.1f} seconds")
        print("="*60)
        
        # Write summary to overall progress file
        with open(OverAllProgress, 'a') as f:
            f.write(f"\n{datetime.now()}: SMART External Optimization Complete\n")
            f.write(f"Baseline: {baseline_aep:.2f} GWh, Optimized: {optimized_aep:.2f} GWh\n")
            f.write(f"Internal: {internal_aep:.2f} GWh, Combined: {full_aep:.2f} GWh\n")
            f.write(f"Improvement: {optimized_aep - baseline_aep:.2f} GWh, Runtime: {workflow_time:.1f}s\n")
        
        return True
        
    except Exception as e:
        print(f"❌ SMART OPTIMIZATION FAILED: {e}")
        import traceback
        traceback.print_exc()
        
        # Save error log
        with open('smart_error_log.txt', 'w') as f:
            f.write(f"SMART External Optimization Error\n")
            f.write(f"===============================\n")
            f.write(f"Time: {datetime.now()}\n")
            f.write(f"Error: {str(e)}\n")
            f.write(f"Traceback:\n")
            traceback.print_exc(file=f)
        
        return False


if __name__ == "__main__":
    # All parameters now read from params.py
    # Override here if needed for testing: main(plot_iterations=True, iteration_interval=1)
    success = main()
    sys.exit(0 if success else 1)
