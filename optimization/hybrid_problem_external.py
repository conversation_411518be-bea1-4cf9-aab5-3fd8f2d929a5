#!/usr/bin/env python3
"""
Hybrid Problem Definition for External Turbine Optimization
Custom problem class using your ProcessPoolExecutor patterns and external turbine framework
"""

import numpy as np
import time
from concurrent.futures import ProcessPoolExecutor, as_completed, TimeoutError
import warnings
warnings.filterwarnings('ignore')

from pymoo.core.problem import Problem


class HybridLayoutOptimizationProblem(Problem):
    """
    Problem definition for hybrid optimization following your LayoutOptimizationPymooExternal patterns
    Handles parallel evaluation with external turbine constraints
    """
    
    def __init__(self, parent_optimizer):
        """
        Initialize using your problem structure patterns
        
        Args:
            parent_optimizer: HybridLayoutOptimizationPymoo instance with your framework
        """
        self.parent = parent_optimizer
        
        # Problem dimensions: only optimize internal turbines (2D coordinates)
        n_vars = 2 * self.parent.nturbines  # Only internal turbines from your pattern
        n_obj = 1  # Single objective: maximize AEP
        n_constr = 2 * self.parent.nturbines  # Distance + boundary constraints
        
        # Initialize problem with unit bounds (your normalization pattern)
        super().__init__(n_var=n_vars, n_obj=n_obj, n_constr=n_constr, xl=0.0, xu=1.0)
        
        # Evaluation counter
        self.eval_count = 0
        
        # Cache for objective function following your pattern
        self._evaluation_cache = {}
        self._cache_tolerance = 1e-8
        
        # Objective normalization (will be set based on baseline waked internal AEP)
        self.baseline_internal_aep = None  # Will be set by parent after baseline calculation
        self.normalization_factor = 1.0  # Will be updated when baseline is set

    def _evaluate(self, X, out, *args, **kwargs):
        """
        Parallel evaluation following your exact ProcessPoolExecutor pattern
        """
        n_samples = X.shape[0]
        
        # Initialize constraint arrays
        g = np.zeros((n_samples, 2 * self.parent.nturbines))
        
        # Parallel objective function evaluation with timeout following your pattern
        try:
            if self.parent.use_monitoring:
                start_time = time.time()
            
            # Use parallel processing for objective function following your pattern
            with ProcessPoolExecutor(max_workers=self.parent.n_workers) as executor:
                # Submit all evaluations
                future_to_idx = {}
                for i in range(n_samples):
                    future = executor.submit(self._safe_obj_func_external, X[i])
                    future_to_idx[future] = i
                
                # Collect results with timeout following your pattern
                f = np.zeros(n_samples)
                for future in as_completed(future_to_idx, timeout=self.parent.timeout_per_eval):
                    idx = future_to_idx[future]
                    try:
                        f[idx] = future.result()
                    except Exception as e:
                        print(f"Warning: External evaluation {idx} failed: {e}")
                        f[idx] = -1e-6 * self.normalization_factor  # Normalized penalty pattern
            
            # Vectorized constraint evaluation following your pattern
            for i in range(n_samples):
                g[i, :self.parent.nturbines] = self.parent._space_constraint(X[i])
                g[i, self.parent.nturbines:2 * self.parent.nturbines] = self.parent._distance_from_boundaries(X[i])
            
            if self.parent.use_monitoring:
                eval_time = time.time() - start_time
                self.parent.monitor_data['eval_times'].append(eval_time)
            
        except TimeoutError:
            print("Warning: External evaluation timeout occurred")
            if self.parent.use_monitoring:
                self.parent.monitor_data['timeouts'] += 1
            # Return normalized penalty values following your pattern
            f = np.full(n_samples, -1e-6 * self.normalization_factor)
            g = np.ones((n_samples, 2 * self.parent.nturbines))  # Violate all constraints
        
        # Convert to minimization problem (negate objective)
        out["F"] = (-f).reshape(-1, 1)  # Negative because we maximize AEP
        out["G"] = g
        
        self.eval_count += n_samples

    def set_baseline_normalization(self, baseline_internal_aep_gwh):
        """Set baseline internal AEP for objective normalization
        
        Args:
            baseline_internal_aep_gwh: Baseline waked internal AEP in GWh
        """
        self.baseline_internal_aep = baseline_internal_aep_gwh
        self.normalization_factor = 1.0 / baseline_internal_aep_gwh
        print(f"📊 Objective normalization set: baseline = {baseline_internal_aep_gwh:.2f} GWh")
        print(f"   Normalization factor = {self.normalization_factor:.6f}")
        print(f"   Objective = 1.0 means same as baseline, >1.0 means improvement")

    def _safe_obj_func_external(self, x):
        """
        Safe wrapper for objective function with external turbine handling
        Following your _safe_obj_func pattern
        """
        try:
            # Check cache first following your caching pattern
            cache_key = tuple(np.round(x / self._cache_tolerance) * self._cache_tolerance)
            if cache_key in self._evaluation_cache:
                if self.parent.use_monitoring:
                    self.parent.monitor_data['cache_hits'] += 1
                return self._evaluation_cache[cache_key]
            
            if self.parent.use_monitoring:
                self.parent.monitor_data['cache_misses'] += 1
            
            # Reconstruct full layout: optimized internal + fixed external turbines
            # Following your exact layout reconstruction pattern
            full_layout_x = np.zeros(self.parent.numberOfInternalWTGs + self.parent.numberOfExternalWTGs)
            full_layout_y = np.zeros(self.parent.numberOfInternalWTGs + self.parent.numberOfExternalWTGs)
            
            # Place optimized internal turbines using your denormalization pattern
            internal_x = self.parent._unnorm(x[:self.parent.nturbines], self.parent.xmin, self.parent.xmax)
            internal_y = self.parent._unnorm(x[self.parent.nturbines:], self.parent.ymin, self.parent.ymax)
            
            for i, turb_idx in enumerate(self.parent.turbs_to_opt):
                full_layout_x[turb_idx] = internal_x[i]
                full_layout_y[turb_idx] = internal_y[i]
            
            # Keep external turbines fixed using your pattern
            if self.parent.numberOfExternalWTGs > 0:
                external_x = self.parent._unnorm(self.parent.xtern[:self.parent.numberOfExternalWTGs], 
                                               self.parent.xmin, self.parent.xmax)
                external_y = self.parent._unnorm(self.parent.xtern[self.parent.numberOfExternalWTGs:], 
                                               self.parent.ymin, self.parent.ymax)
                
                for i, turb_idx in enumerate(self.parent.turbs_extern):
                    full_layout_x[turb_idx] = external_x[i]
                    full_layout_y[turb_idx] = external_y[i]
            
            # Calculate AEP with FLORIS using your interface pattern
            self.parent.fi.reinitialize(layout=(full_layout_x, full_layout_y))
            
            # Extract INTERNAL WAKED AEP only (considering external wake effects)
            from utilities import calculate_gross_net_aep_efficient
            _, internal_waked_aep_gwh, _ = calculate_gross_net_aep_efficient(
                self.parent.fi, self.parent.freq, n_internal=self.parent.nturbines
            )
            
            # Normalize objective relative to baseline internal AEP
            if self.baseline_internal_aep is not None:
                aep_normalized = internal_waked_aep_gwh * self.normalization_factor
            else:
                # Fallback if baseline not set yet
                aep_normalized = internal_waked_aep_gwh / 175.0  # Approximate baseline
                print(f"⚠️  Warning: Using fallback normalization - baseline not set!")
            
            # Cache result following your pattern
            self._evaluation_cache[cache_key] = aep_normalized
            
            # Limit cache size to prevent memory issues
            if len(self._evaluation_cache) > 10000:
                # Remove oldest entries (simple FIFO)
                keys_to_remove = list(self._evaluation_cache.keys())[:1000]
                for key in keys_to_remove:
                    del self._evaluation_cache[key]
            
            return aep_normalized
            
        except Exception as e:
            print(f"Warning: External objective evaluation failed: {e}")
            return -1e-6 * self.normalization_factor  # Normalized penalty pattern

    def _space_constraint(self, x):
        """
        Internal turbine spacing constraints following your pattern
        Only applies to internal turbines (turbs_to_opt)
        """
        try:
            # Denormalize positions
            internal_x = self.parent._unnorm(x[:self.parent.nturbines], self.parent.xmin, self.parent.xmax)
            internal_y = self.parent._unnorm(x[self.parent.nturbines:], self.parent.ymin, self.parent.ymax)
            
            # Calculate pairwise distances between internal turbines
            constraints = np.zeros(self.parent.nturbines)
            
            for i in range(self.parent.nturbines):
                min_dist = np.inf
                
                # Check distances to other internal turbines
                for j in range(self.parent.nturbines):
                    if i != j:
                        dist = np.sqrt((internal_x[i] - internal_x[j])**2 + (internal_y[i] - internal_y[j])**2)
                        min_dist = min(min_dist, dist)
                
                # Check distances to external turbines
                if self.parent.numberOfExternalWTGs > 0:
                    external_x = self.parent._unnorm(self.parent.xtern[:self.parent.numberOfExternalWTGs], 
                                                   self.parent.xmin, self.parent.xmax)
                    external_y = self.parent._unnorm(self.parent.xtern[self.parent.numberOfExternalWTGs:], 
                                                   self.parent.ymin, self.parent.ymax)
                    
                    for j in range(self.parent.numberOfExternalWTGs):
                        dist = np.sqrt((internal_x[i] - external_x[j])**2 + (internal_y[i] - external_y[j])**2)
                        min_dist = min(min_dist, dist)
                
                # Constraint: g <= 0 means satisfied
                # min_dist should be >= min_dist_constraint
                constraints[i] = self.parent.min_dist - min_dist
            
            return constraints
            
        except Exception as e:
            print(f"Warning: Space constraint evaluation failed: {e}")
            return np.ones(self.parent.nturbines)  # Violate all constraints

    def _distance_from_boundaries(self, x):
        """
        Boundary distance constraints following your pattern
        Only applies to internal turbines
        """
        try:
            # Denormalize positions
            internal_x = self.parent._unnorm(x[:self.parent.nturbines], self.parent.xmin, self.parent.xmax)
            internal_y = self.parent._unnorm(x[self.parent.nturbines:], self.parent.ymin, self.parent.ymax)
            
            constraints = np.zeros(self.parent.nturbines)
            
            for i in range(self.parent.nturbines):
                point = self.parent._Point(internal_x[i], internal_y[i])
                
                # Check if point is inside boundary
                if hasattr(self.parent, '_boundary_polygon') and self.parent._boundary_polygon is not None:
                    if self.parent._boundary_polygon.contains(point):
                        # Point is inside, constraint satisfied
                        constraints[i] = -1.0  # Negative = satisfied
                    else:
                        # Point is outside, calculate violation distance
                        distance = self.parent._boundary_line.distance(point)
                        constraints[i] = distance  # Positive = violated
                else:
                    # Fallback: assume satisfied if no boundary defined
                    constraints[i] = -1.0
            
            return constraints
            
        except Exception as e:
            print(f"Warning: Boundary constraint evaluation failed: {e}")
            return np.ones(self.parent.nturbines)  # Violate all constraints

    def get_evaluation_stats(self):
        """Get evaluation statistics following your monitoring pattern"""
        cache_total = self.parent.monitor_data['cache_hits'] + self.parent.monitor_data['cache_misses']
        cache_hit_rate = self.parent.monitor_data['cache_hits'] / max(1, cache_total)
        
        return {
            'total_evaluations': self.eval_count,
            'cache_size': len(self._evaluation_cache),
            'cache_hit_rate': cache_hit_rate,
            'cache_hits': self.parent.monitor_data['cache_hits'],
            'cache_misses': self.parent.monitor_data['cache_misses'],
            'timeouts': self.parent.monitor_data['timeouts'],
            'avg_eval_time': np.mean(self.parent.monitor_data['eval_times']) if self.parent.monitor_data['eval_times'] else 0
        }


class SmartHybridLayoutOptimizationProblem(HybridLayoutOptimizationProblem):
    """
    Enhanced problem with smart external turbine filtering
    Integrates with SmartExternalLayoutManager for wind direction-based filtering
    """
    
    def __init__(self, parent_optimizer, smart_manager=None):
        """
        Initialize with smart external turbine management
        
        Args:
            parent_optimizer: HybridLayoutOptimizationPymoo instance
            smart_manager: SmartExternalLayoutManager for wind direction filtering
        """
        super().__init__(parent_optimizer)
        self.smart_manager = smart_manager
        self.wind_direction_cache = {}
        
        print(f"🧠 SmartHybridLayoutOptimizationProblem initialized:")
        if smart_manager:
            print(f"   Smart filtering enabled")
            print(f"   Distance threshold: {smart_manager.max_distance_m/1000:.0f}km")
        else:
            print(f"   Standard external turbine handling")

    def _safe_obj_func_external_smart(self, x):
        """
        Enhanced objective function with smart external turbine filtering
        """
        try:
            if self.smart_manager is None:
                # Fallback to standard evaluation
                return self._safe_obj_func_external(x)
            
            # Check cache first
            cache_key = tuple(np.round(x / self._cache_tolerance) * self._cache_tolerance)
            if cache_key in self._evaluation_cache:
                if self.parent.use_monitoring:
                    self.parent.monitor_data['cache_hits'] += 1
                return self._evaluation_cache[cache_key]
            
            if self.parent.use_monitoring:
                self.parent.monitor_data['cache_misses'] += 1
            
            # Create optimized internal layout
            internal_x = self.parent._unnorm(x[:self.parent.nturbines], self.parent.xmin, self.parent.xmax)
            internal_y = self.parent._unnorm(x[self.parent.nturbines:], self.parent.ymin, self.parent.ymax)
            
            # Create optimized internal layout DataFrame
            optimized_internal = self.smart_manager.internal_layout.copy()
            for i, turb_idx in enumerate(self.parent.turbs_to_opt):
                optimized_internal.loc[optimized_internal.index[i], 'x'] = internal_x[i]
                optimized_internal.loc[optimized_internal.index[i], 'y'] = internal_y[i]
            
            # Update smart manager with optimized layout
            optimized_smart_manager = self.smart_manager.__class__(
                optimized_internal, 
                self.smart_manager.external_layout, 
                self.smart_manager.max_distance_m / 1000
            )
            
            # Calculate AEP with wind direction-specific external turbines
            # For optimization efficiency, use a representative wind direction
            # or average over dominant wind directions
            dominant_wind_directions = [240]  # Use dominant wind from wind_dir_dominant.py
            
            total_aep = 0
            for wd in dominant_wind_directions:
                # Get relevant layout for this wind direction
                relevant_layout = optimized_smart_manager.get_relevant_layout(wd)
                
                # Calculate AEP contribution
                # Note: This is a simplified calculation for optimization efficiency
                # Full AEP calculation would be done in the final evaluation
                self.parent.fi.reinitialize(
                    layout=(relevant_layout['x'].values, relevant_layout['y'].values),
                    turbine_type=relevant_layout['turb'].values
                )
                
                # Extract INTERNAL WAKED AEP only (considering external wake effects)
                from utilities import calculate_gross_net_aep_efficient
                _, internal_waked_aep, _ = calculate_gross_net_aep_efficient(
                    self.parent.fi, self.parent.freq, n_internal=self.parent.nturbines
                )
                
                total_aep += internal_waked_aep
            
            # Average over wind directions
            final_internal_aep_gwh = total_aep / len(dominant_wind_directions)
            
            # Normalize objective relative to baseline internal AEP
            if self.baseline_internal_aep is not None:
                final_aep_normalized = final_internal_aep_gwh * self.normalization_factor
            else:
                # Fallback if baseline not set yet
                final_aep_normalized = final_internal_aep_gwh / 175.0  # Approximate baseline
                print(f"⚠️  Warning: Smart evaluation using fallback normalization!")
            
            # Cache result
            self._evaluation_cache[cache_key] = final_aep_normalized
            
            return final_aep_normalized
            
        except Exception as e:
            print(f"Warning: Smart external objective evaluation failed: {e}")
            # Fallback to standard evaluation
            return self._safe_obj_func_external(x)

    def _evaluate(self, X, out, *args, **kwargs):
        """
        Override to use smart evaluation if available
        """
        if self.smart_manager is not None:
            # Use smart evaluation method
            n_samples = X.shape[0]
            
            try:
                if self.parent.use_monitoring:
                    start_time = time.time()
                
                # Parallel smart evaluation
                with ProcessPoolExecutor(max_workers=self.parent.n_workers) as executor:
                    future_to_idx = {}
                    for i in range(n_samples):
                        future = executor.submit(self._safe_obj_func_external_smart, X[i])
                        future_to_idx[future] = i
                    
                    f = np.zeros(n_samples)
                    for future in as_completed(future_to_idx, timeout=self.parent.timeout_per_eval):
                        idx = future_to_idx[future]
                        try:
                            f[idx] = future.result()
                        except Exception as e:
                            print(f"Warning: Smart evaluation {idx} failed: {e}")
                            f[idx] = -1e-6
                
                # Constraint evaluation (same as parent)
                g = np.zeros((n_samples, 2 * self.parent.nturbines))
                for i in range(n_samples):
                    g[i, :self.parent.nturbines] = self._space_constraint(X[i])
                    g[i, self.parent.nturbines:] = self._distance_from_boundaries(X[i])
                
                if self.parent.use_monitoring:
                    eval_time = time.time() - start_time
                    self.parent.monitor_data['eval_times'].append(eval_time)
                
                # Convert to minimization problem
                out["F"] = (-f).reshape(-1, 1)
                out["G"] = g
                self.eval_count += n_samples
                
            except Exception as e:
                print(f"Warning: Smart evaluation failed, using standard: {e}")
                # Fallback to parent evaluation
                super()._evaluate(X, out, *args, **kwargs)
        else:
            # Use standard evaluation
            super()._evaluate(X, out, *args, **kwargs)