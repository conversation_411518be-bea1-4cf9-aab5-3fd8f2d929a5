#!/usr/bin/env python3
"""
Test Scenario Generator for DEAP Smart External Optimization
===========================================================

Creates reduced turbine count layouts for rapid development and testing while
maintaining the same wind data, boundaries, and other parameters. Supports
configurable turbine counts and enhanced wind binning for comprehensive testing.

Features:
- Configurable internal/external turbine counts
- Smart turbine selection preserving spatial distribution
- Enhanced wind speed/direction binning
- Test scenario validation
- Performance benchmarking support
"""

import os
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import shutil
from datetime import datetime

# Import utilities for layout handling
from utilities import sanitize_name


class TestScenarioGenerator:
    """
    Generates test scenarios with reduced turbine counts for rapid development
    """
    
    def __init__(self, work_dir: str = None):
        """
        Initialize test scenario generator
        
        Args:
            work_dir: Working directory path (default: current script directory)
        """
        self.work_dir = work_dir or str(Path(__file__).parent.absolute())
        self.input_dir = os.path.join(self.work_dir, 'Input')
        self.test_input_dir = os.path.join(self.work_dir, 'TestInput')
        
        # Test scenario configurations
        self.test_scenarios = {
            'small': {
                'internal_count': 5,
                'external_count': 10,
                'ws_bins': 8,
                'wd_bins': 8,
                'description': 'Small test case for rapid development'
            },
            'medium': {
                'internal_count': 8,
                'external_count': 20,
                'ws_bins': 10,
                'wd_bins': 12,
                'description': 'Medium test case for intermediate testing'
            },
            'enhanced': {
                'internal_count': 10,
                'external_count': 30,
                'ws_bins': 12,
                'wd_bins': 16,
                'description': 'Enhanced resolution test case'
            }
        }
        
    def create_test_input_directory(self):
        """Create test input directory structure"""
        os.makedirs(self.test_input_dir, exist_ok=True)
        print(f"✅ Created test input directory: {self.test_input_dir}")
        
    def load_original_layouts(self) -> Tuple[pd.DataFrame, pd.DataFrame]:
        """
        Load original internal and external turbine layouts
        
        Returns:
            Tuple of (internal_layout, external_layout) DataFrames
        """
        try:
            # Load layout files (same as smart external script)
            from glob import glob
            csv_files = glob(f"{self.input_dir}/External.layout.csv") + glob(f"{self.input_dir}/Initial.layout.csv")
            
            extra_file_path = os.path.join(self.input_dir, 'ExtraExternal.csv')
            if os.path.exists(extra_file_path):
                csv_files.append(extra_file_path)
            
            # Load and filter valid dataframes
            df_list = [pd.read_csv(file) for file in csv_files]
            filtered_df_list = [df for df in df_list if not df.empty and not df.isna().all().all()]
            all_layout = pd.concat(filtered_df_list, ignore_index=True)
            
            # Prepare layout data
            all_layout = all_layout.rename(columns={'easting': 'x', 'northing': 'y'})
            all_layout['turb'] = all_layout['turb'].apply(sanitize_name)
            
            # Separate internal and external layouts
            internal_layout = all_layout[all_layout['external'] == False].copy()
            external_layout = all_layout[all_layout['external'] == True].copy()
            
            print(f"📊 Original layouts loaded:")
            print(f"   Internal turbines: {len(internal_layout)}")
            print(f"   External turbines: {len(external_layout)}")
            
            return internal_layout, external_layout
            
        except Exception as e:
            print(f"❌ Error loading original layouts: {e}")
            raise
    
    def select_representative_turbines(self, layout: pd.DataFrame, target_count: int, 
                                     selection_method: str = 'spatial_distribution') -> pd.DataFrame:
        """
        Select representative subset of turbines maintaining spatial distribution
        
        Args:
            layout: Original turbine layout DataFrame
            target_count: Target number of turbines to select
            selection_method: Selection method ('spatial_distribution', 'random', 'clustered')
            
        Returns:
            Selected turbine layout DataFrame
        """
        if len(layout) <= target_count:
            return layout.copy()
        
        if selection_method == 'spatial_distribution':
            # Use spatial distribution to maintain farm characteristics
            x_coords = layout['x'].values
            y_coords = layout['y'].values
            
            # Create spatial grid and select turbines from different regions
            x_min, x_max = x_coords.min(), x_coords.max()
            y_min, y_max = y_coords.min(), y_coords.max()
            
            # Divide into grid cells
            grid_size = int(np.ceil(np.sqrt(target_count)))
            x_edges = np.linspace(x_min, x_max, grid_size + 1)
            y_edges = np.linspace(y_min, y_max, grid_size + 1)
            
            selected_indices = []
            turbines_per_cell = max(1, target_count // (grid_size * grid_size))
            
            for i in range(grid_size):
                for j in range(grid_size):
                    if len(selected_indices) >= target_count:
                        break
                        
                    # Find turbines in this grid cell
                    mask = ((x_coords >= x_edges[i]) & (x_coords < x_edges[i+1]) &
                           (y_coords >= y_edges[j]) & (y_coords < y_edges[j+1]))
                    
                    cell_indices = np.where(mask)[0]
                    if len(cell_indices) > 0:
                        # Select turbines from this cell
                        n_select = min(turbines_per_cell, len(cell_indices), 
                                     target_count - len(selected_indices))
                        selected_cell_indices = np.random.choice(cell_indices, n_select, replace=False)
                        selected_indices.extend(selected_cell_indices)
            
            # Fill remaining spots if needed
            remaining_count = target_count - len(selected_indices)
            if remaining_count > 0:
                available_indices = list(set(range(len(layout))) - set(selected_indices))
                if available_indices:
                    additional_indices = np.random.choice(available_indices, 
                                                        min(remaining_count, len(available_indices)), 
                                                        replace=False)
                    selected_indices.extend(additional_indices)
            
            selected_layout = layout.iloc[selected_indices[:target_count]].copy().reset_index(drop=True)
            
        elif selection_method == 'random':
            # Random selection
            selected_indices = np.random.choice(len(layout), target_count, replace=False)
            selected_layout = layout.iloc[selected_indices].copy().reset_index(drop=True)
            
        elif selection_method == 'clustered':
            # Select turbines from center of layout (clustered)
            center_x = layout['x'].mean()
            center_y = layout['y'].mean()
            distances = np.sqrt((layout['x'] - center_x)**2 + (layout['y'] - center_y)**2)
            closest_indices = np.argsort(distances)[:target_count]
            selected_layout = layout.iloc[closest_indices].copy().reset_index(drop=True)
            
        else:
            raise ValueError(f"Unknown selection method: {selection_method}")
        
        print(f"   Selected {len(selected_layout)} turbines using {selection_method} method")
        return selected_layout
    
    def enhance_wind_binning(self, ws_bins: int = 8, wd_bins: int = 8) -> Dict:
        """
        Create enhanced wind speed and direction binning
        
        Args:
            ws_bins: Number of wind speed bins
            wd_bins: Number of wind direction bins
            
        Returns:
            Dictionary with enhanced wind parameters
        """
        # Enhanced wind speed bins (typically 3-25 m/s)
        ws_array = np.linspace(3.0, 25.0, ws_bins)
        
        # Enhanced wind direction bins (0-360 degrees)
        wd_array = np.linspace(0.0, 360.0, wd_bins, endpoint=False)
        
        # Create analysis directions from wind direction bins
        analysis_directions = wd_array.tolist()
        
        wind_config = {
            'ws_array': ws_array,
            'wd_array': wd_array,
            'analysis_directions': analysis_directions,
            'ws_bins': ws_bins,
            'wd_bins': wd_bins
        }
        
        print(f"📈 Enhanced wind binning:")
        print(f"   Wind speeds: {ws_bins} bins ({ws_array[0]:.1f} - {ws_array[-1]:.1f} m/s)")
        print(f"   Wind directions: {wd_bins} bins (0 - {wd_array[-1]:.0f}°)")
        
        return wind_config
    
    def generate_test_scenario(self, scenario_name: str, output_subdir: str = None) -> Dict:
        """
        Generate complete test scenario with reduced turbine counts
        
        Args:
            scenario_name: Name of test scenario ('small', 'medium', 'enhanced')
            output_subdir: Optional subdirectory for this scenario
            
        Returns:
            Dictionary with test scenario configuration and paths
        """
        if scenario_name not in self.test_scenarios:
            raise ValueError(f"Unknown scenario: {scenario_name}. Available: {list(self.test_scenarios.keys())}")
        
        config = self.test_scenarios[scenario_name]
        print(f"\n🔄 Generating test scenario: {scenario_name}")
        print(f"   {config['description']}")
        
        # Create output directory
        if output_subdir:
            scenario_dir = os.path.join(self.test_input_dir, output_subdir)
        else:
            scenario_dir = os.path.join(self.test_input_dir, f"scenario_{scenario_name}")
        os.makedirs(scenario_dir, exist_ok=True)
        
        # Load original layouts
        internal_layout, external_layout = self.load_original_layouts()
        
        # Select representative turbines
        test_internal = self.select_representative_turbines(
            internal_layout, config['internal_count'], 'spatial_distribution'
        )
        test_external = self.select_representative_turbines(
            external_layout, config['external_count'], 'spatial_distribution'
        )
        
        # Save test layouts
        test_internal.to_csv(os.path.join(scenario_dir, 'Initial.layout.csv'), index=False)
        test_external.to_csv(os.path.join(scenario_dir, 'External.layout.csv'), index=False)
        
        # Copy other necessary files
        necessary_files = [
            'timeseries.txt',
            'Boundaries.csv',
            'Boundaries-genPoints.csv',
            'config_Jensen_FLS_Validation_FastAEP.yaml'
        ]
        
        for file_name in necessary_files:
            src_path = os.path.join(self.input_dir, file_name)
            dst_path = os.path.join(scenario_dir, file_name)
            if os.path.exists(src_path):
                shutil.copy2(src_path, dst_path)
        
        # Generate enhanced wind configuration
        wind_config = self.enhance_wind_binning(config['ws_bins'], config['wd_bins'])
        
        # Create test scenario summary
        scenario_summary = {
            'scenario_name': scenario_name,
            'scenario_dir': scenario_dir,
            'internal_count': len(test_internal),
            'external_count': len(test_external),
            'total_count': len(test_internal) + len(test_external),
            'original_internal': len(internal_layout),
            'original_external': len(external_layout),
            'reduction_factor': (len(internal_layout) + len(external_layout)) / (len(test_internal) + len(test_external)),
            'wind_config': wind_config,
            'config': config,
            'generated_at': datetime.now().isoformat()
        }
        
        # Save scenario configuration
        import json
        config_file = os.path.join(scenario_dir, 'test_scenario_config.json')
        with open(config_file, 'w') as f:
            json.dump(scenario_summary, f, indent=2, default=str)
        
        print(f"✅ Test scenario '{scenario_name}' generated:")
        print(f"   Directory: {scenario_dir}")
        print(f"   Internal: {len(test_internal)} (was {len(internal_layout)})")
        print(f"   External: {len(test_external)} (was {len(external_layout)})")
        print(f"   Reduction factor: {scenario_summary['reduction_factor']:.1f}x")
        print(f"   Wind conditions: {config['ws_bins']} × {config['wd_bins']} = {config['ws_bins'] * config['wd_bins']} combinations")
        
        return scenario_summary
    
    def generate_all_scenarios(self) -> Dict[str, Dict]:
        """
        Generate all predefined test scenarios
        
        Returns:
            Dictionary mapping scenario names to their configurations
        """
        print("🚀 Generating all test scenarios...")
        self.create_test_input_directory()
        
        all_scenarios = {}
        for scenario_name in self.test_scenarios.keys():
            try:
                scenario_config = self.generate_test_scenario(scenario_name)
                all_scenarios[scenario_name] = scenario_config
            except Exception as e:
                print(f"❌ Failed to generate scenario '{scenario_name}': {e}")
                continue
        
        print(f"\n✅ Generated {len(all_scenarios)} test scenarios successfully!")
        return all_scenarios
    
    def create_performance_comparison_config(self, scenarios: Dict[str, Dict]) -> str:
        """
        Create configuration for performance comparison testing
        
        Args:
            scenarios: Dictionary of generated scenarios
            
        Returns:
            Path to performance comparison configuration file
        """
        comparison_config = {
            'test_scenarios': scenarios,
            'performance_metrics': [
                'optimization_time',
                'function_evaluations',
                'memory_usage',
                'cache_hit_rate',
                'convergence_generations'
            ],
            'benchmark_parameters': {
                'runs_per_scenario': 3,
                'timeout_seconds': 1800,
                'enable_profiling': True,
                'track_memory': True
            },
            'created_at': datetime.now().isoformat()
        }
        
        config_file = os.path.join(self.test_input_dir, 'performance_comparison_config.json')
        import json
        with open(config_file, 'w') as f:
            json.dump(comparison_config, f, indent=2, default=str)
        
        print(f"📊 Performance comparison config saved: {config_file}")
        return config_file


def main():
    """Main function - generate test scenarios"""
    print("🧪 DEAP Smart External Optimization - Test Scenario Generator")
    print("=" * 70)
    
    try:
        # Initialize generator
        generator = TestScenarioGenerator()
        
        # Generate all test scenarios
        scenarios = generator.generate_all_scenarios()
        
        # Create performance comparison configuration
        generator.create_performance_comparison_config(scenarios)
        
        print("\n🎯 Test scenarios ready for development and testing!")
        print("   Use these scenarios to rapidly test performance enhancements.")
        print("   Each scenario maintains the same wind data and boundaries.")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test scenario generation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)