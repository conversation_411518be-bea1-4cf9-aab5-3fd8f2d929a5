#!/usr/bin/env python3
"""
Add early termination methods to DEAP manager
"""

early_termination_methods = '''
    def _check_early_termination(self, population):
        """Check if optimization should terminate early (PyMoo-style convergence)"""
        if not self.config.enable_early_termination:
            return False
            
        # Get current best fitness
        if population:
            current_best = max(ind.fitness.values[0] for ind in population)
            self.best_fitness_history.append(current_best)
            
            # Check for improvement
            if len(self.best_fitness_history) >= 2:
                improvement = abs(self.best_fitness_history[-1] - self.best_fitness_history[-2])
                
                if improvement < self.config.convergence_tol:
                    self.generations_without_improvement += 1
                else:
                    self.generations_without_improvement = 0
                
                # Terminate if no improvement for patience generations
                if self.generations_without_improvement >= self.config.patience:
                    if self.config.verbose:
                        print(f"   🏁 Early termination: no improvement for {self.config.patience} generations")
                        print(f"      Best fitness: {current_best:.6f}")
                        print(f"      Convergence tolerance: {self.config.convergence_tol}")
                    return True
        
        return False
    
    def _update_termination_stats(self, generation, population):
        """Update termination statistics for monitoring"""
        if population:
            current_best = max(ind.fitness.values[0] for ind in population)
            if self.config.verbose and generation % 5 == 0:  # Print every 5 generations
                print(f"   Gen {generation:2d}: Best={current_best:.6f}, No improvement: {self.generations_without_improvement}")
'''

print("Early termination methods:")
print(early_termination_methods)