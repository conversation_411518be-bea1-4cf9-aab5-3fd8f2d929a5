#!/usr/bin/env python3
"""
Test script to verify DEAP turbine weights functionality
Tests optimization with and without external turbines
"""

import sys
import os
from pathlib import Path

# Set up paths
workDir = str(Path(__file__).parent.absolute())
floris_path = f"{workDir}/FLORIS_311_VF1_Operational"
sys.path.append(floris_path)

import numpy as np
import pandas as pd
import params as pr
from core.tools import FlorisInterface
from deap_optimization_manager import DEAPOptimizationManager, DEAPProblemWrapper, OptimizationConfig
from utilities import load_floris_and_layout, ts_to_freq_df

def test_without_weights():
    """Test DEAP optimization without turbine weights (all turbines optimizable)"""
    print("\n" + "="*60)
    print("TEST 1: DEAP Optimization WITHOUT Turbine Weights")
    print("="*60)
    
    # Load FLORIS and layout
    fi, layout, boundaries = load_floris_and_layout()
    
    # Load frequency data
    ts = pd.read_csv(pr.inputLayoutFile.rsplit('/', 1)[0] + '/timeseries.txt', sep=' ')
    WR = ts_to_freq_df(ts)
    wd_array = np.array(sorted(WR["wd"].unique()), dtype=float)
    ws_array = np.array(sorted(WR["ws"].unique()), dtype=float)
    freq = WR.set_index(['wd','ws']).unstack().values
    
    # Initialize FLORIS
    fi.reinitialize(layout=(layout['x'], layout['y']),
                    turbine_type=layout['turb'],
                    wind_directions=wd_array,
                    wind_speeds=ws_array)
    
    # Calculate baseline AEP
    baseline_aep = fi.get_farm_AEP(freq=freq) / 1E9
    print(f"Baseline AEP: {baseline_aep:.3f} GWh")
    print(f"Number of turbines: {len(layout)}")
    
    # Create problem WITHOUT weights
    problem = DEAPProblemWrapper(
        fi=fi,
        freq=freq,
        layout=layout,
        boundaries=boundaries,
        baseline_aep=baseline_aep
    )
    
    # Configure and run optimization
    config = OptimizationConfig(
        algorithm='GA',
        pop_size=pr.PopSize,
        n_gen=pr.MAXGEN,
        crossover_prob=0.9,
        eta_c=10.0,
        eta_m=50.0,
        verbose=True
    )
    
    optimizer = DEAPOptimizationManager(problem, config)
    best_solution, best_fitness, stats = optimizer.optimize()
    
    print(f"\nOptimization complete!")
    print(f"Best fitness: {best_fitness:.4f}")
    print(f"Final AEP: {best_fitness * baseline_aep:.3f} GWh")
    
    return best_fitness * baseline_aep


def test_with_weights():
    """Test DEAP optimization with turbine weights (internal/external separation)"""
    print("\n" + "="*60)
    print("TEST 2: DEAP Optimization WITH Turbine Weights")
    print("="*60)
    
    # Load FLORIS and layout
    fi, layout, boundaries = load_floris_and_layout()
    
    # Load frequency data
    ts = pd.read_csv(pr.inputLayoutFile.rsplit('/', 1)[0] + '/timeseries.txt', sep=' ')
    WR = ts_to_freq_df(ts)
    wd_array = np.array(sorted(WR["wd"].unique()), dtype=float)
    ws_array = np.array(sorted(WR["ws"].unique()), dtype=float)
    freq = WR.set_index(['wd','ws']).unstack().values
    
    # Create turbine weights (first half internal, second half external)
    n_turbines = len(layout)
    n_internal = n_turbines // 2
    turbines_weights = np.ones(n_turbines)
    turbines_weights[n_internal:] = 0  # Mark second half as external
    
    print(f"Total turbines: {n_turbines}")
    print(f"Internal turbines (weight=1): {np.sum(turbines_weights == 1)}")
    print(f"External turbines (weight=0): {np.sum(turbines_weights == 0)}")
    
    # Initialize FLORIS
    fi.reinitialize(layout=(layout['x'], layout['y']),
                    turbine_type=layout['turb'],
                    wind_directions=wd_array,
                    wind_speeds=ws_array)
    
    # Calculate baseline AEP
    baseline_aep = fi.get_farm_AEP(freq=freq) / 1E9
    print(f"Baseline AEP: {baseline_aep:.3f} GWh")
    
    # Create problem WITH weights
    problem = DEAPProblemWrapper(
        fi=fi,
        freq=freq,
        layout=layout,
        boundaries=boundaries,
        baseline_aep=baseline_aep,
        turbines_weights=turbines_weights
    )
    
    # Configure and run optimization
    config = OptimizationConfig(
        algorithm='GA',
        pop_size=pr.PopSize,
        n_gen=pr.MAXGEN,
        crossover_prob=0.9,
        eta_c=10.0,
        eta_m=50.0,
        verbose=True
    )
    
    optimizer = DEAPOptimizationManager(problem, config)
    best_solution, best_fitness, stats = optimizer.optimize()
    
    print(f"\nOptimization complete!")
    print(f"Best fitness: {best_fitness:.4f}")
    print(f"Final AEP: {best_fitness * baseline_aep:.3f} GWh")
    
    return best_fitness * baseline_aep


if __name__ == "__main__":
    print("DEAP Turbine Weights Test")
    print("Testing DEAP optimization with reduced parameters:")
    print(f"Population size: {pr.PopSize}")
    print(f"Max generations: {pr.MAXGEN}")
    
    # Run tests
    aep_without_weights = test_without_weights()
    aep_with_weights = test_with_weights()
    
    # Compare results
    print("\n" + "="*60)
    print("COMPARISON SUMMARY")
    print("="*60)
    print(f"WITHOUT weights (all turbines optimized): {aep_without_weights:.3f} GWh")
    print(f"WITH weights (only internal optimized):   {aep_with_weights:.3f} GWh")
    print("="*60)