#!/usr/bin/env python3
"""
Quick Boundary Constraint Diagnostic
====================================
Test boundary loading and constraint evaluation for PyMoo scripts
"""

import numpy as np
import pandas as pd
import os
from pathlib import Path
from shapely.geometry import Point, Polygon
from scipy.spatial.distance import cdist
import matplotlib.pyplot as plt

# Import project modules
import params as pr
from utilities import load_boundaries, process_multi_polygon_boundaries

# Set up paths
workDir = str(Path(__file__).parent.absolute())
boundary_file_path = os.path.join(workDir, pr.boundariesFile.lstrip('./'))

print("🔍 BOUNDARY CONSTRAINT DIAGNOSTIC")
print("=" * 50)

# Test 1: Boundary file loading
print("\n1. TESTING BOUNDARY FILE LOADING")
print(f"Boundary file path: {boundary_file_path}")
print(f"File exists: {os.path.exists(boundary_file_path)}")

if os.path.exists(boundary_file_path):
    try:
        boundaries_df = pd.read_csv(boundary_file_path)
        print(f"Boundary file loaded: {len(boundaries_df)} points")
        print(f"Columns: {boundaries_df.columns.tolist()}")
        print(f"Sample data:\n{boundaries_df.head()}")
        
        # Convert to boundary list
        boundaries = list(zip(boundaries_df['X'], boundaries_df['Y']))
        print(f"Boundary list created: {len(boundaries)} points")
        
        # Calculate coordinate ranges
        xmin = np.min([tup[0] for tup in boundaries])
        xmax = np.max([tup[0] for tup in boundaries])
        ymin = np.min([tup[1] for tup in boundaries])
        ymax = np.max([tup[1] for tup in boundaries])
        
        print(f"X range: {xmin:.2f} to {xmax:.2f} ({xmax-xmin:.2f}m)")
        print(f"Y range: {ymin:.2f} to {ymax:.2f} ({ymax-ymin:.2f}m)")
        
    except Exception as e:
        print(f"❌ ERROR loading boundary file: {e}")
        exit(1)
else:
    print("❌ Boundary file not found!")
    exit(1)

# Test 2: Process multi-polygon boundaries
print("\n2. TESTING MULTI-POLYGON BOUNDARY PROCESSING")
try:
    processed_boundaries = process_multi_polygon_boundaries(boundaries)
    print(f"Processed boundaries: {len(processed_boundaries)} points")
    
    # Create polygon
    boundary_polygon = Polygon(processed_boundaries)
    print(f"Polygon valid: {boundary_polygon.is_valid}")
    print(f"Polygon area: {boundary_polygon.area:.2f} m²")
    print(f"Polygon bounds: {boundary_polygon.bounds}")
    
except Exception as e:
    print(f"❌ ERROR processing boundaries: {e}")
    boundary_polygon = Polygon(boundaries)

# Test 3: Load initial layout
print("\n3. TESTING INITIAL LAYOUT LOADING")
try:
    # Load layout data
    x0_file = f"{workDir}/x0_file.txt"
    y0_file = f"{workDir}/y0_file.txt"
    
    if os.path.exists(x0_file) and os.path.exists(y0_file):
        x0 = np.loadtxt(x0_file, delimiter=',')
        y0 = np.loadtxt(y0_file, delimiter=',')
        print(f"Initial layout loaded: {len(x0)} turbines")
        print(f"X range: {np.min(x0):.2f} to {np.max(x0):.2f}")
        print(f"Y range: {np.min(y0):.2f} to {np.max(y0):.2f}")
        
        # Test boundary constraints for initial layout
        print("\n4. TESTING INITIAL LAYOUT BOUNDARY CONSTRAINTS")
        boundary_violations = 0
        for i in range(len(x0)):
            point = Point(x0[i], y0[i])
            inside = boundary_polygon.contains(point)
            if not inside:
                boundary_violations += 1
                if boundary_violations <= 5:  # Show first 5 violations
                    distance = point.distance(boundary_polygon.boundary)
                    print(f"   Turbine {i}: OUTSIDE boundary by {distance:.2f}m at ({x0[i]:.2f}, {y0[i]:.2f})")
        
        print(f"Boundary violations: {boundary_violations}/{len(x0)} turbines")
        print(f"Feasible turbines: {len(x0) - boundary_violations}/{len(x0)}")
        
        # Test minimum distance constraints
        print("\n5. TESTING MINIMUM DISTANCE CONSTRAINTS")
        min_dist = 1500.0  # From optimizer_params.py
        locs = np.vstack((x0, y0)).T
        distances = cdist(locs, locs)
        np.fill_diagonal(distances, np.inf)
        
        distance_violations = np.sum(distances < min_dist)
        print(f"Minimum distance: {min_dist}m")
        print(f"Distance violations: {distance_violations} pairs")
        print(f"Min distance in layout: {np.min(distances):.2f}m")
        
    else:
        print("❌ Initial layout files not found!")
        x0 = y0 = None
        
except Exception as e:
    print(f"❌ ERROR testing initial layout: {e}")
    x0 = y0 = None

# Test 4: Generate random layout and test constraints
print("\n6. TESTING RANDOM LAYOUT GENERATION")
try:
    np.random.seed(42)
    n_test = 10
    
    # Generate random coordinates within bounding box
    x_random = np.random.uniform(xmin, xmax, n_test)
    y_random = np.random.uniform(ymin, ymax, n_test)
    
    # Test boundary constraints
    boundary_violations_random = 0
    for i in range(n_test):
        point = Point(x_random[i], y_random[i])
        inside = boundary_polygon.contains(point)
        if not inside:
            boundary_violations_random += 1
    
    print(f"Random layout test ({n_test} turbines):")
    print(f"Boundary violations: {boundary_violations_random}/{n_test}")
    print(f"Success rate: {(n_test - boundary_violations_random)/n_test*100:.1f}%")
    
    # Test if bounding box method works for initial population
    if boundary_violations_random > n_test * 0.8:
        print("⚠️  WARNING: Bounding box sampling has high failure rate!")
        print("   This explains why PyMoo generates mostly infeasible solutions")
    
except Exception as e:
    print(f"❌ ERROR testing random layout: {e}")

# Test 5: Normalization/denormalization
print("\n7. TESTING COORDINATE NORMALIZATION")
try:
    if x0 is not None and y0 is not None:
        # Test PyMoo normalization approach
        def norm(val, lb, ub):
            return (val - lb) / (ub - lb)
        
        def unnorm(val, lb, ub):
            return val * (ub - lb) + lb
        
        # Normalize initial coordinates
        x0_norm = [norm(x, xmin, xmax) for x in x0]
        y0_norm = [norm(y, ymin, ymax) for y in y0]
        
        print(f"Normalized X range: {np.min(x0_norm):.3f} to {np.max(x0_norm):.3f}")
        print(f"Normalized Y range: {np.min(y0_norm):.3f} to {np.max(y0_norm):.3f}")
        
        # Denormalize back
        x0_denorm = [unnorm(x, xmin, xmax) for x in x0_norm]
        y0_denorm = [unnorm(y, ymin, ymax) for y in y0_norm]
        
        # Check accuracy
        x_error = np.max(np.abs(np.array(x0) - np.array(x0_denorm)))
        y_error = np.max(np.abs(np.array(y0) - np.array(y0_denorm)))
        
        print(f"Normalization accuracy:")
        print(f"   Max X error: {x_error:.6f}m")
        print(f"   Max Y error: {y_error:.6f}m")
        
        if x_error > 0.1 or y_error > 0.1:
            print("⚠️  WARNING: Normalization accuracy issues!")
        
except Exception as e:
    print(f"❌ ERROR testing normalization: {e}")

print("\n" + "=" * 50)
print("DIAGNOSTIC COMPLETE")

if boundary_violations > len(x0) * 0.1:
    print("🚨 CRITICAL: High boundary violation rate in initial layout")
    print("   → This explains PyMoo feasibility issues")
    print("   → Need to fix initial population generation")
else:
    print("✅ Initial layout mostly feasible")
    print("   → Problem may be in constraint evaluation during optimization")