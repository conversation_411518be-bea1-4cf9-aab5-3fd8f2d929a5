
from utilities import *




from numpy import genfromtxt
from scipy.spatial.distance import cdist

import params as pr
import optimizer_params as optimizer_etc 
import wind_dir_dominant as winddir

from pathlib import Path
print("RunFloris.py      Path:", Path(__file__).parent.absolute())
workDir=str( Path(__file__).parent.absolute())
OverAllProgress = workDir + "/OverAllProgress.txt"

        
print(f"OverAllProgress : " , OverAllProgress)


dirName=workDir
floris_path   =  dirName + '/FLORIS_311_VF1_Operational'
print(f"floris_path : {floris_path}" )
turb_lib_path =  dirName + '/FLORIS_311_VF1_Operational/floris/turbine_library'
print(f"turb_lib_path : {turb_lib_path}" )



sys.path.append(floris_path)

from scipy.interpolate import NearestNDInterpolator
from datetime import datetime
from time import perf_counter as timerpc
import matplotlib.pyplot as plt
from pymoo.algorithms.moo.nsga2 import NSGA2
from pymoo.core.crossover import Crossover
from pymoo.core.mutation import Mutation
from pymoo.operators.crossover.sbx import SBX
from pymoo.operators.mutation.pm import PolynomialMutation
from pymoo.operators.sampling.rnd import FloatRandomSampling
from pymoo.problems import get_problem
from pymoo.optimize import minimize



from  floris.tools.optimization.layout_optimization.LayoutOptimizationPymoo import LayoutOptimizationPymoo  







from shapely.geometry import Polygon, LineString

def find_closest_points(polygon1, polygon2):
    min_distance = float('inf')
    closest_points = (None, None)
    for idx1, row1 in polygon1.iterrows():
        for idx2, row2 in polygon2.iterrows():
            distance = ((row1['X'] - row2['X']) ** 2 + (row1['Y'] - row2['Y']) ** 2) ** 0.5
            if distance < min_distance:
                min_distance = distance
                closest_points = (row1, row2)
    return closest_points

def concatenate_polygons(polygon1, polygon2, connecting_point1, connecting_point2):
    points1 = list(zip(polygon1['X'], polygon1['Y']))
    points2 = list(zip(polygon2['X'], polygon2['Y']))
    start_idx1 = min(range(len(points1)), key=lambda i: ((points1[i][0] - connecting_point1['X'])**2 + (points1[i][1] - connecting_point1['Y'])**2)**0.5)
    start_idx2 = min(range(len(points2)), key=lambda i: ((points2[i][0] - connecting_point2['X'])**2 + (points2[i][1] - connecting_point2['Y'])**2)**0.5)
    
    connecting_line = [points1[start_idx1], points2[start_idx2]]
    
    concatenated_points = points1[:start_idx1] + connecting_line + points2[start_idx2:] + points2[:start_idx2] + connecting_line[::-1] + points1[start_idx1:]
    return concatenated_points

def find_closest_points2(polygon1, polygon2):
    min_distance = float('inf')
    closest_points = (None, None)
    for idx1, row1 in polygon1.iterrows():
        for idx2, row2 in polygon2.iterrows():
            distance = ((row1['lng'] - row2['lng']) ** 2 + (row1['lat'] - row2['lat']) ** 2) ** 0.5
            if distance < min_distance:
                min_distance = distance
                closest_points = (row1, row2)
    return closest_points

def concatenate_polygons2(polygon1, polygon2, connecting_point1, connecting_point2):
    points1 = list(zip(polygon1['lng'], polygon1['lat']))
    points2 = list(zip(polygon2['lng'], polygon2['lat']))
    start_idx1 = min(range(len(points1)), key=lambda i: ((points1[i][0] - connecting_point1['lng'])**2 + (points1[i][1] - connecting_point1['lat'])**2)**0.5)
    start_idx2 = min(range(len(points2)), key=lambda i: ((points2[i][0] - connecting_point2['lng'])**2 + (points2[i][1] - connecting_point2['lat'])**2)**0.5)
    
    connecting_line = [points1[start_idx1], points2[start_idx2]]
    
    concatenated_points = points1[:start_idx1] + connecting_line + points2[start_idx2:] + points2[:start_idx2] + connecting_line[::-1] + points1[start_idx1:]
    return concatenated_points



def run_layoutOpt (inputs_loc=workDir+"/Input/" ,
        a_file = 'a_par.txt' ,
        freq_file = 'freq_table.txt',
        k_file = 'k_par.txt',
        layout_file = 'layout_SB.csv' , # 'layout-1000.csv'# 'layout_SB.csv',
        input_config = 'config_Jensen_FLS_Validation_FastAEP.yaml',
        logfile_name = r"mylogfile.txt" ,  ## dir is the same as "outputs_loc",
        optLog= r"optim_logfile.txt" ,
        finaloptLog= r"finalOptim_logfile.txt" ,
        outputs_loc=workDir+"/Output/" , 
        shp_loc=workDir+"/shp/"  , 
        parallel=True,
        PlotFlowfield = False ,
        GeneratingTurbEffContourPlots=False
    ):
    print("========================================")
    print("User given inputs:")
    print(f"inputs_loc : {inputs_loc}" )
    print(f"outputs_loc : {outputs_loc}" )
    print(f"layout_file : {layout_file}" )
    print(f"input_config : {input_config}" )
    print(f"logfile_name : {logfile_name}" )
    print("========================================")
    
    ref_ht = None # ref_ht = 94.6
    
    
    os.chdir(inputs_loc)

    if os.path.exists(outputs_loc+'turbine_library')==True:
        shutil.rmtree(outputs_loc+'turbine_library')

    shutil.copytree(turb_lib_path,outputs_loc+'turbine_library')
    
    log_loc = '{}/Output/{}'.format(os.path.dirname(os.getcwd()),logfile_name)
    if os.path.exists(log_loc):
        log = open(log_loc, "w")
        log.seek(0)
        log.truncate()
    
    start_floris =timerpc()
    ts  = pd.read_csv(inputs_loc+"timeseries.txt" , sep =' ')
    WR = ts_to_freq_df (ts)
    
    write_log('Convert weibull parameters to a windrose format useable for floris runtime: %.2f  sec ' % (timerpc()-start_floris),log_loc)
    print (f" size  of WR {len(WR.index)}")
    
    turbs = []
    for file in os.listdir():
        if 'turb.csv' in file:
            new_turb = {}
            turb_df = pd.read_csv(file)
    
            safe_name = re.sub(r'[\/:*?"<>|]', '_', str(turb_df['name'][0])).lower()
            new_turb = {'turbine_type':safe_name,
                     'generator_efficiency':float(1.0),
                     'hub_height':float(turb_df['hh'][0]),
                     'pP':float(1.88),
                     'pT':float(1.88),
                     'rotor_diameter':float(turb_df['dia'][0]),
                     'TSR':float(8.0),
                     'power_thrust_table':
                            {'power':turb_df['cp'].to_list(),
                             'thrust':turb_df['ct'].to_list(),
                             'wind_speed': turb_df['ws'].to_list()}
                     }
    
            outname = '{}/{}.yaml'.format(turb_lib_path, safe_name)
    
            with open(outname, 'w') as yaml_file:
                oyaml.dump(new_turb, yaml_file)
    


    print ("# #######################################")
    print ("#  layout Optimization                   ")
    print ("# #######################################")
    
    layout = pd.read_csv("Initial.layout.csv")
    layout = layout.rename(columns={'easting':'x','northing':'y'})
    layout['turb'] = layout['turb'].apply(sanitize_name)
    
    
    fi = FlorisInterface(input_config)
    wd_array = np.array(WR["wd"].unique(), dtype=float)
    ws_array = np.array(WR["ws"].unique(), dtype=float)
    freq = WR.set_index(['wd','ws']).unstack().values
   

                    
    
    
    fi.reinitialize(layout=(layout['x'],layout['y']), 
                    turbine_type=layout['turb'],
                    wind_directions=wd_array,
                    wind_speeds=ws_array)
    
    
    print(f"size of wd_array : {fi.floris.flow_field.n_wind_directions}"  )
    print(f"size of ws_array : {fi.floris.flow_field.n_wind_speeds}"  )


    internal_turb = layout.loc[layout['external']==False,'turb'].unique()
    if ref_ht==None:
        if len(internal_turb)>1:
            hhs = []
            for turb in internal_turb:
                idx = layout.loc[layout['turb']==turb].index[0]
                hhs.append(fi.floris.farm.hub_heights[idx])
            ref_ht = sum(hhs)/len(hhs)
            write_log('Two different types of turbines are specified as internal (belonging to current park / AEP calculation). We have therefore selected the reference wind height to be the average of the turbines hub heights.',log_loc)
        else:
            internal_turb_idx = test = layout.loc[layout['external'] == False].index[0]
            ref_ht = fi.floris.farm.hub_heights[internal_turb_idx]

    fi.floris.flow_field.reference_wind_height = ref_ht

    

    start_init=timerpc()
    fi.calculate_no_wake()
    print('calculate_no_wake runtime: %.2f  sec ' % (timerpc()- start_init) )



    farm_power_array = fi.get_farm_power() / 1E6 # in MW
    turbine_powers   = fi.get_turbine_powers() / 1E6 # In MW
    turbine_powers   = np.nan_to_num(turbine_powers )
    
    
   
    df = pd.read_csv('Boundaries.csv')

    
    
    polygons = [df[df['L1'] == label] for label in df['L1'].unique()]
    
    polygons = sorted(polygons, key=lambda polygon: Polygon(list(zip(polygon['X'], polygon['Y']))).centroid.y)
    
    concatenated_shape_points = list(zip(polygons[0]['X'], polygons[0]['Y']))
    while len(polygons) > 1:
        min_distance = float('inf')
        closest_polygon_idx = None
        closest_points = (None, None)
        for i in range(1, len(polygons)):
            points = find_closest_points(pd.DataFrame(concatenated_shape_points, columns=['X', 'Y']), polygons[i])
            distance = ((points[0]['X'] - points[1]['X']) ** 2 + (points[0]['Y'] - points[1]['Y']) ** 2) ** 0.5
            if distance < min_distance:
                min_distance = distance
                closest_polygon_idx = i
                closest_points = points
    
        concatenated_shape_points = concatenate_polygons(pd.DataFrame(concatenated_shape_points, columns=['X', 'Y']), polygons[closest_polygon_idx], closest_points[0], closest_points[1])
    
        del polygons[closest_polygon_idx]

    
    
    
    
    
    
    
    list_zip = concatenated_shape_points
    
    boundaries3 =  list (list_zip)
    BnDdata2 = genfromtxt( 
      shp_loc + '/shp_coord_total.csv', 
      delimiter=',' ,
      names=True,
      usecols=("lng","lat" )  ) 
    list_zip2 =  zip (  BnDdata2 ['lng'] , BnDdata2 ['lat'] )
    boundaries2  =list (list_zip2)
    


    def plotContraints(boundaries=boundaries3):
        plt.figure(figsize=(9, 6))
        fontsize = 16
        plt.xlabel("x (m)", fontsize=fontsize)
        plt.ylabel("y (m)", fontsize=fontsize)
        plt.axis("equal")
        plt.grid()
        verts = boundaries
        print ("size  verts : " , len(verts ) )
        
        for i in range(len(boundaries)):
            if i == len(boundaries) - 1:
                plt.plot([boundaries[i][0], boundaries[0][0]], [boundaries[i][1], boundaries[0][1]], "b")
            else:
                plt.plot([boundaries[i][0], boundaries[i + 1][0]], [boundaries[i][1], boundaries[i + 1][1]], "b")

        plt.savefig ("constraints.png")
    
    
    def plot_layout_beforeOptim(  x0=fi.layout_x ,y0=fi.layout_y , boundaries=boundaries3 ):
        plt.figure(figsize=(9, 6))
        fontsize = 16
        print(" #####  write to file : ")
        with open('x0_file.txt', 'w') as f:
            for x in x0:
              f.write(f"{x}\n")
        with open('y0_file.txt', 'w') as f:
            for y in y0:
              f.write(f"{y}\n")
        plt.scatter(x=x0, y=y0 )
        plt.xlabel("x (m)", fontsize=fontsize)
        plt.ylabel("y (m)", fontsize=fontsize)
        plt.axis("equal")
        plt.grid()
        plt.MaxNLocator(5)

        verts = boundaries
        print ("size  verts : " , len(verts ) )
        
        for i in range(len(verts)):
            if i == len(verts) - 1:
                plt.plot([verts[i][0], verts[0][0]], [verts[i][1], verts[0][1]], "b")
            else:
                plt.plot([verts[i][0], verts[i + 1][0]], [verts[i][1], verts[i + 1][1]], "b")

        plt.savefig ("0.png")
    
    plotContraints(boundaries=boundaries3)
    
    plot_layout_beforeOptim()

    x0=fi.layout_x 
    y0=fi.layout_y    

    if  hasattr( pr , 'solver') :
        solver=pr.solver
    else :
        solver='SLSQP'
    
    
    
    turbine_powers = GetAEPParallel ( fi  , max_workers = 6 , n_wind_direction_splits = 3 , n_wind_speed_splits=2 ) #best

    ordered = []
    colnames = ['{}_MW'.format(i) for i in layout['turb_ID']]
    for _, row in WR.iterrows():
        ws = row['ws']
        wd = row['wd']
        wd_idx = np.where(wd_array==wd )
        ws_idx = np.where(ws_array==ws)
        powers = turbine_powers[wd_idx,ws_idx]
        ordered.append(powers)

    keep_turbs = ['{}_MW'.format(layout.loc[c,'turb_ID']) for c in layout.index if layout.loc[c,'external']==False]

    turbine_powers_df = pd.DataFrame(np.squeeze(ordered),columns=colnames)
    turbine_powers_df = turbine_powers_df.fillna(0)
    turbine_powers_df = turbine_powers_df[keep_turbs]

    waked_final_results = pd.concat([WR,turbine_powers_df],axis=1)

    turbine_aeps = []
    for i in keep_turbs:
        turbine_aeps.append(np.dot(waked_final_results['freq'],waked_final_results[i])*8766/1E03)
    
    print ("sum(turbine_aeps) : " , sum(turbine_aeps) )
    base_aep =  sum(turbine_aeps)
    
   
    print ("baseline_aep : " , base_aep )

    
    nturbs = len(fi.layout_x)
    from pymoo.termination.ftol import MultiObjectiveSpaceTermination
    from pymoo.operators.sampling.rnd import FloatRandomSampling
    from pymoo.operators.crossover.sbx import SBX
    from pymoo.operators.mutation.pm import PolynomialMutation  

    problem_name = "layoutOptimization"
    n_gen = pr.MAXGEN 
    pop_size = pr.PopSize
    
    sampling = FloatRandomSampling()
    mutation = PolynomialMutation(prob=0.50/nturbs , eta=30)
    crossover = SBX(prob=0.9, eta=10 )
    
    
    from pymoo.termination.default import DefaultSingleObjectiveTermination
    termination = DefaultSingleObjectiveTermination(
        xtol=1e-8,
        cvtol=1e-6,
        ftol=  pr.ftol, #=1e-6,
        period=20,
        n_max_gen=  n_gen ,  #  1000,
        n_max_evals=100000
    )


    print ("-------------------------------------\nOptimizer weibull parameters :\n-------------------------------------\n")
    print ("mdistOpim: " , optimizer_etc.mdistOpim) 
    print ("wdIntervalOptim: " , optimizer_etc.wdIntervalOptim) 
    print ("Nuber of turbs: " , nturbs)
    print ("layoutOptimization\n")
    print ("Solver : " , pr.solver )
    print ("MAXGEN : " , pr.MAXGEN )
    print ("-------------------------------------\n")
   
    layout_opt = LayoutOptimizationPymoo(
      fi,
      boundaries3,
      freq=freq,
      min_dist= optimizer_etc.mdistOpim,
      problem_name=problem_name, 
      n_gen=n_gen, 
      pop_size=pop_size, 
      ftol=pr.ftol,
      crossover=crossover, 
      mutation=mutation , 
      sampling=sampling ,
      termination=termination
    )

    sol = layout_opt.optimize()
    
    
    
    result = sol # Your pymoo.core.result.Result object

    write_log("Best solution(s):\nIt holds the decision variables of the best found solution(s).:",optLog )
    write_log(result.X,optLog )
    
    write_log("\nObjective space values F:\nIt contains the objective space values of the best found solution(s):",optLog )
    write_log(result.F,optLog )
    
    if result.G is not None:
        write_log("Constraint violation values G:\nIt includes the constraint violation of the best found solution(s), if there are any constraints.",optLog )
        write_log("Each value in the array represents the degree to which the corresponding constraint is violated. ",optLog )
        write_log("A value of 0 or less indicates that the constraint is not violated",optLog )
        write_log("while a positive value indicates the degree of violation",optLog )
        write_log(result.G,optLog )
    
    if result.CV is not None:
        write_log("Aggregated constraint violation values CV:",optLog )
        write_log("If the aggregated constraint violation is zero, it means that there are no constraint violations, ",optLog )
        write_log("and the solution satisfies all the constraints. If the aggregated constraint violation is greater than zero, it means that there are constraint violations.",optLog ) 
        write_log("The larger the aggregated constraint violation, the more the solution violates the constraints.",optLog )
        write_log(result.CV,optLog )
    
    
    if hasattr(result, 'feasible'):
        write_log("\nBoolean mask for feasible solutions:",optLog )
        write_log(result.feasible,optLog )
    


    layout_opt.plot_layout_opt_results(str(outputs_loc+solver+"_restuls.png"), boundaries2=boundaries2)
    
    xsol_opt, ysol_opt = layout_opt.get_optimized_locs()
    layout['x']  = xsol_opt
    layout['y']  = ysol_opt
    
    locs = np.vstack((xsol_opt, ysol_opt)).T
    distances = cdist(locs, locs)
    
    np.fill_diagonal(distances, np.inf)
    
    min_distance = np.min(distances)
    
    print("Minimum Distance: ", min_distance , "m")
    
    layout = layout.rename(columns={'x':'easting','y':'northing'})
    
    layout.to_csv (outputs_loc+ "InternalLayout.csv" ,index=False)
    
    export_layout  = layout
    export_layout.loc[export_layout['external']==False,'external']='Internal'
    export_layout = export_layout.rename(columns={'external':'AEP_mode'})
    export_layout.to_excel (outputs_loc+ "OptimizedLayout.xlsx" ,index=False)
    

    fi.reinitialize(layout=(xsol_opt, ysol_opt) , 
                    turbine_type=layout['turb'],
                    wind_directions=wd_array,
                    wind_speeds=ws_array)

    freq = WR.set_index(['wd','ws']).unstack().values
    
    fi.calculate_wake()

    
    turbine_powers = GetAEPParallel ( fi  , max_workers = 6 , n_wind_direction_splits = 3 , n_wind_speed_splits=2 ) #best

    ordered = []
    colnames = ['{}_MW'.format(i) for i in layout['turb_ID']]
    for _, row in WR.iterrows():
        ws = row['ws']
        wd = row['wd']
        wd_idx = np.where(wd_array==wd )
        ws_idx = np.where(ws_array==ws)
        powers = turbine_powers[wd_idx,ws_idx]
        ordered.append(powers)

    keep_turbs = ['{}_MW'.format(layout.loc[c,'turb_ID']) for c in layout.index if layout.loc[c,'external']=='Internal']

    turbine_powers_df = pd.DataFrame(np.squeeze(ordered),columns=colnames)
    turbine_powers_df = turbine_powers_df.fillna(0)
    turbine_powers_df = turbine_powers_df[keep_turbs]

    waked_final_results = pd.concat([WR,turbine_powers_df],axis=1)

    turbine_aeps = []
    for i in keep_turbs:
        turbine_aeps.append(np.dot(waked_final_results['freq'],waked_final_results[i])*8766/1E03)
    
    print ("sum(turbine_aeps) : ",sum(turbine_aeps) )
    opt_aep = sum(turbine_aeps)
    
    percent_gain = 100 * ( opt_aep - base_aep) / base_aep
    print('Optimal layout improves AEP by %.4f%% from %.2f GWh to %.2f GWh' % (percent_gain, base_aep, opt_aep))    
    
    
    write_log('Optimal layout improves AEP by %.3f%% from %.3f GWh to %.3f GWh' % (percent_gain, base_aep, opt_aep) , finaloptLog)
    write_log('Minimum pairwise distances between all WTG pairs. :  %.1f m   ' % ( min_distance ) , finaloptLog )

       
   

    
def run_florisInitial (inputs_loc=workDir+"/Input/" ,
        a_file = 'a_par.txt' ,
        freq_file = 'freq_table.txt',
        k_file = 'k_par.txt',
        layout_file = 'layout_SB.csv' , # 'layout-1000.csv'# 'layout_SB.csv',
        input_config = 'config_Jensen_FLS_Validation_FastAEP.yaml',
        logfile_name = r"mylogfile.txt" ,  ## dir is the same as "outputs_loc"
        outputs_loc=workDir+"/OutputInitial/" ,
        parallel=True,
        PlotFlowfield = False ,
        GeneratingTurbEffContourPlots=False
    ):
    print("========================================")
    print("User given inputs:")
    print(f"inputs_loc : {inputs_loc}" )
    print(f"outputs_loc : {outputs_loc}" )
    print(f"layout_file : {layout_file}" )
    print(f"input_config : {input_config}" )
    print(f"logfile_name : {logfile_name}" )
    print("========================================")

    ref_ht = None # ref_ht = 94.6


    os.chdir(inputs_loc)

    if os.path.exists(outputs_loc+'turbine_library')==True:
        shutil.rmtree(outputs_loc+'turbine_library')

    shutil.copytree(turb_lib_path,outputs_loc+'turbine_library')

    log_loc = '{}/OutputInitial/{}'.format(os.path.dirname(os.getcwd()),logfile_name)
    if os.path.exists(log_loc):
        log = open(log_loc, "w")
        log.seek(0)
        log.truncate()

    start_floris =timerpc()
    ts  = pd.read_csv(inputs_loc+"timeseries.txt" , sep =' ')
    WR = ts_to_freq_df (ts)
    
    write_log('Convert weibull parameters to a windrose format useable for floris runtime: %.2f  sec ' % (timerpc()-start_floris),log_loc)
    print (f" size  of WR {len(WR.index)}")

    turbs = []
    for file in os.listdir():
        if 'turb.csv' in file:
            new_turb = {}
            turb_df = pd.read_csv(file)

            safe_name = re.sub(r'[\/:*?"<>|]', '_', str(turb_df['name'][0])).lower()
            new_turb = {'turbine_type':safe_name,
                     'generator_efficiency':float(1.0),
                     'hub_height':float(turb_df['hh'][0]),
                     'pP':float(1.88),
                     'pT':float(1.88),
                     'rotor_diameter':float(turb_df['dia'][0]),
                     'TSR':float(8.0),
                     'power_thrust_table':
                            {'power':turb_df['cp'].to_list(),
                             'thrust':turb_df['ct'].to_list(),
                             'wind_speed': turb_df['ws'].to_list()}
                     }

            outname = '{}/{}.yaml'.format(turb_lib_path, safe_name)

            with open(outname, 'w') as yaml_file:
                oyaml.dump(new_turb, yaml_file)

    layout = pd.read_csv(layout_file)
    layout = layout.rename(columns={'easting':'x','northing':'y'})
    layout['turb'] = layout['turb'].apply(sanitize_name)
    
    fi = FlorisInterface(input_config)
    wd_array = np.array(WR["wd"].unique(), dtype=float)
    ws_array = np.array(WR["ws"].unique(), dtype=float)



    start_init=timerpc()
    fi.reinitialize(layout=(layout['x'],layout['y']),
                    turbine_type=layout['turb'],
                    wind_directions=wd_array,
                    wind_speeds=ws_array)
    print('fi.reinitialize runtime: %.2f  sec ' % (timerpc()-start_init ) )
    print(f"size of wd_array : {fi.floris.flow_field.n_wind_directions}"  )
    print(f"size of ws_array : {fi.floris.flow_field.n_wind_speeds}"  )




    internal_turb = layout.loc[layout['external']==False,'turb'].unique()
    if ref_ht==None:
        if len(internal_turb)>1:
            hhs = []
            for turb in internal_turb:
                idx = layout.loc[layout['turb']==turb].index[0]
                hhs.append(fi.floris.farm.hub_heights[idx])
            ref_ht = sum(hhs)/len(hhs)
            write_log('Two different types of turbines are specified as internal (belonging to current park / AEP calculation). We have therefore selected the reference wind height to be the average of the turbines hub heights.',log_loc)

        else:
            internal_turb_idx = test = layout.loc[layout['external'] == False].index[0]
            ref_ht = fi.floris.farm.hub_heights[internal_turb_idx]

    fi.floris.flow_field.reference_wind_height = ref_ht

    write_log('Beginning unwaked/gross AEP calculation',log_loc)

    start_unwaked = timerpc()
    fi.calculate_no_wake()
    print('calculate_no_wake runtime: %.2f  sec ' % (timerpc()-start_unwaked ) )



    farm_power_array = fi.get_farm_power() / 1E6 # in MW
    turbine_powers   = fi.get_turbine_powers() / 1E6 # In MW
    turbine_powers   = np.nan_to_num(turbine_powers )

    keep_turbs = ['{}_MW'.format(layout.loc[c,'turb_ID']) for c in layout.index if layout.loc[c,'external']==False]
    keep_turb_idxs = [c for c in layout.index if layout.loc[c,'external']==False]
    startcsv_waked = timerpc()
    ordered = []
    colnames = ['{}_MW'.format(i) for i in layout['turb_ID']]
    for _, row in WR.iterrows():
        ws = row['ws']
        wd = row['wd']
        wd_idx = np.where(wd_array==wd)
        ws_idx = np.where(ws_array==ws)
        powers = turbine_powers[wd_idx,ws_idx]
        ordered.append(powers)

    unwaked_turbine_powers_df = pd.DataFrame(np.squeeze(ordered),columns=colnames)
    unwaked_turbine_powers_df = unwaked_turbine_powers_df.fillna(0)



    unwaked_turbine_powers_df = unwaked_turbine_powers_df.loc[:,keep_turbs]

    unwaked_final_results = pd.concat([WR,unwaked_turbine_powers_df],axis=1)
    unwaked_final_results.loc[:,'farm_power [MW]'] = unwaked_final_results.loc[:,keep_turbs].sum(axis=1)
    unwaked_final_results.to_csv('{}Summarized_AEP_power_results_NOWAKE.csv'.format(outputs_loc))
    print('CSV runtime: %.2f  sec ' % (timerpc()-startcsv_waked) )

    unwaked_turbine_aeps = []
    for i in keep_turbs:
        unwaked_turbine_aeps.append(np.dot(unwaked_final_results['freq'],unwaked_final_results[i])*8766/1E03)

    unwaked_turb_aeps_df = pd.Series(unwaked_turbine_aeps,name='AEP [GWh]')
    unwaked_turb_aeps_df.index = layout['turb_ID'][keep_turb_idxs]
    unwaked_turb_aeps_df.to_csv('{}Summarized_turbine_AEP_NOWAKE.csv'.format(outputs_loc))

    print("==================================================================")

    unwaked_aep =sum (unwaked_turbine_aeps)


    print("baseline AEP: {:.3f} MWh.".format( unwaked_aep  ))
    print("==================================================================")


    write_log('Unwaked/gross AEP calculation complete! Runtime: %.2f  sec ' % (timerpc()-start_unwaked),log_loc)

    def plotFlowFieldForGivenWinDirection ( input_wSec ):
        direct_compute = timerpc()
        fi.reinitialize(wind_directions=[input_wSec], wind_speeds=[8])
        horizontal_plane, _ =  fi.calculate_horizontal_plane(x_resolution=100, y_resolution=100, height=fi.floris.flow_field.reference_wind_height, north_up=True)
        fig, ax = plt.subplots()
        visualize_cut_plane(horizontal_plane, ax=ax,
                                title="Horizontal Plane, height: {} \n WD: {}, WS: {}, TI: {:0.2f} \n Model: {} {}".format(
                                    fi.floris.flow_field.reference_wind_height, input_wSec, fi.floris.flow_field.wind_speeds,
                                    fi.floris.flow_field.turbulence_intensity, fi.floris.wake.velocity_model,
                                    fi.floris.wake.combination_model), color_bar=True, minSpeed=4,
                                maxSpeed=fi.floris.flow_field.wind_speeds[0] + 0.5)
        fig.tight_layout()
        plt.savefig('{}Flowfield_{}deg'.format(outputs_loc, input_wSec))
        print(f"create flowfield plot for {input_wSec} runtime: { (timerpc()-direct_compute )} seconds" )
        return 


    if PlotFlowfield :
      creatFlowField=timerpc()
      write_log('Calculating flowfield visualization for: 12 sectors of 30 deg!', log_loc)
      processlist = []
      for d in range(0,360,30):
        p = Process (target=plotFlowFieldForGivenWinDirection, args=(d ,))
        processlist.append(p )
        p.start()
        
      for t in processlist:
        t.join()
        t.close()


      print('generate Horizontal flowfield plots: %.2f  sec ' % (timerpc()- creatFlowField  ) )
      write_log('Generating flowfield ictures for: 12 sectors Runtime: %.2f  sec ' % (timerpc()-creatFlowField),log_loc)

    start_waked = timerpc()




















    print('Now calculating wakes ')
    fi.reinitialize(layout=(layout['x'],layout['y']),turbine_type=layout['turb'],wind_directions=wd_array,wind_speeds=ws_array)
    print(f" reinitialize wakes runtime: { (timerpc()- start_waked)} seconds" )

    before = memory_footprint()

    start_wake= timerpc()
    start_wake= timerpc()
    if parallel :
      n_cores = 6
      write_log('Beginning Waked AEP calculation(parallel mode number of cores: {})'.format(n_cores),log_loc)
      
      
      start_unwaked = timerpc()
      freq = WR.set_index(['wd','ws']).unstack().values



      print ( "AEP  ( parallel  new impl : )"      )
      before = memory_footprint()
      turbine_powers   = GetAEPParallel ( fi  , max_workers = n_cores , n_wind_direction_splits = 3 , n_wind_speed_splits=2 ) #best
      after = memory_footprint()
      print( f"memory (in MB) being used by Python process = {(after - before)} " )
      
      
      print(f" calculate_wake parallel using {n_cores} cores : { (timerpc()- start_wake)} seconds" )
    else: # serial
      write_log('Beginning Waked AEP calculation(serial mode)', log_loc)
      fi.calculate_wake(logfile=log_loc)
      print(f" calculate_wake serial-core : { (timerpc()- start_wake)} seconds" )
      turbine_powers = fi.get_turbine_powers() / 1E6 # In MW



    after = memory_footprint()
    print( f"memory (in MB) being used by Python process = {(after - before)} " )


    ordered = []
    colnames = ['{}_MW'.format(i) for i in layout['turb_ID']]
    for _, row in WR.iterrows():
        ws = row['ws']
        wd = row['wd']
        wd_idx = np.where(wd_array==wd)
        ws_idx = np.where(ws_array==ws)
        powers = turbine_powers[wd_idx,ws_idx]
        ordered.append(powers)

    write_log('Wake calculation total runtime: %.2f  sec ' % (timerpc()-start_waked),log_loc)

    startcsv_waked = timerpc()
    turbine_powers_df = pd.DataFrame(np.squeeze(ordered),columns=colnames)
    turbine_powers_df = turbine_powers_df.fillna(0)
    turbine_powers_df = turbine_powers_df[keep_turbs]

    waked_final_results = pd.concat([WR,turbine_powers_df],axis=1)
    waked_final_results['farm_power [MW]'] = waked_final_results[keep_turbs].sum(axis=1)
    waked_final_results.to_csv('{}Summarized_AEP_power_results.csv'.format(outputs_loc))
    print('CSV runtime: %.2f  sec ' % (timerpc()-startcsv_waked) )

    turbine_aeps = []
    for i in keep_turbs:
        turbine_aeps.append(np.dot(waked_final_results['freq'],waked_final_results[i])*8766/1E03)

    turb_aeps_df = pd.Series(turbine_aeps,name='AEP [GWh]')
    turb_aeps_df.index = layout['turb_ID'][keep_turb_idxs]
    turb_aeps_df.to_csv('{}/Summarized_turbine_AEP.csv'.format(outputs_loc))

    waked_aep = sum (turbine_aeps)





    start_postprocessing = timerpc()




    print("==================================================================")
    print("waked AEP: {:.3f} MWh.".format( waked_aep  ) )
    print("==================================================================")

    write_log('Generating park-level AEP/efficiency per sector plot.',log_loc)
    sector = []
    gross = []
    net = []


    unwaked_final_results.loc[:,'closest_sect'] = unwaked_final_results['wd'].apply(lambda x: custom_round(x,base=30))
    unwaked_final_results.loc[unwaked_final_results['closest_sect']==360,'closest_sect']=0

    waked_final_results.loc[:,'closest_sect'] = waked_final_results['wd'].apply(lambda x: custom_round(x,base=30))
    waked_final_results.loc[waked_final_results['closest_sect']==360,'closest_sect']=0

    for i in range(0,360,30):
        sector.append(i)
        gross_aep_df = unwaked_final_results.loc[unwaked_final_results.loc[:,'closest_sect']==i,:].copy()
        gross_aep_df.loc[:,'energy']=gross_aep_df.loc[:,'farm_power [MW]']*gross_aep_df.loc[:,'freq']*8766/1E03  #unit GWh
        gross_aep = gross_aep_df['energy'].sum()
        gross.append(gross_aep)
        net_aep_df = waked_final_results.loc[waked_final_results.loc[:,'closest_sect']==i,:].copy()
        net_aep_df.loc[:,'energy']=net_aep_df.loc[:,'farm_power [MW]']*net_aep_df.loc[:,'freq']*8766/1E03  #unit GWh
        net_aep = net_aep_df['energy'].sum()
        net.append(net_aep)


    wake_loss = list(np.array(gross) - np.array(net))
    wake_loss_pct = list(100*np.array(wake_loss)/np.array(gross))


    fig, ax = plt.subplots()
    bar1 = ax.bar(sector, net, width=20, label='Net AEP', color = "#2071B5")
    bar2 = ax.bar(sector, wake_loss, width=20, bottom=net, label='Wake Loss', color="#D1266B")

    ax.set_xlabel('Sector (Degrees)')
    ax.set_ylabel('AEP (GWh)')
    ax.set_title('Internal Wake AEP and Wake Loss by Sector \n Wake Model: {}'.format(fi.floris.wake.velocity_model))

    ax.bar_label(bar2, labels=['{:.2f}%'.format(i) for i in wake_loss_pct],
                 padding=4, color='b', fontsize=8)
    ax.legend(loc='upper left')
    plt.savefig('{}Directional_AEP_bar_plot.png'.format(outputs_loc))

    directional_df = pd.DataFrame({'Sector':sector,'Gross AEP':gross,'Net AEP':net,'Wake Loss':wake_loss})
    directional_df=directional_df.set_index('Sector')
    directional_df.to_csv('{}Directional_AEP_df.csv'.format(outputs_loc))


    def GeneratingTurbineEfficiencyContourPlots(i):
       temp = waked_final_results.loc[waked_final_results.loc[:,'closest_sect']==i,:].copy()
       temp_unwaked = unwaked_final_results.loc[unwaked_final_results.loc[:,'closest_sect']==i,:].copy()
       for j in waked_final_results.columns:
               if ('MW' not in j) or ('farm' in j):
                   continue
               temp.loc[:,'AEP_{}'.format(j.split('_MW')[0])]=temp[j]*temp['freq']*8766/1E03 #AEP in GWh
               temp_unwaked.loc[:,'AEP_nowake_{}'.format(j.split('_MW')[0])]=temp_unwaked[j]*temp_unwaked['freq']*8766/1E03 #AEP in GWh
  
       temp=temp.sum(axis=0)
       temp_unwaked=temp_unwaked.sum(axis=0)
  
       turb_cols = [c for c in temp.index if 'AEP_' in c]
       for t in turb_cols:
               turb_no = t.split('_')[1]
               temp['RATIO_{}'.format(t)]=min(temp[t]/temp_unwaked['AEP_nowake_{}'.format(turb_no)],1)
       keep_rows = [ix for ix in temp.index if 'RATIO' in ix]
       temp = temp[keep_rows]
  
       plt.clf()
       fig, ax = plt.subplots()
       bounds = np.linspace(0.7,1.001,10)
       x = layout.loc[keep_turb_idxs,'x']
       y = layout.loc[keep_turb_idxs,'y']
  
       tcf = ax.tricontourf(x, y, temp.values,vmin=0.7,vmax=1.001,levels=bounds)
       fig.colorbar(tcf,label='Power Ratio to Unwaked',extend='both',format='%.2f')
       ax.scatter(x, y,color='y')
       plt.title('Wind Direction Sector: {}\xb0 \n Wake Model: {}'.format(i,fi.floris.wake.velocity_model))
       fig.tight_layout()
       plt.savefig('{}Directional_AEP_{}deg.png'.format(outputs_loc,i))
       return 
     
    
    if GeneratingTurbEffContourPlots :
        write_log('Generating turbine efficiency contour plots per sector.',log_loc)
        processlist=[]
        for d in range(0,360,30):
          p = Process (target=GeneratingTurbineEfficiencyContourPlots, args=(d ,))
          processlist.append(p )
          p.start()
          
        for t in processlist:
            t.join()
            t.close()
     

    write_log('Complete! Postprocessing time: %.2f  sec ' % ( timerpc()-start_postprocessing),log_loc)
    print('Postprocessing time: %.2f  sec ' % ( timerpc()-start_postprocessing) )

    write_log('Simulation complete! time: %.2f  sec ' % (  timerpc() - start_floris ),log_loc)
    print ('Simulation complete! Runtime:  %.2f  sec ' % (  timerpc() - start_floris ))





def run_floris(
        inputs_loc=workDir+"/Input/" ,
        a_file = 'a_par.txt' ,
        freq_file = 'freq_table.txt',
        k_file = 'k_par.txt',
        layout_file = 'layout_SB.csv' , # 'layout-1000.csv'# 'layout_SB.csv',
        OptLayout_file=workDir+"/Output/InternalLayout.csv" ,
        input_config = 'config_Jensen_FLS_Validation_FastAEP.yaml',
        logfile_name = r"mylogfile.txt" ,  ## dir is the same as "outputs_loc"
        outputs_loc=workDir+"/Output/" , 
        shp_loc=workDir+"/shp/" ,
        parallel=True,
        PlotFlowfield = False ,
        GeneratingTurbEffContourPlots=False
    ):
    print("========================================")
    print("User given inputs:")
    print(f"inputs_loc : {inputs_loc}" )
    print(f"outputs_loc : {outputs_loc}" )
    print(f"layout_file : {layout_file}" )
    print(f"input_config : {input_config}" )
    print(f"logfile_name : {logfile_name}" )
    print("========================================")
    
    ref_ht = None # ref_ht = 94.6
    
    
    os.chdir(inputs_loc)

    if os.path.exists(outputs_loc+'turbine_library')==True:
        shutil.rmtree(outputs_loc+'turbine_library')

    shutil.copytree(turb_lib_path,outputs_loc+'turbine_library')
    
    log_loc = '{}/Output/{}'.format(os.path.dirname(os.getcwd()),logfile_name)
    if os.path.exists(log_loc):
        log = open(log_loc, "w")
        log.seek(0)
        log.truncate()
    
    start_floris =timerpc()
    
    
    
    ts = pd.read_csv( inputs_loc+"timeseries.txt"  ,delim_whitespace=True, names=['date', 'time', 'wd', 'ws'], skiprows=1)
    ts['timestamp'] = pd.to_datetime(ts['date'] + ' ' + ts['time'])

    ts = ts.drop(['date', 'time'], axis=1)
    
    ts = ts[['timestamp', 'wd', 'ws']]
    
    WR = ts_to_freq_df (ts)
    all_wd = list(WR['wd'].unique())
    all_wd.sort()
    WR.loc[WR['wd']==min(all_wd),'wd'] = all_wd[1] - WR['wd'].diff().mode()[0]
    
    wd_array = np.array(WR["wd"].unique(), dtype=float)
    ws_array = np.array(WR["ws"].unique(), dtype=float)
    
    write_log('Convert weibull parameters to a windrose format useable for floris runtime: %.2f  sec ' % (timerpc()-start_floris),log_loc)
    print (f" size  of WR {len(WR.index)}")
    
    turbs = []
    for file in os.listdir():
        if 'turb.csv' in file:
            new_turb = {}
            turb_df = pd.read_csv(file)
            safe_name = re.sub(r'[\/:*?"<>|]', '_', str(turb_df['name'][0])).lower()
            new_turb = {'turbine_type':safe_name,
                     'generator_efficiency':float(1.0),
                     'hub_height':float(turb_df['hh'][0]),
                     'pP':float(1.88),
                     'pT':float(1.88),
                     'rotor_diameter':float(turb_df['dia'][0]),
                     'TSR':float(8.0),
                     'power_thrust_table':
                            {'power':turb_df['cp'].to_list(),
                             'thrust':turb_df['ct'].to_list(),
                             'wind_speed': turb_df['ws'].to_list()}
                     }
    
            outname = '{}/{}.yaml'.format(turb_lib_path, safe_name)
    
            with open(outname, 'w') as yaml_file:
                oyaml.dump(new_turb, yaml_file)
    
    from glob import glob
    csv_files = glob(  workDir   + '/Input/External.layout.csv')  + glob(  workDir   + '/Output/InternalLayout.csv')
     
    print ("csv_files : ", csv_files  )
    df_list = (pd.read_csv(file) for file in csv_files)
    
    layout   = pd.concat(df_list, ignore_index=True)
    
    layout = layout.rename(columns={'easting':'x','northing':'y'})
    
    layout['turb'] = layout['turb'].apply(sanitize_name)
    
    fi = FlorisInterface(input_config)


    
    start_init=timerpc()
    fi.reinitialize(layout=(layout['x'],layout['y']), 
                    turbine_type=layout['turb'],
                    wind_directions=wd_array,
                    wind_speeds=ws_array)
    print('fi.reinitialize runtime: %.2f  sec ' % (timerpc()-start_init ) )
    print(f"size of wd_array : {fi.floris.flow_field.n_wind_directions}"  )
    print(f"size of ws_array : {fi.floris.flow_field.n_wind_speeds}"  )
    
    

    
    internal_turb = layout.loc[layout['external']==False,'turb'].unique()
    if ref_ht==None:
        if len(internal_turb)>1:
            hhs = []
            for turb in internal_turb:
                idx = layout.loc[layout['turb']==turb].index[0]
                hhs.append(fi.floris.farm.hub_heights[idx])
            ref_ht = sum(hhs)/len(hhs)
            write_log('Two different types of turbines are specified as internal (belonging to current park / AEP calculation). We have therefore selected the reference wind height to be the average of the turbines hub heights.',log_loc)
    
        else:
            internal_turb_idx = test = layout.loc[layout['external'] == False].index[0]
            ref_ht = fi.floris.farm.hub_heights[internal_turb_idx]
    
    fi.floris.flow_field.reference_wind_height = ref_ht
    
    write_log('Beginning unwaked/gross AEP calculation',log_loc)

    
    
    start_unwaked=timerpc()
    fi.calculate_no_wake()
    print('calculate_no_wake runtime: %.2f  sec ' % (timerpc()-start_unwaked ) )

    
    
    FL_gross   = fi.get_turbine_powers() / 1E6 # In MW
    FL_gross   = np.nan_to_num(FL_gross )
     

    ordered = []
    colnames = ['{}_MW'.format(i) for i in layout['turb_ID']]
    for _, row in WR.iterrows():
        ws = row['ws']
        wd = row['wd']
        wd_idx = np.where(wd_array==wd)
        ws_idx = np.where(ws_array==ws)
        powers = FL_gross[wd_idx,ws_idx]
        ordered.append(powers)

    unwaked_turbine_powers_df = pd.DataFrame(np.squeeze(ordered),columns=colnames)
    unwaked_turbine_powers_df = unwaked_turbine_powers_df.fillna(0)

    keep_turbs = ['{}_MW'.format(layout.loc[c,'turb_ID']) for c in layout.index if layout.loc[c,'external']==False]
    keep_turb_idxs = [c for c in layout.index if layout.loc[c,'external']==False]
    unwaked_turbine_powers_df = unwaked_turbine_powers_df.loc[:,keep_turbs]

    unwaked_final_results = pd.concat([WR,unwaked_turbine_powers_df],axis=1)
    unwaked_final_results.loc[:,'farm_power [MW]'] = unwaked_final_results.loc[:,keep_turbs].sum(axis=1)


    unwaked_turbine_aeps = []
    for i in keep_turbs:
        unwaked_turbine_aeps.append(np.dot(unwaked_final_results['freq'],unwaked_final_results[i])*8766/1E03)

    unwaked_turb_aeps_df = pd.Series(unwaked_turbine_aeps,name='AEP [GWh]')
    unwaked_turb_aeps_df.index = layout['turb_ID'][keep_turb_idxs]
    unwaked_turb_aeps_df.to_csv('{}Summarized_turbine_AEP_NOWAKE.csv'.format(outputs_loc))



    print("==================================================================")

    unwaked_aep =sum (unwaked_turbine_aeps)


    print("baseline AEP: {:.3f} GWh.".format( unwaked_aep  ))
    print("==================================================================")

    unwaked_aep =sum (unwaked_turbine_aeps)
    print("unwaked_aep : " , unwaked_aep )
    
    gross_AEP = unwaked_turb_aeps_df.sum()
    
    
    
    ts_sens  = copy.deepcopy(ts) 
    ts_sens['ws'] = ts_sens['ws']*0.99
    WR_sens = ts_to_freq_df(ts_sens )

    
    unwaked_final_results_sens = pd.concat([WR_sens,unwaked_turbine_powers_df],axis=1) 
    unwaked_final_results_sens.loc[:,'farm_power [MW]'] = unwaked_final_results_sens.loc[:,keep_turbs].sum(axis=1) 
    
    unwaked_turbine_aeps_sens = [] 
    for i in keep_turbs:
      unwaked_turbine_aeps_sens.append(np.dot(unwaked_final_results_sens['freq'],unwaked_final_results_sens[i])*365*24) #MWh 
    
    unwaked_turb_aeps_df_sens = pd.Series(unwaked_turbine_aeps_sens,name='AEP [GWh]') 
    unwaked_turb_aeps_df_sens.index = layout['turb_ID'][keep_turb_idxs] 
    
    gross_AEP_sens = unwaked_turb_aeps_df_sens.sum() /1E03  
    print("gross_AEP_sens : " , gross_AEP_sens )
    print("gross_AEP : " , gross_AEP )
    sensitivity_factor = 100 * ((gross_AEP-gross_AEP_sens) / gross_AEP)
    print("sensitivity_factor : " , sensitivity_factor )
    
    f=open("sensitivity_factor.txt", "w")
    f.write("{}\n".format(sensitivity_factor))
    f.close()



    start_waked = timerpc()




















    print('Now calculating wakes ')
    fi.reinitialize(layout=(layout['x'],layout['y']),turbine_type=layout['turb'],wind_directions=wd_array,wind_speeds=ws_array)
    print(f" reinitialize wakes runtime: { (timerpc()- start_waked)} seconds" )

    before = memory_footprint()

    start_wake= timerpc()
    if parallel :
      n_cores = 6
      write_log('Beginning Waked AEP calculation(parallel mode number of cores: {})'.format(n_cores),log_loc)
      
      
      start_unwaked = timerpc()
      freq = WR.set_index(['wd','ws']).unstack().values



      print ( "AEP  ( parallel  new impl : )"      )
      before = memory_footprint()
      FL_net    = GetAEPParallel ( fi  , max_workers = n_cores , n_wind_direction_splits = 3 , n_wind_speed_splits=2 , logfile=log_loc , CalculateExternal=True ) #best
      after = memory_footprint()
      print( f"memory (in MB) being used by Python process = {(after - before)} " )
      
      
      print(f" calculate_wake parallel using {n_cores} cores : { (timerpc()- start_wake)} seconds" )
    else: # serial
      write_log('Beginning Waked AEP calculation(serial mode)', log_loc)
      fi.calculate_wake(logfile=log_loc)
      print(f" calculate_wake serial-core : { (timerpc()- start_wake)} seconds" )
      FL_net  = fi.get_turbine_powers() / 1E6 # In MW


    

    mask = np.where(FL_net > FL_gross)
    FL_net[mask] = FL_gross[mask]


    after = memory_footprint()
    print( f"memory (in MB) being used by Python process = {(after - before)} " )


    
    BnDdata2 = genfromtxt( 
      shp_loc + '/shp_coord_total.csv', 
      delimiter=',' ,
      names=True,
      usecols=("lng","lat" )  ) 
    list_zip2 =  zip (  BnDdata2 ['lng'] , BnDdata2 ['lat'] )
    boundaries2  =list (list_zip2)
    
 
    def plotFlowFieldForGivenWinDirection(input_wSec):
      direct_compute = timerpc()
      fi.reinitialize(wind_directions=[input_wSec], wind_speeds=[8])
      horizontal_plane, _ = fi.calculate_horizontal_plane(x_resolution=200, y_resolution=200, height=fi.floris.flow_field.reference_wind_height, north_up=True)
      fig, ax = plt.subplots(figsize=(10, 10))
      # Plotting boundary lines
      verts = boundaries2
      for i in range(len(verts)):
          if i == len(verts) - 1:
              plt.plot([verts[i][0], verts[0][0]], [verts[i][1], verts[0][1]], "--b")
          else:
              plt.plot([verts[i][0], verts[i + 1][0]], [verts[i][1], verts[i + 1][1]], "--b")
              
      visualize_cut_plane(horizontal_plane, ax=ax,
                          title="Horizontal Plane, height: {} \n WD: {}, WS: {}, TI: {:0.2f} \n Model: {} {}".format(
                              fi.floris.flow_field.reference_wind_height, input_wSec, fi.floris.flow_field.wind_speeds,
                              fi.floris.flow_field.turbulence_intensity, fi.floris.wake.velocity_model,
                              fi.floris.wake.combination_model), color_bar=True, minSpeed=4,
                          maxSpeed=fi.floris.flow_field.wind_speeds[0] + 0.5)
  

  
       
      fig.tight_layout()
  
      # Save the figure with boundaries included
      plt.savefig('{}Flowfield_{}deg'.format(outputs_loc, input_wSec))




    write_log('Unwaked/gross AEP calculation complete! Runtime: %.2f  sec ' % (timerpc()-start_unwaked),log_loc)

    if PlotFlowfield :
      creatFlowField=timerpc()
      write_log('Calculating flowfield visualization for: 12 sectors of 30 deg!', log_loc)
      processes = [Process(target=plotFlowFieldForGivenWinDirection, args=(i,)) for i in range(0,360,30)]
      for process in processes:
        process.start()
      for process in processes:
        process.join()

      print('generate Horizontal flowfield plots: %.2f  sec ' % (timerpc()- creatFlowField  ) )
      write_log('Generating flowfield ictures for: 12 sectors Runtime: %.2f  sec ' % (timerpc()-creatFlowField),log_loc)



    ordered = []
    colnames = ['{}_MW'.format(i) for i in layout['turb_ID']]
    for _, row in WR.iterrows():
        ws = row['ws']
        wd = row['wd']
        wd_idx = np.where(wd_array==wd)
        ws_idx = np.where(ws_array==ws)
        powers = FL_net [wd_idx,ws_idx]
        ordered.append(powers)

    write_log('Wake calculation total runtime: %.2f  sec ' % (timerpc()-start_waked),log_loc)

    csv_postprocessing = timerpc()

    turbine_powers_df = pd.DataFrame(np.squeeze(ordered),columns=colnames)
    turbine_powers_df = turbine_powers_df.fillna(0)
    turbine_powers_df = turbine_powers_df[keep_turbs]

    waked_final_results = pd.concat([WR,turbine_powers_df],axis=1)
    waked_final_results['farm_power [MW]'] = waked_final_results[keep_turbs].sum(axis=1)

    print ("CSV postProce time : " , timerpc ()  -  csv_postprocessing )



    turbine_aeps = []
    for i in keep_turbs:
        turbine_aeps.append(np.dot(waked_final_results['freq'],waked_final_results[i])*8766/1E03)

    turb_aeps_df = pd.Series(turbine_aeps,name='AEP [GWh]')
    turb_aeps_df.index = layout['turb_ID'][keep_turb_idxs]
    turb_aeps_df.to_csv('{}/Summarized_turbine_AEP.csv'.format(outputs_loc))

    waked_aep = sum (turbine_aeps)


    prod_ts =  ts_mapping_optimized (ts ,layout,FL_net,FL_gross,wd_array,ws_array) 
    prod_ts.iloc[:, 1] = prod_ts.iloc[:, 1].round(1)  
    prod_ts.iloc[:, 2] = prod_ts.iloc[:, 2].round(2)  
    if prod_ts.shape[1] > 3:  
      prod_ts.iloc[:, 3:] = prod_ts.iloc[:, 3:].round(3)  
    prod_ts.to_csv('{}/production_timeseries.csv'.format(outputs_loc), index=False)

    start_postprocessing = timerpc()




    print("==================================================================")
    print("waked AEP: {:.3f} GWh.".format( waked_aep  ) )
    print("==================================================================")

    write_log('Generating park-level AEP/efficiency per sector plot.',log_loc)
    sector = []
    gross = []
    net = []


    unwaked_final_results.loc[:,'closest_sect'] = unwaked_final_results['wd'].apply(lambda x: custom_round(x,base=30))
    unwaked_final_results.loc[unwaked_final_results['closest_sect']==360,'closest_sect']=0

    waked_final_results.loc[:,'closest_sect'] = waked_final_results['wd'].apply(lambda x: custom_round(x,base=30))
    waked_final_results.loc[waked_final_results['closest_sect']==360,'closest_sect']=0

    for i in range(0,360,30):
        sector.append(i)
        gross_aep_df = unwaked_final_results.loc[unwaked_final_results.loc[:,'closest_sect']==i,:].copy()
        gross_aep_df.loc[:,'energy']=gross_aep_df.loc[:,'farm_power [MW]']*gross_aep_df.loc[:,'freq']*8766/1E03 
        gross_aep = gross_aep_df['energy'].sum()

        gross.append(gross_aep)

        net_aep_df = waked_final_results.loc[waked_final_results.loc[:,'closest_sect']==i,:].copy()
        net_aep_df.loc[:,'energy']=net_aep_df.loc[:,'farm_power [MW]']*net_aep_df.loc[:,'freq']*8766/1E03 #unit GWh
        net_aep = net_aep_df['energy'].sum()
        net.append(net_aep)


    wake_loss = list(np.array(gross) - np.array(net))
    wake_loss_pct = list(100*np.array(wake_loss)/np.array(gross))


    fig, ax = plt.subplots()
    bar1 = ax.bar(sector, net, width=20, label='Net AEP' , color = "#2071B5")
    bar2 = ax.bar(sector, wake_loss, width=20, bottom=net, label='Wake Loss', color="#D1266B" )

    ax.set_xlabel('Sector (Degrees)')
    ax.set_ylabel('AEP (GWh)')
    ax.set_title('External+Internal wake AEP and Wake Loss by Sector \n Wake Model: {}'.format(fi.floris.wake.velocity_model))

    ax.bar_label(bar2, labels=['{:.2f}%'.format(i) for i in wake_loss_pct],
                 padding=4, color="#D1266B", fontsize=8)
    ax.legend(loc='upper left')
    plt.savefig('{}Directional_AEP_bar_plot.png'.format(outputs_loc))

    directional_df = pd.DataFrame({'Sector':sector,'Gross AEP':gross,'Net AEP':net,'Wake Loss':wake_loss})
    directional_df=directional_df.set_index('Sector')
    directional_df.to_csv('{}Directional_AEP_df.csv'.format(outputs_loc))

    if GeneratingTurbEffContourPlots :
        write_log('Generating turbine efficiency contour plots per sector.',log_loc)

        def GeneratingTurbineEfficiencyContourPlots(i):
          temp = waked_final_results.loc[waked_final_results.loc[:,'closest_sect']==i,:].copy()
          temp_unwaked = unwaked_final_results.loc[unwaked_final_results.loc[:,'closest_sect']==i,:].copy()
          for j in waked_final_results.columns:
                  if ('MW' not in j) or ('farm' in j):
                      continue
                
                  temp.loc[:,'AEP_{}'.format(j.split('_MW')[0])]=temp[j]*temp['freq']*8766/1E03 #AEP in GWh
                  temp_unwaked.loc[:,'AEP_nowake_{}'.format(j.split('_MW')[0])]=temp_unwaked[j]*temp_unwaked['freq']*8766/1E03 #AEP in GWh

          temp=temp.sum(axis=0)
          temp_unwaked=temp_unwaked.sum(axis=0)

          turb_cols = [c for c in temp.index if 'AEP_' in c]
          for t in turb_cols:
                  turb_no = t.split('AEP_')[1]
                  temp['RATIO_{}'.format(t)]=min(temp[t]/temp_unwaked['AEP_nowake_{}'.format(turb_no)],1)
          keep_rows = [ix for ix in temp.index if 'RATIO' in ix]
          temp = temp[keep_rows]

          plt.clf()
          fig, ax = plt.subplots()
          bounds = np.linspace(0.7,1.001,10)
          x = layout.loc[keep_turb_idxs,'x']
          y = layout.loc[keep_turb_idxs,'y']

          tcf = ax.tricontourf(x, y, temp.values,vmin=0.7,vmax=1.001,levels=bounds)
          fig.colorbar(tcf,label='Power Ratio to Unwaked',extend='both',format='%.2f')
          ax.scatter(x, y,color='y')
          plt.title('Wind Direction Sector: {}\xb0 \n Wake Model: {}'.format(i,fi.floris.wake.velocity_model))
          fig.tight_layout()
          plt.savefig('{}Directional_AEP_{}deg.png'.format(outputs_loc,i))

        processlist=[]
        for d in range(0,360,30):
          processlist.append(Process (target=GeneratingTurbineEfficiencyContourPlots, args=(d ,)) )
        for t in processlist:
          t.start()
        for t in processlist:
          t.join()

    write_log('Complete! Postprocessing time: %.2f  sec ' % ( timerpc()-start_postprocessing),log_loc)
    print('Postprocessing time: %.2f  sec ' % ( timerpc()-start_postprocessing) )

    write_log('Simulation complete! time: %.2f  sec ' % (  timerpc() - start_floris ),log_loc)
    print ('Simulation complete! Runtime: %.2f  sec ' % (  timerpc() - start_floris ))




def run_florisInternal(inputs_loc=workDir+"/Input/" ,
        a_file = 'a_par.txt' ,
        freq_file = 'freq_table.txt',
        k_file = 'k_par.txt',
        layout_file = 'layout_SB.csv' , # 'layout-1000.csv'# 'layout_SB.csv',
        input_config = 'config_Jensen_FLS_Validation_FastAEP.yaml',
        logfile_name = r"mylogfile.txt" ,  ## dir is the same as "outputs_loc"
        outputs_loc=workDir+"/OutputInternal/" ,
        parallel=True,
        PlotFlowfield = False ,
        GeneratingTurbEffContourPlots=False
    ):
    print("========================================")
    print("User given inputs:")
    print(f"inputs_loc : {inputs_loc}" )
    print(f"outputs_loc : {outputs_loc}" )
    print(f"layout_file : {layout_file}" )
    print(f"input_config : {input_config}" )
    print(f"logfile_name : {logfile_name}" )
    print("========================================")

    ref_ht = None # ref_ht = 94.6


    os.chdir(inputs_loc)

    if os.path.exists(outputs_loc+'turbine_library')==True:
        shutil.rmtree(outputs_loc+'turbine_library')

    shutil.copytree(turb_lib_path,outputs_loc+'turbine_library')

    log_loc = '{}/OutputInternal/{}'.format(os.path.dirname(os.getcwd()),logfile_name)
    if os.path.exists(log_loc):
        log = open(log_loc, "w")
        log.seek(0)
        log.truncate()

    start_floris =timerpc()
    ts = pd.read_csv( inputs_loc+"timeseries.txt"  ,delim_whitespace=True, names=['date', 'time', 'wd', 'ws'], skiprows=1)
    ts['timestamp'] = pd.to_datetime(ts['date'] + ' ' + ts['time'])

    ts = ts.drop(['date', 'time'], axis=1)
    
    ts = ts[['timestamp', 'wd', 'ws']]
    
    WR = ts_to_freq_df (ts)
    all_wd = list(WR['wd'].unique())
    all_wd.sort()
    WR.loc[WR['wd']==min(all_wd),'wd'] = all_wd[1] - WR['wd'].diff().mode()[0]
    
    wd_array = np.array(WR["wd"].unique(), dtype=float)
    ws_array = np.array(WR["ws"].unique(), dtype=float)
    
    write_log('Convert weibull parameters to a windrose format useable for floris runtime: %.2f  sec ' % (timerpc()-start_floris),log_loc)
    print (f" size  of WR {len(WR.index)}")

    turbs = []
    for file in os.listdir():
        if 'turb.csv' in file:
            new_turb = {}
            turb_df = pd.read_csv(file)

            safe_name = re.sub(r'[\/:*?"<>|]', '_', str(turb_df['name'][0])).lower()
            new_turb = {'turbine_type':safe_name,
                     'generator_efficiency':float(1.0),
                     'hub_height':float(turb_df['hh'][0]),
                     'pP':float(1.88),
                     'pT':float(1.88),
                     'rotor_diameter':float(turb_df['dia'][0]),
                     'TSR':float(8.0),
                     'power_thrust_table':
                            {'power':turb_df['cp'].to_list(),
                             'thrust':turb_df['ct'].to_list(),
                             'wind_speed': turb_df['ws'].to_list()}
                     }

            outname = '{}/{}.yaml'.format(turb_lib_path, safe_name)

            with open(outname, 'w') as yaml_file:
                oyaml.dump(new_turb, yaml_file)

    layout = pd.read_csv(layout_file)
    layout = layout.rename(columns={'easting':'x','northing':'y'})
    layout['turb'] = layout['turb'].apply(sanitize_name)
    
    fi = FlorisInterface(input_config)
    wd_array = np.array(WR["wd"].unique(), dtype=float)
    ws_array = np.array(WR["ws"].unique(), dtype=float)



    start_init=timerpc()
    fi.reinitialize(layout=(layout['x'],layout['y']),
                    turbine_type=layout['turb'],
                    wind_directions=wd_array,
                    wind_speeds=ws_array)
    print('fi.reinitialize runtime: %.2f  sec ' % (timerpc()-start_init ) )
    print(f"size of wd_array : {fi.floris.flow_field.n_wind_directions}"  )
    print(f"size of ws_array : {fi.floris.flow_field.n_wind_speeds}"  )




    internal_turb = layout.loc[layout['external']==False,'turb'].unique()
    if ref_ht==None:
        if len(internal_turb)>1:
            hhs = []
            for turb in internal_turb:
                idx = layout.loc[layout['turb']==turb].index[0]
                hhs.append(fi.floris.farm.hub_heights[idx])
            ref_ht = sum(hhs)/len(hhs)
            write_log('Two different types of turbines are specified as internal (belonging to current park / AEP calculation). We have therefore selected the reference wind height to be the average of the turbines hub heights.',log_loc)

        else:
            internal_turb_idx = test = layout.loc[layout['external'] == False].index[0]
            ref_ht = fi.floris.farm.hub_heights[internal_turb_idx]

    fi.floris.flow_field.reference_wind_height = ref_ht

    write_log('Beginning unwaked/gross AEP calculation',log_loc)

    start_unwaked = timerpc()
    fi.calculate_no_wake()
    print('calculate_no_wake runtime: %.2f  sec ' % (timerpc()-start_unwaked ) )



    FL_gross   = fi.get_turbine_powers() / 1E6 # In MW
    FL_gross   = np.nan_to_num(FL_gross )
    

    keep_turbs = ['{}_MW'.format(layout.loc[c,'turb_ID']) for c in layout.index if layout.loc[c,'external']==False]
    keep_turb_idxs = [c for c in layout.index if layout.loc[c,'external']==False]
    startcsv_waked = timerpc()
    ordered = []
    colnames = ['{}_MW'.format(i) for i in layout['turb_ID']]
    for _, row in WR.iterrows():
        ws = row['ws']
        wd = row['wd']
        wd_idx = np.where(wd_array==wd)
        ws_idx = np.where(ws_array==ws)
        powers = FL_gross[wd_idx,ws_idx]
        ordered.append(powers)

    unwaked_turbine_powers_df = pd.DataFrame(np.squeeze(ordered),columns=colnames)
    unwaked_turbine_powers_df = unwaked_turbine_powers_df.fillna(0)



    unwaked_turbine_powers_df = unwaked_turbine_powers_df.loc[:,keep_turbs]

    unwaked_final_results = pd.concat([WR,unwaked_turbine_powers_df],axis=1)
    unwaked_final_results.loc[:,'farm_power [MW]'] = unwaked_final_results.loc[:,keep_turbs].sum(axis=1)
    unwaked_final_results.to_csv('{}Summarized_AEP_power_results_NOWAKE.csv'.format(outputs_loc))
    print('CSV runtime: %.2f  sec ' % (timerpc()-startcsv_waked) )

    unwaked_turbine_aeps = []
    for i in keep_turbs:
        unwaked_turbine_aeps.append(np.dot(unwaked_final_results['freq'],unwaked_final_results[i])*8766/1E03)

    unwaked_turb_aeps_df = pd.Series(unwaked_turbine_aeps,name='AEP [GWh]')
    unwaked_turb_aeps_df.index = layout['turb_ID'][keep_turb_idxs]
    unwaked_turb_aeps_df.to_csv('{}Summarized_turbine_AEP_NOWAKE.csv'.format(outputs_loc))

    print("==================================================================")

    unwaked_aep =sum (unwaked_turbine_aeps)


    print("baseline AEP: {:.3f} GWh.".format( unwaked_aep  ))
    print("==================================================================")


    write_log('Unwaked/gross AEP calculation complete! Runtime: %.2f  sec ' % (timerpc()-start_unwaked),log_loc)

    def plotFlowFieldForGivenWinDirection ( input_wSec ):
        direct_compute = timerpc()
        fi.reinitialize(wind_directions=[input_wSec], wind_speeds=[8])
        horizontal_plane, _ =  fi.calculate_horizontal_plane(x_resolution=100, y_resolution=100, height=fi.floris.flow_field.reference_wind_height, north_up=True)
        fig, ax = plt.subplots()
        visualize_cut_plane(horizontal_plane, ax=ax,
                                title="Horizontal Plane, height: {} \n WD: {}, WS: {}, TI: {:0.2f} \n Model: {} {}".format(
                                    fi.floris.flow_field.reference_wind_height, input_wSec, fi.floris.flow_field.wind_speeds,
                                    fi.floris.flow_field.turbulence_intensity, fi.floris.wake.velocity_model,
                                    fi.floris.wake.combination_model), color_bar=True, minSpeed=4,
                                maxSpeed=fi.floris.flow_field.wind_speeds[0] + 0.5)
        fig.tight_layout()
        plt.savefig('{}Flowfield_{}deg'.format(outputs_loc, input_wSec))
        print(f"create flowfield plot for {input_wSec} runtime: { (timerpc()-direct_compute )} seconds" )

    if PlotFlowfield :
      creatFlowField=timerpc()
      write_log('Calculating flowfield visualization for: 12 sectors of 30 deg!', log_loc)
      processlist = []
      for d in range(0,360,30):
        p = Process (target=plotFlowFieldForGivenWinDirection, args=(d ,))
        processlist.append(p )
        p.start()
        
      for t in processlist:
          t.join()
          t.close()
          
           

      print('generate Horizontal flowfield plots: %.2f  sec ' % (timerpc()- creatFlowField  ) )
      write_log('Generating flowfield ictures for: 12 sectors Runtime: %.2f  sec ' % (timerpc()-creatFlowField),log_loc)

    start_waked = timerpc()




















    print('Now calculating wakes ')
    fi.reinitialize(layout=(layout['x'],layout['y']),turbine_type=layout['turb'],wind_directions=wd_array,wind_speeds=ws_array)
    print(f" reinitialize wakes runtime: { (timerpc()- start_waked)} seconds" )

    before = memory_footprint()

    start_wake= timerpc()
    start_wake= timerpc()
    if parallel :
      n_cores = 6
      write_log('Beginning Waked AEP calculation(parallel mode number of cores: {})'.format(n_cores),log_loc)
      
      
      start_unwaked = timerpc()
      freq = WR.set_index(['wd','ws']).unstack().values



      print ( "AEP  ( parallel  new impl : )"      )
      before = memory_footprint()
      FL_net   = GetAEPParallel ( fi  , max_workers = n_cores , n_wind_direction_splits = 3 , n_wind_speed_splits=2 ) #best
      after = memory_footprint()
      print( f"memory (in MB) being used by Python process = {(after - before)} " )
      
      
      print(f" calculate_wake parallel using {n_cores} cores : { (timerpc()- start_wake)} seconds" )
    else: # serial
      write_log('Beginning Waked AEP calculation(serial mode)', log_loc)
      fi.calculate_wake(logfile=log_loc)
      print(f" calculate_wake serial-core : { (timerpc()- start_wake)} seconds" )
      FL_net = fi.get_turbine_powers() / 1E6 # In MW


    mask = np.where(FL_net > FL_gross)
    FL_net[mask] = FL_gross[mask]
    
    after = memory_footprint()
    print( f"memory (in MB) being used by Python process = {(after - before)} " )


    ordered = []
    colnames = ['{}_MW'.format(i) for i in layout['turb_ID']]
    for _, row in WR.iterrows():
        ws = row['ws']
        wd = row['wd']
        wd_idx = np.where(wd_array==wd)
        ws_idx = np.where(ws_array==ws)
        powers = FL_net[wd_idx,ws_idx]
        ordered.append(powers)

    write_log('Wake calculation total runtime: %.2f  sec ' % (timerpc()-start_waked),log_loc)

    startcsv_waked = timerpc()
    turbine_powers_df = pd.DataFrame(np.squeeze(ordered),columns=colnames)
    turbine_powers_df = turbine_powers_df.fillna(0)
    turbine_powers_df = turbine_powers_df[keep_turbs]

    waked_final_results = pd.concat([WR,turbine_powers_df],axis=1)
    waked_final_results['farm_power [MW]'] = waked_final_results[keep_turbs].sum(axis=1)
    waked_final_results.to_csv('{}Summarized_AEP_power_results.csv'.format(outputs_loc))
    print('CSV runtime: %.2f  sec ' % (timerpc()-startcsv_waked) )

    turbine_aeps = []
    for i in keep_turbs:
        turbine_aeps.append(np.dot(waked_final_results['freq'],waked_final_results[i])*8766/1E03)

    turb_aeps_df = pd.Series(turbine_aeps,name='AEP [GWh]')
    turb_aeps_df.index = layout['turb_ID'][keep_turb_idxs]
    turb_aeps_df.to_csv('{}/Summarized_turbine_AEP.csv'.format(outputs_loc))

    waked_aep = sum (turbine_aeps)





    start_postprocessing = timerpc()




    print("==================================================================")
    print("waked AEP: {:.3f} GWh.".format( waked_aep  ) )
    print("==================================================================")

    write_log('Generating park-level AEP/efficiency per sector plot.',log_loc)
    sector = []
    gross = []
    net = []


    unwaked_final_results.loc[:,'closest_sect'] = unwaked_final_results['wd'].apply(lambda x: custom_round(x,base=30))
    unwaked_final_results.loc[unwaked_final_results['closest_sect']==360,'closest_sect']=0

    waked_final_results.loc[:,'closest_sect'] = waked_final_results['wd'].apply(lambda x: custom_round(x,base=30))
    waked_final_results.loc[waked_final_results['closest_sect']==360,'closest_sect']=0

    for i in range(0,360,30):
        sector.append(i)
        gross_aep_df = unwaked_final_results.loc[unwaked_final_results.loc[:,'closest_sect']==i,:].copy()
        gross_aep_df.loc[:,'energy']=gross_aep_df.loc[:,'farm_power [MW]']*gross_aep_df.loc[:,'freq']*8766/1E03  #unit GWh
        gross_aep = gross_aep_df['energy'].sum()
        gross.append(gross_aep)
        net_aep_df = waked_final_results.loc[waked_final_results.loc[:,'closest_sect']==i,:].copy()
        net_aep_df.loc[:,'energy']=net_aep_df.loc[:,'farm_power [MW]']*net_aep_df.loc[:,'freq']*8766/1E03  #unit GWh
        net_aep = net_aep_df['energy'].sum()
        net.append(net_aep)


    wake_loss = list(np.array(gross) - np.array(net))
    wake_loss_pct = list(100*np.array(wake_loss)/np.array(gross))


    fig, ax = plt.subplots()
    bar1 = ax.bar(sector, net, width=20, label='Net AEP', color = "#2071B5")
    bar2 = ax.bar(sector, wake_loss, width=20, bottom=net, label='Wake Loss', color="#D1266B")

    ax.set_xlabel('Sector (Degrees)')
    ax.set_ylabel('AEP (GWh)')
    ax.set_title('Internal Wake AEP and Wake Loss by Sector \n Wake Model: {}'.format(fi.floris.wake.velocity_model))

    ax.bar_label(bar2, labels=['{:.2f}%'.format(i) for i in wake_loss_pct],
                 padding=4, color='b', fontsize=8)
    ax.legend(loc='upper left')
    plt.savefig('{}Directional_AEP_bar_plot.png'.format(outputs_loc))

    directional_df = pd.DataFrame({'Sector':sector,'Gross AEP':gross,'Net AEP':net,'Wake Loss':wake_loss})
    directional_df=directional_df.set_index('Sector')
    directional_df.to_csv('{}Directional_AEP_df.csv'.format(outputs_loc))


    def GeneratingTurbineEfficiencyContourPlots(i):
     temp = waked_final_results.loc[waked_final_results.loc[:,'closest_sect']==i,:].copy()
     temp_unwaked = unwaked_final_results.loc[unwaked_final_results.loc[:,'closest_sect']==i,:].copy()
     for j in waked_final_results.columns:
             if ('MW' not in j) or ('farm' in j):
                 continue

             temp.loc[:,'AEP_{}'.format(j.split('_MW')[0])]=temp[j]*temp['freq']*8766/1E03 #AEP in GWh
             temp_unwaked.loc[:,'AEP_nowake_{}'.format(j.split('_MW')[0])]=temp_unwaked[j]*temp_unwaked['freq']*8766/1E03 #AEP in GWh

     temp=temp.sum(axis=0)
     temp_unwaked=temp_unwaked.sum(axis=0)

     turb_cols = [c for c in temp.index if 'AEP_' in c]
     for t in turb_cols:
             turb_no = t.split('_')[1]
             temp['RATIO_{}'.format(t)]=min(temp[t]/temp_unwaked['AEP_nowake_{}'.format(turb_no)],1)
     keep_rows = [ix for ix in temp.index if 'RATIO' in ix]
     temp = temp[keep_rows]

     plt.clf()
     fig, ax = plt.subplots()
     bounds = np.linspace(0.7,1.001,10)
     x = layout.loc[keep_turb_idxs,'x']
     y = layout.loc[keep_turb_idxs,'y']

     tcf = ax.tricontourf(x, y, temp.values,vmin=0.7,vmax=1.001,levels=bounds)
     fig.colorbar(tcf,label='Power Ratio to Unwaked',extend='both',format='%.2f')
     ax.scatter(x, y,color='y')
     plt.title('Wind Direction Sector: {}\xb0 \n Wake Model: {}'.format(i,fi.floris.wake.velocity_model))
     fig.tight_layout()
     plt.savefig('{}Directional_AEP_{}deg.png'.format(outputs_loc,i))

    if GeneratingTurbEffContourPlots :
        write_log('Generating turbine efficiency contour plots per sector.',log_loc)
        processlist=[]
        for d in range(0,360,30):
          p = Process (target=GeneratingTurbineEfficiencyContourPlots, args=(d ,))
          processlist.append(p )
          p.start()
        
        for t in processlist:
            t.join()
            t.close()
    
    write_log('Complete! Postprocessing time: %.2f  sec ' % ( timerpc()-start_postprocessing),log_loc)
    print('Postprocessing time: %.2f  sec ' % ( timerpc()-start_postprocessing) )

    write_log('Simulation complete! time: %.2f  sec ' % (  timerpc() - start_floris ),log_loc)
    print ('Simulation complete! Runtime: %.2f  sec ' % (  timerpc() - start_floris ))




    
if __name__ == "__main__":
    print ("1) AEP of the initial layout ")
    f = open(OverAllProgress, "w")


    start_postprocessing = timerpc() 
    run_florisInitial(parallel=False,
                       layout_file="Initial.layout.csv",
                       PlotFlowfield=False,
                       GeneratingTurbEffContourPlots=False)

    
    start_optim = timerpc()
    write_log('1) AEP of the baseline layout complete(%.2f sec)!' % ( start_optim -start_postprocessing),OverAllProgress)
    
    
    
    
    start_optim = timerpc()
    print ("2) Layout Optimization")
    run_layoutOpt( optLog="optim_log.txt",
               parallel=True,
               layout_file="layout.csv",
               PlotFlowfield=False,
               GeneratingTurbEffContourPlots=False)
    
    start_inter = timerpc()
    write_log('2) Layout Optimization complete (%.2f sec )!'  % ( start_inter - start_optim ),OverAllProgress)

    print ("3) simulation1 : only internal")
    run_florisInternal(
           parallel=True,
           inputs_loc=workDir+"/Input/" ,
           layout_file=workDir+"/Output/InternalLayout.csv" ,
           PlotFlowfield=False,
           GeneratingTurbEffContourPlots=False
           )
    start_internal = timerpc()
    write_log('3) Internal-Simulation Opt-layout complete(%.2f sec)!' % (  start_internal- start_inter ),OverAllProgress)
    print ("4) simulation2 : internal + External")
    run_floris(
             parallel=True,
             layout_file= workDir+"/Input/External.layout.csv", # from the  Input folder
             OptLayout_file=workDir+"/Output/InternalLayout.csv" ,
             PlotFlowfield=True,
             GeneratingTurbEffContourPlots=False)
    
    write_log('4) Internal+External Sim using Opt-lyt complete(%.2f sec)!' % (timerpc()-start_internal) ,OverAllProgress)
    print('Total time: %.2f sec' % (  timerpc() - start_postprocessing  ),OverAllProgress)
    print ("4) done!")
    print('Killing all tasks')

    import sys
    f.close()
    sys.exit(0)
