#!/usr/bin/env python3
"""
Performance Monitor for DEAP Optimization Enhancement
====================================================

Comprehensive performance monitoring system for tracking and analyzing
optimization performance, bottlenecks, and resource usage. Provides
real-time monitoring, detailed profiling, and automated performance
analysis for wind farm layout optimization.

Features:
- Real-time performance tracking
- Memory and CPU monitoring  
- Bottleneck identification
- Progress estimation with ETA
- Automated performance profiling
- Resource usage analytics
- Performance comparison tools
"""

import os
import gc
import time
import psutil
import threading
import functools
from pathlib import Path
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
from collections import deque, defaultdict
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import json

# Optional imports for enhanced monitoring
try:
    import matplotlib.pyplot as plt
    import matplotlib.ticker as ticker
    HAS_MATPLOTLIB = True
except ImportError:
    HAS_MATPLOTLIB = False

try:
    import cProfile
    import pstats
    import io
    HAS_PROFILER = True
except ImportError:
    HAS_PROFILER = False


@dataclass
class PerformanceMetric:
    """Single performance metric measurement"""
    timestamp: float
    value: float
    metric_type: str
    context: Optional[Dict] = None
    
    def __post_init__(self):
        if self.context is None:
            self.context = {}


@dataclass 
class ResourceSnapshot:
    """System resource usage snapshot"""
    timestamp: float = field(default_factory=time.time)
    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_used_mb: float = 0.0
    memory_available_mb: float = 0.0
    disk_usage_percent: float = 0.0
    active_threads: int = 0
    open_files: int = 0
    
    @classmethod
    def capture(cls) -> 'ResourceSnapshot':
        """Capture current system resource usage"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            system_memory = psutil.virtual_memory()
            
            return cls(
                cpu_percent=process.cpu_percent(),
                memory_percent=process.memory_percent(),
                memory_used_mb=memory_info.rss / 1024 / 1024,
                memory_available_mb=system_memory.available / 1024 / 1024,
                disk_usage_percent=psutil.disk_usage('.').percent,
                active_threads=process.num_threads(),
                open_files=len(process.open_files())
            )
        except Exception:
            return cls()


@dataclass
class OptimizationPhaseStats:
    """Statistics for a single optimization phase"""
    phase_name: str
    start_time: float
    end_time: Optional[float] = None
    function_evaluations: int = 0
    best_fitness: Optional[float] = None
    avg_fitness: Optional[float] = None
    convergence_rate: Optional[float] = None
    cache_hit_rate: Optional[float] = None
    memory_peak_mb: Optional[float] = None
    cpu_time_seconds: Optional[float] = None
    
    @property
    def duration_seconds(self) -> float:
        """Get phase duration in seconds"""
        if self.end_time is None:
            return time.time() - self.start_time
        return self.end_time - self.start_time
    
    @property
    def evaluations_per_second(self) -> float:
        """Get function evaluations per second"""
        duration = self.duration_seconds
        if duration == 0:
            return 0.0
        return self.function_evaluations / duration


class PerformanceProfiler:
    """Automated performance profiler using cProfile"""
    
    def __init__(self, enable_profiling: bool = True):
        self.enable_profiling = enable_profiling and HAS_PROFILER
        self.profiler = None
        self.profiling_active = False
        
    def start_profiling(self):
        """Start performance profiling"""
        if not self.enable_profiling:
            return
            
        try:
            self.profiler = cProfile.Profile()
            self.profiler.enable()
            self.profiling_active = True
        except ValueError as e:
            if "Another profiling tool is already active" in str(e):
                print("Warning: Profiling disabled - another profiler is active")
                self.enable_profiling = False
            else:
                raise
    
    def stop_profiling(self) -> Optional[str]:
        """Stop profiling and return stats summary"""
        if not self.profiling_active or self.profiler is None:
            return None
            
        self.profiler.disable()
        self.profiling_active = False
        
        # Generate stats summary with error handling
        try:
            s = io.StringIO()
            ps = pstats.Stats(self.profiler, stream=s).sort_stats('cumulative')
            ps.print_stats(20)  # Top 20 functions
            return s.getvalue()
        except (TypeError, ValueError) as e:
            print(f"Warning: Could not generate profiling stats: {e}")
            return f"Profiling completed but stats generation failed: {e}"
    
    def profile_function(self, func: Callable) -> Callable:
        """Decorator to profile a specific function"""
        if not self.enable_profiling:
            return func
            
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            local_profiler = cProfile.Profile()
            local_profiler.enable()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                local_profiler.disable()
                # Could save profile results here
        return wrapper


class BottleneckDetector:
    """Automatic bottleneck detection and analysis"""
    
    def __init__(self, window_size: int = 100, threshold_factor: float = 2.0):
        self.window_size = window_size
        self.threshold_factor = threshold_factor
        self.timing_history = defaultdict(lambda: deque(maxlen=window_size))
        self.bottlenecks = []
        
    def record_timing(self, operation: str, duration: float):
        """Record timing for an operation"""
        self.timing_history[operation].append(duration)
        self._check_for_bottleneck(operation, duration)
    
    def _check_for_bottleneck(self, operation: str, duration: float):
        """Check if current duration indicates a bottleneck"""
        history = self.timing_history[operation]
        if len(history) < 10:  # Need some history
            return
            
        avg_duration = np.mean(history)
        if duration > avg_duration * self.threshold_factor:
            bottleneck = {
                'timestamp': time.time(),
                'operation': operation,
                'duration': duration,
                'avg_duration': avg_duration,
                'slowdown_factor': duration / avg_duration
            }
            self.bottlenecks.append(bottleneck)
    
    def get_recent_bottlenecks(self, minutes: int = 5) -> List[Dict]:
        """Get bottlenecks from recent time period"""
        cutoff_time = time.time() - (minutes * 60)
        return [b for b in self.bottlenecks if b['timestamp'] > cutoff_time]
    
    def get_worst_operations(self, top_n: int = 5) -> List[Tuple[str, float]]:
        """Get operations with worst average performance"""
        avg_times = []
        for operation, history in self.timing_history.items():
            if len(history) > 5:
                avg_time = np.mean(history)
                avg_times.append((operation, avg_time))
        
        return sorted(avg_times, key=lambda x: x[1], reverse=True)[:top_n]


class ProgressEstimator:
    """Progress estimation with ETA calculation"""
    
    def __init__(self, total_generations: int, window_size: int = 20):
        self.total_generations = total_generations
        self.window_size = window_size
        self.generation_times = deque(maxlen=window_size)
        self.fitness_history = deque(maxlen=window_size)
        self.start_time = time.time()
        self.current_generation = 0
        
    def update(self, generation: int, best_fitness: float, generation_time: float):
        """Update progress with latest generation info"""
        self.current_generation = generation
        self.generation_times.append(generation_time)
        self.fitness_history.append(best_fitness)
    
    def get_eta(self) -> Tuple[float, str]:
        """Get estimated time to completion"""
        if len(self.generation_times) == 0:
            return 0.0, "Unknown"
            
        remaining_generations = self.total_generations - self.current_generation
        avg_generation_time = np.mean(self.generation_times)
        eta_seconds = remaining_generations * avg_generation_time
        
        eta_str = str(timedelta(seconds=int(eta_seconds)))
        return eta_seconds, eta_str
    
    def get_progress_percent(self) -> float:
        """Get progress percentage"""
        if self.total_generations == 0:
            return 0.0
        return (self.current_generation / self.total_generations) * 100
    
    def get_convergence_rate(self) -> float:
        """Estimate convergence rate from fitness history"""
        if len(self.fitness_history) < 5:
            return 0.0
            
        # Calculate improvement rate
        recent_fitness = list(self.fitness_history)
        improvements = []
        for i in range(1, len(recent_fitness)):
            if recent_fitness[i-1] != 0:
                improvement = (recent_fitness[i] - recent_fitness[i-1]) / abs(recent_fitness[i-1])
                improvements.append(improvement)
        
        return np.mean(improvements) if improvements else 0.0


class PerformanceMonitor:
    """
    Main performance monitoring system
    """
    
    def __init__(self, enable_profiling: bool = True, enable_plotting: bool = True,
                 monitoring_interval: float = 1.0):
        """
        Initialize performance monitor
        
        Args:
            enable_profiling: Enable detailed profiling
            enable_plotting: Enable performance plots
            monitoring_interval: Resource monitoring interval in seconds
        """
        self.enable_profiling = enable_profiling
        self.enable_plotting = enable_plotting and HAS_MATPLOTLIB
        self.monitoring_interval = monitoring_interval
        
        # Core components
        self.profiler = PerformanceProfiler(enable_profiling)
        self.bottleneck_detector = BottleneckDetector()
        self.progress_estimator = None
        
        # Data storage
        self.metrics = []
        self.resource_snapshots = []
        self.phase_stats = {}
        self.current_phase = None
        
        # Monitoring thread
        self.monitoring_active = False
        self.monitoring_thread = None
        
        # Timing context manager
        self.timing_stack = []
        
        print("📊 Performance Monitor initialized")
        print(f"   Profiling: {'✅' if self.enable_profiling else '❌'}")
        print(f"   Plotting: {'✅' if self.enable_plotting else '❌'}")
        print(f"   Monitoring interval: {monitoring_interval}s")
    
    def start_monitoring(self, total_generations: int = None):
        """Start performance monitoring"""
        if total_generations:
            self.progress_estimator = ProgressEstimator(total_generations)
        
        # Start resource monitoring thread
        self.monitoring_active = True
        self.monitoring_thread = threading.Thread(target=self._monitor_resources, daemon=True)
        self.monitoring_thread.start()
        
        # Start profiling
        self.profiler.start_profiling()
        
        print("🚀 Performance monitoring started")
    
    def stop_monitoring(self) -> Dict[str, Any]:
        """Stop monitoring and return performance summary"""
        # Stop resource monitoring
        self.monitoring_active = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=2.0)
        
        # Stop profiling
        profile_summary = self.profiler.stop_profiling()
        
        # Generate summary
        summary = self._generate_performance_summary()
        summary['profile_data'] = profile_summary
        
        print("⏹️ Performance monitoring stopped")
        return summary
    
    def _monitor_resources(self):
        """Background resource monitoring"""
        while self.monitoring_active:
            try:
                snapshot = ResourceSnapshot.capture()
                self.resource_snapshots.append(snapshot)
                
                # Keep only recent snapshots (last hour)
                cutoff_time = time.time() - 3600
                self.resource_snapshots = [
                    s for s in self.resource_snapshots 
                    if s.timestamp > cutoff_time
                ]
                
                time.sleep(self.monitoring_interval)
            except Exception as e:
                print(f"Warning: Resource monitoring error: {e}")
                break
    
    def start_phase(self, phase_name: str):
        """Start tracking a new optimization phase"""
        if self.current_phase:
            self.end_phase()
        
        self.current_phase = phase_name
        self.phase_stats[phase_name] = OptimizationPhaseStats(
            phase_name=phase_name,
            start_time=time.time()
        )
        
        print(f"📈 Started phase: {phase_name}")
    
    def end_phase(self):
        """End current optimization phase"""
        if self.current_phase and self.current_phase in self.phase_stats:
            stats = self.phase_stats[self.current_phase]
            stats.end_time = time.time()
            
            # Calculate phase statistics
            if self.resource_snapshots:
                phase_snapshots = [
                    s for s in self.resource_snapshots 
                    if s.timestamp >= stats.start_time
                ]
                if phase_snapshots:
                    stats.memory_peak_mb = max(s.memory_used_mb for s in phase_snapshots)
                    total_cpu_time = sum(s.cpu_percent for s in phase_snapshots)
                    stats.cpu_time_seconds = total_cpu_time * self.monitoring_interval / 100
            
            print(f"✅ Completed phase: {self.current_phase} ({stats.duration_seconds:.1f}s)")
            self.current_phase = None
    
    def record_metric(self, metric_type: str, value: float, context: Dict = None):
        """Record a performance metric"""
        metric = PerformanceMetric(
            timestamp=time.time(),
            value=value,
            metric_type=metric_type,
            context=context or {}
        )
        self.metrics.append(metric)
    
    def time_operation(self, operation_name: str):
        """Context manager for timing operations"""
        return TimingContext(self, operation_name)
    
    def update_generation_progress(self, generation: int, best_fitness: float, 
                                 generation_time: float):
        """Update progress for current generation"""
        if self.progress_estimator:
            self.progress_estimator.update(generation, best_fitness, generation_time)
        
        # Update current phase stats
        if self.current_phase and self.current_phase in self.phase_stats:
            stats = self.phase_stats[self.current_phase]
            stats.function_evaluations += 1
            if stats.best_fitness is None or best_fitness > stats.best_fitness:
                stats.best_fitness = best_fitness
    
    def get_current_progress(self) -> Dict[str, Any]:
        """Get current optimization progress"""
        if not self.progress_estimator:
            return {}
        
        eta_seconds, eta_str = self.progress_estimator.get_eta()
        
        return {
            'generation': self.progress_estimator.current_generation,
            'total_generations': self.progress_estimator.total_generations,
            'progress_percent': self.progress_estimator.get_progress_percent(),
            'eta_seconds': eta_seconds,
            'eta_string': eta_str,
            'convergence_rate': self.progress_estimator.get_convergence_rate()
        }
    
    def _generate_performance_summary(self) -> Dict[str, Any]:
        """Generate comprehensive performance summary"""
        current_time = time.time()
        
        # Resource usage summary
        resource_summary = {}
        if self.resource_snapshots:
            recent_snapshots = [
                s for s in self.resource_snapshots 
                if current_time - s.timestamp < 300  # Last 5 minutes
            ]
            
            if recent_snapshots:
                resource_summary = {
                    'avg_cpu_percent': np.mean([s.cpu_percent for s in recent_snapshots]),
                    'peak_memory_mb': max([s.memory_used_mb for s in recent_snapshots]),
                    'avg_memory_mb': np.mean([s.memory_used_mb for s in recent_snapshots]),
                    'peak_threads': max([s.active_threads for s in recent_snapshots])
                }
        
        # Bottleneck summary
        bottlenecks = self.bottleneck_detector.get_recent_bottlenecks(10)
        worst_operations = self.bottleneck_detector.get_worst_operations(10)
        
        # Phase summary
        phase_summary = {}
        for phase_name, stats in self.phase_stats.items():
            phase_summary[phase_name] = {
                'duration_seconds': stats.duration_seconds,
                'function_evaluations': stats.function_evaluations,
                'evaluations_per_second': stats.evaluations_per_second,
                'best_fitness': stats.best_fitness,
                'memory_peak_mb': stats.memory_peak_mb,
                'cpu_time_seconds': stats.cpu_time_seconds
            }
        
        # Progress summary
        progress_summary = self.get_current_progress()
        
        return {
            'timestamp': current_time,
            'monitoring_duration': current_time - (self.resource_snapshots[0].timestamp if self.resource_snapshots else current_time),
            'resource_usage': resource_summary,
            'recent_bottlenecks': bottlenecks,
            'worst_operations': worst_operations,
            'phase_statistics': phase_summary,
            'progress': progress_summary,
            'total_metrics_recorded': len(self.metrics),
            'total_resource_snapshots': len(self.resource_snapshots)
        }
    
    def save_performance_report(self, filename: str = None) -> str:
        """Save detailed performance report to file"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_report_{timestamp}.json"
        
        summary = self._generate_performance_summary()
        
        with open(filename, 'w') as f:
            json.dump(summary, f, indent=2, default=str)
        
        print(f"📄 Performance report saved: {filename}")
        return filename
    
    def plot_performance_trends(self, save_path: str = None):
        """Plot performance trends"""
        if not self.enable_plotting or not self.resource_snapshots:
            return
        
        timestamps = [s.timestamp for s in self.resource_snapshots]
        start_time = timestamps[0]
        relative_times = [(t - start_time) / 60 for t in timestamps]  # Convert to minutes
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Performance Monitoring Dashboard', fontsize=16)
        
        # CPU usage
        cpu_data = [s.cpu_percent for s in self.resource_snapshots]
        axes[0, 0].plot(relative_times, cpu_data, 'b-', alpha=0.7)
        axes[0, 0].set_title('CPU Usage (%)')
        axes[0, 0].set_ylabel('CPU %')
        axes[0, 0].grid(True, alpha=0.3)
        
        # Memory usage
        memory_data = [s.memory_used_mb for s in self.resource_snapshots]
        axes[0, 1].plot(relative_times, memory_data, 'r-', alpha=0.7)
        axes[0, 1].set_title('Memory Usage (MB)')
        axes[0, 1].set_ylabel('Memory (MB)')
        axes[0, 1].grid(True, alpha=0.3)
        
        # Thread count
        thread_data = [s.active_threads for s in self.resource_snapshots]
        axes[1, 0].plot(relative_times, thread_data, 'g-', alpha=0.7)
        axes[1, 0].set_title('Active Threads')
        axes[1, 0].set_ylabel('Thread Count')
        axes[1, 0].grid(True, alpha=0.3)
        
        # Fitness progress (if available)
        if self.progress_estimator and len(self.progress_estimator.fitness_history) > 0:
            fitness_data = list(self.progress_estimator.fitness_history)
            axes[1, 1].plot(range(len(fitness_data)), fitness_data, 'purple', alpha=0.7)
            axes[1, 1].set_title('Fitness Progress')
            axes[1, 1].set_ylabel('Best Fitness')
            axes[1, 1].set_xlabel('Generation')
            axes[1, 1].grid(True, alpha=0.3)
        else:
            axes[1, 1].text(0.5, 0.5, 'No fitness data available', 
                          ha='center', va='center', transform=axes[1, 1].transAxes)
            axes[1, 1].set_title('Fitness Progress')
        
        # Set x-axis labels for time-based plots
        for ax in axes[0, :]:
            ax.set_xlabel('Time (minutes)')
        axes[1, 0].set_xlabel('Time (minutes)')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 Performance plots saved: {save_path}")
        else:
            plt.show()
    
    def print_real_time_status(self):
        """Print real-time optimization status"""
        progress = self.get_current_progress()
        recent_bottlenecks = self.bottleneck_detector.get_recent_bottlenecks(1)
        
        if progress:
            print(f"\r🔄 Gen {progress['generation']}/{progress['total_generations']} "
                  f"({progress['progress_percent']:.1f}%) ETA: {progress['eta_string']}", 
                  end='', flush=True)
        
        if recent_bottlenecks:
            print(f"\n⚠️  Bottleneck detected: {recent_bottlenecks[-1]['operation']}")


class TimingContext:
    """Context manager for timing operations"""
    
    def __init__(self, monitor: PerformanceMonitor, operation_name: str):
        self.monitor = monitor
        self.operation_name = operation_name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.start_time:
            duration = time.time() - self.start_time
            self.monitor.bottleneck_detector.record_timing(self.operation_name, duration)
            self.monitor.record_metric(f"{self.operation_name}_time", duration)


# Global performance monitor instance
_global_monitor = None

def get_performance_monitor(enable_profiling: bool = True, enable_plotting: bool = True) -> PerformanceMonitor:
    """Get global performance monitor instance"""
    global _global_monitor
    if _global_monitor is None:
        _global_monitor = PerformanceMonitor(enable_profiling, enable_plotting)
    return _global_monitor

def clear_performance_monitor():
    """Clear global performance monitor"""
    global _global_monitor
    if _global_monitor:
        _global_monitor.stop_monitoring()
        _global_monitor = None