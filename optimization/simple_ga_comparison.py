#!/usr/bin/env python3
"""
Simple GA Comparison: PyMoo vs DEAP for Wind Farm Layout Optimization
A simplified comparison script for testing PyMoo GA vs DEAP GA performance
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import time
import json
import warnings
from pathlib import Path
warnings.filterwarnings('ignore')

# Import optimization frameworks
import params as pr
from deap_algorithms_external import create_deap_algorithm

# Set up paths
workDir = str(Path(__file__).parent.absolute())
floris_path = f"{workDir}/FLORIS_311_VF1_Operational"
import sys
sys.path.append(floris_path)

from core.tools import FlorisInterface
from core.tools.optimization.layout_optimization.LayoutOptimizationPymoo import LayoutOptimizationPymoo


class SimpleGAComparison:
    """Simple GA comparison between PyMoo and DEAP"""
    
    def __init__(self):
        self.results = {}
        self.timestamp = time.strftime("%Y%m%d_%H%M%S")
        
    def setup_simple_wind_farm(self):
        """Setup a simple wind farm for testing"""
        print("🌪️ Setting up simple wind farm for GA comparison...")
        
        # Create simple wind conditions
        wind_directions = np.array([0., 90., 180., 270.])
        wind_speeds = np.array([8., 10., 12.])
        
        # Create simple frequency matrix (uniform distribution)
        freq = np.ones((len(wind_directions), len(wind_speeds))) / (len(wind_directions) * len(wind_speeds))
        
        # Load layout and filter to internal turbines
        layout = pd.read_csv("Input/Initial.layout.csv")
        layout = layout.rename(columns={'easting':'x','northing':'y'})
        internal_layout = layout[layout['external'] == False].copy()
        
        print(f"Internal turbines: {len(internal_layout)}")
        
        # Initialize FLORIS with simple configuration
        fi = FlorisInterface(pr.FLORIS_CONFIG)
        fi.reinitialize(
            layout=(internal_layout['x'], internal_layout['y']),
            wind_directions=wind_directions,
            wind_speeds=wind_speeds
        )
        
        # Calculate baseline AEP
        fi.calculate_wake()
        baseline_aep = fi.get_farm_AEP(freq=freq) / 1E9  # GWh
        
        print(f"Baseline AEP: {baseline_aep:.3f} GWh")
        print(f"Wind conditions: {len(wind_directions)} directions, {len(wind_speeds)} speeds")
        
        return fi, freq, internal_layout, baseline_aep
    
    def run_pymoo_ga(self, fi, freq, layout, baseline_aep, generations=3, pop_size=8):
        """Run PyMoo GA optimization"""
        print(f"\n🧬 Running PyMoo GA (gen={generations}, pop={pop_size})...")
        
        # Load boundaries
        boundaries_df = pd.read_csv(pr.boundariesFile) 
        boundaries = list(zip(boundaries_df['X'], boundaries_df['Y']))
        
        start_time = time.time()
        
        # Create optimizer
        optimizer = LayoutOptimizationPymoo(
            fi,
            boundaries,
            min_dist=pr.min_dist,
            freq=freq,
            n_gen=generations,
            pop_size=pop_size,
            ftol=1e-6
        )
        
        # Run optimization
        result = optimizer.optimize()
        total_time = time.time() - start_time
        
        # Get final AEP
        opt_x, opt_y = optimizer.get_optimized_locs()
        fi.reinitialize(layout=(opt_x, opt_y))
        final_aep = fi.get_farm_AEP(freq=freq) / 1E9
        
        results = {
            'framework': 'PyMoo',
            'algorithm': 'GA',
            'final_aep': final_aep,
            'baseline_aep': baseline_aep,
            'improvement_pct': ((final_aep - baseline_aep) / baseline_aep) * 100,
            'runtime_sec': total_time,
            'generations': generations,
            'pop_size': pop_size,
            'n_turbines': len(layout)
        }
        
        print(f"✅ PyMoo GA: AEP={final_aep:.3f} GWh (+{results['improvement_pct']:.2f}%) in {total_time:.1f}s")
        return results
    
    def run_deap_ga(self, fi, freq, layout, baseline_aep, generations=3, pop_size=8):
        """Run DEAP GA optimization"""
        print(f"\n🧬 Running DEAP GA (gen={generations}, pop={pop_size})...")
        
        # Create simplified problem for DEAP
        class SimpleProblem:
            def __init__(self, fi, freq, layout, boundaries_df):
                self.fi = fi
                self.freq = freq
                self.layout = layout
                self.n_turbines = len(layout)
                self.n_var = 2 * self.n_turbines
                self.n_obj = 1
                self.n_constr = 0  # Simplified - no constraints for this test
                
                # Get boundary limits
                self.xmin = boundaries_df['X'].min()
                self.xmax = boundaries_df['X'].max()
                self.ymin = boundaries_df['Y'].min()
                self.ymax = boundaries_df['Y'].max()
                
            def _evaluate(self, X, out):
                """Evaluate for DEAP"""
                if X.ndim == 1:
                    X = X.reshape(1, -1)
                    
                n_samples = X.shape[0]
                F = np.zeros((n_samples, 1))
                
                for i in range(n_samples):
                    # Denormalize coordinates
                    x_norm = X[i, :self.n_turbines]
                    y_norm = X[i, self.n_turbines:]
                    
                    x_real = x_norm * (self.xmax - self.xmin) + self.xmin
                    y_real = y_norm * (self.ymax - self.ymin) + self.ymin
                    
                    # Calculate AEP
                    try:
                        self.fi.reinitialize(layout=(x_real, y_real))
                        aep = self.fi.get_farm_AEP(freq=self.freq) / 1E9
                        F[i, 0] = aep
                    except:
                        F[i, 0] = 0.0
                
                out["F"] = F
        
        # Load boundaries and create problem
        boundaries_df = pd.read_csv(pr.boundariesFile)
        problem = SimpleProblem(fi, freq, layout, boundaries_df)
        
        start_time = time.time()
        
        # Create DEAP optimizer
        deap_optimizer = create_deap_algorithm(
            problem,
            algorithm_name="GA",
            pop_size=pop_size,
            n_gen=generations,
            n_workers=1,
            verbose=False
        )
        
        # Run optimization
        result = deap_optimizer.optimize()
        total_time = time.time() - start_time
        
        # Get final AEP from best solution
        best_x = result['x']
        x_norm = best_x[:problem.n_turbines]
        y_norm = best_x[problem.n_turbines:]
        
        x_real = x_norm * (problem.xmax - problem.xmin) + problem.xmin
        y_real = y_norm * (problem.ymax - problem.ymin) + problem.ymin
        
        fi.reinitialize(layout=(x_real, y_real))
        final_aep = fi.get_farm_AEP(freq=freq) / 1E9
        
        results = {
            'framework': 'DEAP',
            'algorithm': 'GA',
            'final_aep': final_aep,
            'baseline_aep': baseline_aep,
            'improvement_pct': ((final_aep - baseline_aep) / baseline_aep) * 100,
            'runtime_sec': total_time,
            'generations': generations,
            'pop_size': pop_size,
            'n_turbines': len(layout),
            'n_evals': result['n_evals']
        }
        
        print(f"✅ DEAP GA: AEP={final_aep:.3f} GWh (+{results['improvement_pct']:.2f}%) in {total_time:.1f}s")
        return results
    
    def compare_frameworks(self, generations=3, pop_size=8):
        """Run complete comparison"""
        print("🚀 Simple GA Framework Comparison")
        print("=" * 50)
        
        # Setup wind farm
        fi, freq, layout, baseline_aep = self.setup_simple_wind_farm()
        
        # Run PyMoo GA
        pymoo_results = self.run_pymoo_ga(fi, freq, layout, baseline_aep, generations, pop_size)
        self.results['pymoo'] = pymoo_results
        
        # Run DEAP GA  
        deap_results = self.run_deap_ga(fi, freq, layout, baseline_aep, generations, pop_size)
        self.results['deap'] = deap_results
        
        # Generate comparison
        self.generate_comparison()
        
        return self.results
    
    def generate_comparison(self):
        """Generate comparison report"""
        print(f"\n📊 GA COMPARISON RESULTS")
        print("=" * 50)
        
        pymoo = self.results['pymoo']
        deap = self.results['deap']
        
        print(f"PyMoo GA:")
        print(f"  Final AEP: {pymoo['final_aep']:.3f} GWh")
        print(f"  Improvement: {pymoo['improvement_pct']:+.2f}%")
        print(f"  Runtime: {pymoo['runtime_sec']:.1f} seconds")
        
        print(f"\nDEAP GA:")
        print(f"  Final AEP: {deap['final_aep']:.3f} GWh")
        print(f"  Improvement: {deap['improvement_pct']:+.2f}%")
        print(f"  Runtime: {deap['runtime_sec']:.1f} seconds")
        print(f"  Evaluations: {deap.get('n_evals', 'N/A')}")
        
        # Calculate relative differences
        aep_diff = ((deap['final_aep'] - pymoo['final_aep']) / pymoo['final_aep']) * 100
        time_diff = ((deap['runtime_sec'] - pymoo['runtime_sec']) / pymoo['runtime_sec']) * 100
        
        print(f"\n📈 RELATIVE PERFORMANCE (DEAP vs PyMoo):")
        print(f"  AEP Difference: {aep_diff:+.2f}%")
        print(f"  Runtime Difference: {time_diff:+.2f}%")
        
        # Determine winner
        if abs(aep_diff) < 1.0:
            winner = "Similar AEP performance"
        elif aep_diff > 0:
            winner = "DEAP GA (better AEP)"
        else:
            winner = "PyMoo GA (better AEP)"
            
        if abs(time_diff) < 10.0:
            speed_winner = "Similar runtime"
        elif time_diff < 0:
            speed_winner = "DEAP GA (faster)"
        else:
            speed_winner = "PyMoo GA (faster)"
        
        print(f"\n🏆 RESULTS:")
        print(f"  AEP Performance: {winner}")
        print(f"  Runtime Performance: {speed_winner}")
        
        # Save results
        filename = f"simple_ga_comparison_{self.timestamp}.json"
        with open(filename, 'w') as f:
            json.dump(self.results, f, indent=2, default=str)
        print(f"\n📁 Results saved to: {filename}")
        
        # Create simple plot
        self.create_comparison_plot()
    
    def create_comparison_plot(self):
        """Create simple comparison visualization"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
        
        frameworks = ['PyMoo GA', 'DEAP GA']
        aeps = [self.results['pymoo']['final_aep'], self.results['deap']['final_aep']]
        runtimes = [self.results['pymoo']['runtime_sec'], self.results['deap']['runtime_sec']]
        baseline = self.results['pymoo']['baseline_aep']
        
        # AEP comparison
        bars1 = ax1.bar(frameworks, aeps, color=['#1f77b4', '#ff7f0e'], alpha=0.7)
        ax1.axhline(y=baseline, color='red', linestyle='--', label=f'Baseline: {baseline:.3f} GWh')
        ax1.set_ylabel('AEP (GWh)')
        ax1.set_title('Annual Energy Production')
        ax1.legend()
        
        for bar, aep in zip(bars1, aeps):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                    f'{aep:.3f}', ha='center', va='bottom')
        
        # Runtime comparison
        bars2 = ax2.bar(frameworks, runtimes, color=['#1f77b4', '#ff7f0e'], alpha=0.7)
        ax2.set_ylabel('Runtime (seconds)')
        ax2.set_title('Optimization Runtime')
        
        for bar, runtime in zip(bars2, runtimes):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{runtime:.1f}s', ha='center', va='bottom')
        
        plt.tight_layout()
        
        plot_filename = f"simple_ga_comparison_{self.timestamp}.png"
        plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
        print(f"📈 Plot saved to: {plot_filename}")
        plt.show()


def main():
    """Main function"""
    try:
        comparison = SimpleGAComparison()
        results = comparison.compare_frameworks(generations=3, pop_size=32)
        print("\n✅ Simple GA comparison completed successfully!")
        return True
    except Exception as e:
        print(f"\n❌ Comparison failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
