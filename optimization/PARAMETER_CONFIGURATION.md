# Wind Farm Optimization Parameter Configuration

All optimization parameters are now centralized in `params.py` for easy configuration and consistency across both optimization scripts.

## Configuration Parameters in params.py

### Core Optimization Parameters
```python
solver = 'NSGA2'                    # Optimization algorithm
MAXGEN = 10                         # Maximum generations
PopSize = 32                        # Population size (multiple of 4)
pCross_real = 0.9                   # Crossover probability (0.6-1.0)
eta_c = 10.0                        # Crossover distribution index (5.0-20.0)
eta_m = 50.0                        # Mutation distribution index (5.0-50.0)
ftol = 1E-06                        # Convergence accuracy
```

### File Paths
```python
FLORIS_CONFIG = './Input/config_Jensen_FLS_Validation_FastAEP.yaml'
inputLayoutFile = './Input/Initial.layout.csv'
windRoseFile = './Input/timeseries.txt'
boundariesFile = './Input/Boundaries-genPoints.csv'
min_dist = 3481.5                  # Minimum distance between turbines
```

### External Turbine Configuration
```python
use_external_turbines = True        # Enable/disable external turbine loading
max_distance_km = 14               # Maximum distance for external turbine inclusion (km)
use_enhanced_filtering = False      # Enable enhanced directional farm filtering
```

### Enhanced Plotting and Visualization
```python
plot_farm_analysis = True          # Enable enhanced farm analysis plots
farm_sector_width = 90             # Angular width for directional sectors (degrees)
farm_grouping = 'auto'             # Farm grouping: 'auto', 'by_name', 'by_cluster', 'individual'
analysis_directions = [0, 90, 180, 270]  # Wind directions for analysis plots

# Plotting Controls
plot_iterations = True             # Enable iteration plotting during optimization
iteration_interval = 10            # Plot every N generations (1 = every generation)
plot_layout_before_optim = True    # Plot initial layout before optimization
plot_external_comparison = True    # Plot comparison of internal vs external turbines
create_enhanced_plots = True       # Create enhanced visualization plots
```

### Parallel Processing
```python
n_workers = 32                     # Number of parallel workers
timeout_per_eval = 300             # Timeout in seconds per evaluation
```

## How to Use

### 1. Basic Configuration
Simply edit `params.py` to change any parameter:
```python
# To change distance threshold
max_distance_km = 20

# To disable external turbines
use_external_turbines = False

# To change plotting frequency
iteration_interval = 5  # Plot every 5 generations
```

### 2. Running Scripts
Both scripts now automatically read all parameters from `params.py`:

```bash
# Run hybrid optimization
python3 opt-pymoo-windrose-freq-ts-external_hybrid.py

# Run smart optimization with directional filtering
python3 opt-pymoo-windrose-freq-ts-external_smart.py
```

### 3. Override Parameters (Advanced)
You can still override parameters programmatically if needed:
```python
# In script or interactive use
success = main(plot_iterations=False, iteration_interval=20)
```

## Parameter Effects

### External Turbine Parameters
- **`use_external_turbines`**: 
  - `True`: Load and include external turbines in optimization
  - `False`: Internal-only optimization
  
- **`max_distance_km`**: 
  - Controls how far external turbines are included
  - Smaller values = fewer external turbines = faster optimization
  - Larger values = more external turbines = more accurate wake modeling

- **`use_enhanced_filtering`**: 
  - `True`: Use directional farm filtering (smart script feature)
  - `False`: Use simple distance filtering

### Plotting Parameters
- **`plot_farm_analysis`**: Creates detailed farm sector and directional coverage plots
- **`farm_sector_width`**: Controls angular width of directional sectors (30°-180°)
- **`farm_grouping`**: Controls how external turbines are grouped into farms
- **`iteration_interval`**: Controls plotting frequency (1 = every generation, 10 = every 10th)

### Performance Parameters
- **`n_workers`**: Number of parallel processes (match your CPU cores)
- **`timeout_per_eval`**: Prevents hanging evaluations
- **`PopSize`**: Larger = better exploration, slower convergence
- **`MAXGEN`**: More generations = better optimization, longer runtime

## Common Configurations

### Fast Testing
```python
MAXGEN = 5
PopSize = 16
max_distance_km = 10
plot_iterations = False
```

### Production Run
```python
MAXGEN = 50
PopSize = 64
max_distance_km = 20
plot_iterations = True
iteration_interval = 5
```

### Internal-Only Optimization
```python
use_external_turbines = False
plot_farm_analysis = False
```

### Enhanced Analysis
```python
plot_farm_analysis = True
farm_sector_width = 60
analysis_directions = [0, 45, 90, 135, 180, 225, 270, 315]
create_enhanced_plots = True
```