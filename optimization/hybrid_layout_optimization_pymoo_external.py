#!/usr/bin/env python3
"""
Hybrid Layout Optimization using Your Established Framework
Based on LayoutOptimizationPymooExternal.py with multi-stage hybrid algorithms
"""

import numpy as np
from scipy.spatial.distance import cdist
# from shapely.geometry import Point, Polygon, LineString, MultiPolygon  # Temporarily disabled
from concurrent.futures import ProcessPoolExecutor, as_completed, TimeoutError
import multiprocessing as mp
from multiprocessing import Manager, Queue
import matplotlib.pyplot as plt
import csv
import time
import os
import psutil
import gc
from functools import partial
import warnings
warnings.filterwarnings('ignore')

from pymoo.core.problem import Problem
from pymoo.optimize import minimize
from pymoo.operators.crossover.sbx import SBX
from pymoo.operators.mutation.pm import PolynomialMutation
from pymoo.operators.sampling.rnd import FloatRandomSampling
from pymoo.core.population import Population
from pymoo.termination.ftol import MultiObjectiveSpaceTermination
from pymoo.termination.default import DefaultSingleObjectiveTermination
from pymoo.core.parameters import get_params, flatten, set_params, hierarchical
from pymoo.algorithms.hyperparameters import SingleObjectiveSingleRun, HyperparameterProblem
from pymoo.termination.robust import RobustTermination
from pymoo.operators.sampling.lhs import LHS
from pymoo.constraints.as_penalty import ConstraintsAsPenalty
from pymoo.core.evaluator import Evaluator
from pymoo.core.individual import Individual
from pymoo.algorithms.moo.unsga3 import UNSGA3
from pymoo.operators.selection.tournament import TournamentSelection
from pymoo.core.termination import TerminateIfAny
from pymoo.core.callback import Callback
from pymoo.core.mating import Mating
from pymoo.operators.selection.rnd import RandomSelection

# Hybrid algorithm imports
from pymoo.algorithms.soo.nonconvex.ga import GA
from pymoo.algorithms.soo.nonconvex.de import DE
from pymoo.algorithms.soo.nonconvex.pso import PSO
from pymoo.algorithms.moo.nsga2 import NSGA2

# Import base optimization
import sys
import os
workDir = os.getcwd()
floris_path = workDir + '/FLORIS_311_VF1_Operational'
sys.path.append(floris_path)

from core.tools.optimization.layout_optimization.layout_optimization_base import LayoutOptimization


class OptimizedCallback(Callback):
    """Optimized callback with buffered file writing and real-time monitoring"""
    
    def __init__(self, filename, buffer_size=1, monitor_system=True):
        super().__init__()
        self.filename = filename
        self.buffer_size = buffer_size
        self.buffer = []
        self.monitor_system = monitor_system


class IterationPlottingCallback(Callback):
    """Callback for plotting optimization progress during iterations"""
    
    def __init__(self, stage_name, boundaries, plot_interval=10, output_dir="Output"):
        super().__init__()
        self.stage_name = stage_name
        self.boundaries = boundaries
        self.plot_interval = plot_interval
        self.output_dir = output_dir
        self.generation_count = 0
        
    def notify(self, algorithm):
        self.generation_count += 1
        
        # Only plot at specified intervals
        if self.generation_count % self.plot_interval != 0:
            return
            
        try:
            # Get best solution from current population
            if hasattr(algorithm, 'pop') and algorithm.pop is not None:
                pop = algorithm.pop
                if hasattr(pop, 'get') and pop.get("F") is not None:
                    f_values = pop.get("F").flatten()
                    if len(f_values) > 0:
                        best_idx = np.argmax(f_values)  # Maximize AEP (negative values)
                        best_x = pop.get("X")[best_idx]
                        
                        # Convert from normalized to real coordinates if needed
                        if hasattr(algorithm, 'problem') and hasattr(algorithm.problem, '_denorm'):
                            # Get problem instance to denormalize
                            problem = algorithm.problem
                            n_vars_per_turb = len(best_x) // 2
                            
                            # Denormalize x and y coordinates
                            x_norm = best_x[:n_vars_per_turb]
                            y_norm = best_x[n_vars_per_turb:]
                            
                            best_x_real = problem._denorm(x_norm, problem.xmin, problem.xmax)
                            best_y_real = problem._denorm(y_norm, problem.ymin, problem.ymax)
                            
                            # Import plotting function
                            from utilities import plot_iteration_solution
                            
                            # Create filename with stage info
                            generation_name = f"{self.stage_name}_gen_{self.generation_count:03d}"
                            
                            # Plot the iteration
                            plot_iteration_solution(
                                generation=generation_name,
                                best_x=best_x_real,
                                best_y=best_y_real,
                                boundaries=self.boundaries,
                                output_dir=self.output_dir,
                                iteration_interval=1  # Force plotting since we're already at interval
                            )
                            
        except Exception as e:
            print(f"Warning: Iteration plotting failed for {self.stage_name} gen {self.generation_count}: {e}")


class OptimizedCallbackOrig(Callback):
    """Original optimized callback with buffered file writing and real-time monitoring"""
    
    def notify(self, algorithm):
        # Get current generation info
        gen = algorithm.n_gen
        pop = algorithm.pop
        
        # Calculate metrics
        if hasattr(pop, 'get') and pop.get("F") is not None:
            f_values = pop.get("F").flatten()
            best_f = np.max(f_values) if len(f_values) > 0 else 0
            mean_f = np.mean(f_values) if len(f_values) > 0 else 0
            std_f = np.std(f_values) if len(f_values) > 0 else 0
        else:
            best_f = mean_f = std_f = 0
        
        # Create log line
        line = f"{gen},{best_f:.6e},{mean_f:.6e},{std_f:.6e}"
        
        # Add system monitoring if enabled
        if self.monitor_system:
            try:
                cpu_percent = psutil.cpu_percent()
                memory_percent = psutil.virtual_memory().percent
                line += f",{cpu_percent:.1f},{memory_percent:.1f}"
            except:
                line += ",0.0,0.0"
        
        line += "\n"
        
        # Buffer the output
        self.buffer.append(line)
        
        # Write buffer to file when full or immediately if buffer_size=1
        if len(self.buffer) >= self.buffer_size:
            self._flush_buffer()
    
    def _flush_buffer(self):
        if self.buffer:
            with open(self.filename, 'a') as f:
                f.writelines(self.buffer)
            self.buffer.clear()
    
    def __del__(self):
        """Ensure buffer is flushed on destruction"""
        self._flush_buffer()


class AdaptiveMating(Mating):
    """Adaptive mating that handles small feasible populations"""
    
    def __init__(self, selection, crossover, mutation, **kwargs):
        super().__init__(selection, crossover, mutation, **kwargs)
        self.fallback_selection = RandomSelection()
    
    def do(self, problem, pop, n_offsprings, **kwargs):
        # Get feasible solutions
        feasible_mask = pop.get("feasible")
        n_feasible = np.sum(feasible_mask)
        
        # If we have very few feasible solutions, use a different strategy
        if n_feasible < 4:  # Need at least 4 for proper tournament selection
            print(f"WARNING: Only {n_feasible} feasible solutions. Using adaptive strategy.")
            
            # If we have at least 2 feasible solutions, duplicate them
            if n_feasible >= 2:
                feasible_pop = pop[feasible_mask]
                # Duplicate feasible solutions to create more diversity
                duplicated = Population.empty(n_offsprings)
                for i in range(n_offsprings):
                    idx = i % len(feasible_pop)
                    duplicated[i] = feasible_pop[idx]
                
                # Apply mutation more aggressively
                duplicated = self.mutation.do(problem, duplicated)
                return duplicated
            else:
                # If we have less than 2 feasible solutions, create random solutions
                from pymoo.operators.sampling.rnd import FloatRandomSampling
                sampling = FloatRandomSampling()
                return sampling(problem, n_offsprings)
        
        # Normal mating process
        return super().do(problem, pop, n_offsprings, **kwargs)


class HybridLayoutOptimizationPymoo(LayoutOptimization):
    """
    Hybrid Layout Optimization with multi-stage algorithms and external turbine support
    Based on your LayoutOptimizationPymooExternal.py patterns
    """
    
    def __init__(
        self,
        fi,
        boundaries,
        freq=None,
        bnds=None,
        min_dist=None,
        optOptions=None,
        problem_name="hybridLayoutOptimization", 
        n_gen=100, 
        pop_size=32, 
        ftol=1E-09,
        turbines_weights=None,
        crossover=None, 
        mutation=None, 
        sampling=None,
        termination=None,
        n_workers=32,  # Number of parallel workers
        use_monitoring=True,  # Enable real-time monitoring
        timeout_per_eval=300,  # Timeout in seconds per evaluation
        hybrid_stages=['GA', 'DE', 'PSO'],  # Hybrid algorithm stages
        stage_generations=[30, 50, 20],  # Generations per stage
        stage_transitions='auto'  # 'auto', 'fixed', or 'adaptive'
    ):
        super().__init__(fi, boundaries, min_dist=min_dist, freq=freq)
        
        # Store parameters following your pattern
        self.problem_name = problem_name
        self.n_gen = n_gen
        self.ftol = ftol
        self.pop_size = pop_size
        
        # Initialize operators with defaults if not provided
        self.crossover = crossover if crossover is not None else SBX(prob=0.9, eta=10)
        self.mutation = mutation if mutation is not None else PolynomialMutation(prob=1.0, eta=30)
        self.sampling = sampling if sampling is not None else FloatRandomSampling()
        
        # Set up termination using your pattern
        if termination is None:
            self.termination = DefaultSingleObjectiveTermination(
                xtol=1e-8,
                cvtol=1e-6,
                ftol=ftol,
                period=20,
                n_max_gen=n_gen,
                n_max_evals=100000
            )
        else:
            self.termination = termination
            
        # Parallelization parameters following your pattern
        self.n_workers = n_workers
        self.use_monitoring = use_monitoring
        self.timeout_per_eval = timeout_per_eval
        
        # Hybrid-specific parameters
        self.hybrid_stages = hybrid_stages
        self.stage_generations = stage_generations
        self.stage_transitions = stage_transitions
        
        # Validate stage configuration
        if len(self.hybrid_stages) != len(self.stage_generations):
            raise ValueError("Number of hybrid stages must match number of stage generations")
        
        if sum(self.stage_generations) != self.n_gen:
            print(f"Warning: Stage generations sum ({sum(self.stage_generations)}) != total generations ({self.n_gen})")
            # Adjust proportionally
            ratio = self.n_gen / sum(self.stage_generations)
            self.stage_generations = [int(g * ratio) for g in self.stage_generations]
            # Adjust last stage to match exactly
            self.stage_generations[-1] = self.n_gen - sum(self.stage_generations[:-1])
            print(f"Adjusted stage generations: {self.stage_generations}")
        
        # Initialize turbine configuration using your exact pattern
        self.nturbines = self.fi.floris.farm.n_turbines
        self.original_turbines_weights = turbines_weights  # Store original weights for algorithm initialization
        self._initialize_turbine_configuration(turbines_weights)
        
        # Set optimization bounds AFTER turbine configuration (self.nturbines may have changed)
        if bnds is not None:
            self.bnds = bnds
        else:
            self.bnds = [(0.0, 1.0)] * (2 * self.nturbines)
            
        self.optOptions = optOptions or {"maxiter": 100, "disp": True, "iprint": 2, "ftol": 1e-9, "eps": 0.01}
        
        # Pre-compute boundary polygon/line for efficiency
        self._prepare_boundaries()
        
        # Cache for frequently used values
        self._cache = {}
        
        # Initialize monitoring using your pattern
        if self.use_monitoring:
            self._init_monitoring()

    def _norm(self, val, min_val, max_val):
        """Vectorized normalization"""
        return (np.array(val) - min_val) / (max_val - min_val)

    def _unnorm(self, val, min_val, max_val):
        """Vectorized unnormalization"""
        return np.array(val) * (max_val - min_val) + min_val

    def _init_monitoring(self):
        """Initialize monitoring components following your pattern"""
        self.monitor_data = {
            'eval_times': [],
            'cache_hits': 0,
            'cache_misses': 0,
            'timeouts': 0,
            'stage_performance': {},
            'transition_points': []
        }

    def _initialize_turbine_configuration(self, turbines_weights):
        """Initialize turbine configuration with weights - your exact pattern"""
        if turbines_weights is not None and len(turbines_weights) > 0:
            # Use numpy operations for efficiency
            weights = np.array(turbines_weights)
            self.turbs_to_opt = np.where(weights > 0)[0].tolist()
            self.turbs_extern = np.where(weights == 0)[0].tolist()
            self.numberOfInternalWTGs = len(self.turbs_to_opt)
            self.numberOfExternalWTGs = len(self.turbs_extern)
            self.nturbines = self.numberOfInternalWTGs
            
            # Vectorized normalization
            self.x0 = np.concatenate([
                self._norm(self.fi.layout_x[self.turbs_to_opt], self.xmin, self.xmax),
                self._norm(self.fi.layout_y[self.turbs_to_opt], self.ymin, self.ymax)
            ])
            
            if self.numberOfExternalWTGs > 0:
                self.xtern = np.concatenate([
                    self._norm(self.fi.layout_x[self.turbs_extern], self.xmin, self.xmax),
                    self._norm(self.fi.layout_y[self.turbs_extern], self.ymin, self.ymax)
                ])
            else:
                self.xtern = np.array([])
        else:
            self.turbs_to_opt = list(range(self.nturbines))
            self.turbs_extern = []
            self.numberOfInternalWTGs = self.nturbines
            self.numberOfExternalWTGs = 0
            
            # Vectorized normalization
            self.x0 = np.concatenate([
                self._norm(self.fi.layout_x, self.xmin, self.xmax),
                self._norm(self.fi.layout_y, self.ymin, self.ymax)
            ])
            self.xtern = np.array([])

    def _prepare_boundaries(self):
        """Pre-process boundaries for efficient distance calculations - your pattern"""
        try:
            # Try to use shapely if available
            from shapely.geometry import Polygon, LineString
            
            if isinstance(self.boundaries, list):
                try:
                    self._boundary_polygon = Polygon(self.boundaries)
                    self._boundary_line = self._boundary_polygon.boundary
                except Exception as e:
                    print(f"Warning: Failed to convert boundaries to Polygon: {e}")
                    self._boundary_polygon = None
                    self._boundary_line = None
            elif isinstance(self.boundaries, (Polygon, LineString)):
                if isinstance(self.boundaries, Polygon):
                    self._boundary_polygon = self.boundaries
                    self._boundary_line = self.boundaries.boundary
                else:
                    self._boundary_line = self.boundaries
                    self._boundary_polygon = None
            else:
                print(f"Warning: Unsupported boundary type: {type(self.boundaries)}")
                self._boundary_polygon = None
                self._boundary_line = None
        except ImportError:
            # Shapely not available, use simple boundary representation
            print("Warning: Shapely not available, using simple rectangular boundary constraints")
            self._boundary_polygon = None
            self._boundary_line = None
            
            # Extract simple bounds from boundary list
            if isinstance(self.boundaries, list):
                boundary_array = np.array(self.boundaries)
                if len(boundary_array) > 0:
                    self.xmin = np.min(boundary_array[:, 0])
                    self.xmax = np.max(boundary_array[:, 0])
                    self.ymin = np.min(boundary_array[:, 1])
                    self.ymax = np.max(boundary_array[:, 1])
                else:
                    # Default bounds
                    self.xmin, self.xmax = 0, 10000
                    self.ymin, self.ymax = 0, 10000

    def _obj_func(self, x):
        """Objective function following your pattern with external turbine handling"""
        try:
            # Reconstruct full layout: optimized internal + fixed external
            full_layout_x = np.zeros(self.numberOfInternalWTGs + self.numberOfExternalWTGs)
            full_layout_y = np.zeros(self.numberOfInternalWTGs + self.numberOfExternalWTGs)
            
            # Place optimized internal turbines
            internal_x = self._unnorm(x[:self.nturbines], self.xmin, self.xmax)
            internal_y = self._unnorm(x[self.nturbines:], self.ymin, self.ymax)
            
            for i, turb_idx in enumerate(self.turbs_to_opt):
                full_layout_x[turb_idx] = internal_x[i]
                full_layout_y[turb_idx] = internal_y[i]
            
            # Keep external turbines fixed
            if self.numberOfExternalWTGs > 0:
                external_x = self._unnorm(self.xtern[:self.numberOfExternalWTGs], self.xmin, self.xmax)
                external_y = self._unnorm(self.xtern[self.numberOfExternalWTGs:], self.ymin, self.ymax)
                
                for i, turb_idx in enumerate(self.turbs_extern):
                    full_layout_x[turb_idx] = external_x[i]
                    full_layout_y[turb_idx] = external_y[i]
            
            # Calculate AEP with FLORIS
            self.fi.reinitialize(layout=(full_layout_x, full_layout_y))
            return self.fi.get_farm_AEP(freq=self.freq) / 1E6  # Convert to GWh
            
        except Exception as e:
            print(f"Warning: Objective evaluation failed: {e}")
            return -1e-6

    def _parallel_obj_func(self, X):
        """Parallel objective function evaluation following your exact pattern"""
        n_samples = X.shape[0]
        
        if n_samples == 1:
            return self._obj_func(X[0])
        
        try:
            with ProcessPoolExecutor(max_workers=min(self.n_workers, n_samples)) as executor:
                # Submit all evaluations
                futures = [executor.submit(self._obj_func, X[i]) for i in range(n_samples)]
                
                # Collect results with timeout
                results = []
                for i, future in enumerate(futures):
                    try:
                        result = future.result(timeout=self.timeout_per_eval)
                        results.append(result)
                    except Exception as e:
                        print(f"Warning: Parallel evaluation {i} failed: {e}")
                        results.append(-1e-6)  # Penalty value
                
                return np.array(results)
                
        except Exception as e:
            print(f"Warning: Parallel evaluation failed, falling back to serial: {e}")
            return np.array([self._obj_func(X[i]) for i in range(n_samples)])

    def _space_constraint(self, x_in, rho=500):
        """Minimum distance constraint matching original implementation exactly"""
        x = [
            self._unnorm(valx, self.xmin, self.xmax)
            for valx in x_in[0: self.nturbines]
        ]
        y = [
            self._unnorm(valy, self.ymin, self.ymax)
            for valy in x_in[self.nturbines: 2 * self.nturbines]
        ]
    
        # Calculate distances between turbines
        locs = np.vstack((x, y)).T
        distances = cdist(locs, locs)
        np.fill_diagonal(distances, np.inf)  # Set diagonal elements to infinity
    
        # Check if any distance violates the minimum distance constraint
        violated_distances = distances < self.min_dist
        g = np.sum(violated_distances, axis=1)
    
        return g

    def _distance_from_boundaries(self, x_in):
        """Boundary constraint without shapely dependency"""
        x = [
            self._unnorm(valx, self.xmin, self.xmax)
            for valx in x_in[0 : self.nturbines]
        ]
        y =  [
            self._unnorm(valy, self.ymin, self.ymax)
            for valy in x_in[self.nturbines : 2 * self.nturbines]
        ]
        boundary_con = np.zeros(self.nturbines)
        
        # Simple boundary check without shapely
        # Assume rectangular boundary for now
        try:
            if hasattr(self, '_boundary_polygon') and self._boundary_polygon is not None:
                # Try to use shapely if available
                from shapely.geometry import Point
                for i in range(self.nturbines):
                    loc = Point(x[i], y[i])
                    boundary_con[i] = loc.distance(self._boundary_line)
                    if self._boundary_polygon.contains(loc) is True:
                        boundary_con[i] *=  -1.0
                    else:
                        boundary_con[i] *=   1.0
            else:
                # Fallback: simple rectangular bounds check
                for i in range(self.nturbines):
                    # Check if point is within bounds
                    if (self.xmin <= x[i] <= self.xmax and self.ymin <= y[i] <= self.ymax):
                        boundary_con[i] = -1.0  # Inside boundary
                    else:
                        # Calculate distance to nearest boundary edge
                        dx = max(self.xmin - x[i], 0, x[i] - self.xmax)
                        dy = max(self.ymin - y[i], 0, y[i] - self.ymax)
                        boundary_con[i] = np.sqrt(dx**2 + dy**2)
        except ImportError:
            # Shapely not available, use simple rectangular bounds
            for i in range(self.nturbines):
                if (self.xmin <= x[i] <= self.xmax and self.ymin <= y[i] <= self.ymax):
                    boundary_con[i] = -1.0  # Inside boundary
                else:
                    # Calculate distance to nearest boundary edge
                    dx = max(self.xmin - x[i], 0, x[i] - self.xmax)
                    dy = max(self.ymin - y[i], 0, y[i] - self.ymax)
                    boundary_con[i] = np.sqrt(dx**2 + dy**2)

        return boundary_con

    def optimize(self):
        """Enhanced hybrid optimization with multi-stage approach"""
        print(f"\n🚀 Starting Hybrid External Layout Optimization")
        print(f"   Problem: {self.problem_name}")
        print(f"   Internal turbines: {self.numberOfInternalWTGs}")
        print(f"   External turbines: {self.numberOfExternalWTGs}")
        print(f"   Population size: {self.pop_size}")
        print(f"   Total generations: {self.n_gen}")
        print(f"   Hybrid stages: {self.hybrid_stages}")
        print(f"   Stage generations: {self.stage_generations}")
        print(f"   Parallel workers: {self.n_workers}")
        print(f"   Timeout per eval: {self.timeout_per_eval}s")
        print("="*50)
        
        # Initialize problem
        from hybrid_problem_external import HybridLayoutOptimizationProblem
        problem = HybridLayoutOptimizationProblem(self)
        
        # Calculate baseline internal waked AEP for objective normalization
        print("📊 Calculating baseline internal waked AEP for objective normalization...")
        
        # Reconstruct baseline layout from x0
        baseline_internal_x = self._unnorm(self.x0[:self.nturbines], self.xmin, self.xmax)
        baseline_internal_y = self._unnorm(self.x0[self.nturbines:], self.ymin, self.ymax)
        
        # Create full baseline layout (internal + external)
        full_baseline_x = np.zeros(self.numberOfInternalWTGs + self.numberOfExternalWTGs)
        full_baseline_y = np.zeros(self.numberOfInternalWTGs + self.numberOfExternalWTGs)
        
        # Place baseline internal turbines
        for i, turb_idx in enumerate(self.turbs_to_opt):
            full_baseline_x[turb_idx] = baseline_internal_x[i]
            full_baseline_y[turb_idx] = baseline_internal_y[i]
        
        # Keep external turbines fixed
        if self.numberOfExternalWTGs > 0:
            external_x = self._unnorm(self.xtern[:self.numberOfExternalWTGs], self.xmin, self.xmax)
            external_y = self._unnorm(self.xtern[self.numberOfExternalWTGs:], self.ymin, self.ymax)
            for i, turb_idx in enumerate(self.turbs_extern):
                full_baseline_x[turb_idx] = external_x[i]
                full_baseline_y[turb_idx] = external_y[i]
        
        # Calculate baseline internal waked AEP
        self.fi.reinitialize(layout=(full_baseline_x, full_baseline_y))
        from utilities import calculate_gross_net_aep_efficient
        _, baseline_internal_aep_gwh, _ = calculate_gross_net_aep_efficient(
            self.fi, self.freq, n_internal=self.nturbines
        )
        
        # Set normalization for objective function
        problem.set_baseline_normalization(baseline_internal_aep_gwh)
        
        # Initialize population
        initial_population = np.tile(self.x0, (self.pop_size, 1))
        
        # Add diversity to population
        if self.pop_size > 1:
            perturbation = np.random.uniform(-0.02, 0.02, initial_population[1:].shape)
            initial_population[1:] += perturbation
            initial_population = np.clip(initial_population, 0.0, 1.0)
        
        current_population = Population.new("X", initial_population)
        
        # Run hybrid optimization stages
        best_solution = None
        all_results = []
        
        for stage_idx, (algorithm_name, stage_gens) in enumerate(zip(self.hybrid_stages, self.stage_generations)):
            print(f"\n🔄 Stage {stage_idx + 1}: {algorithm_name} ({stage_gens} generations)")
            
            # Skip stages with 0 generations
            if stage_gens == 0:
                print(f"   ⏭️  Skipping {algorithm_name} stage (0 generations allocated)")
                continue
            
            # Create algorithm for this stage
            algorithm = self._create_stage_algorithm(algorithm_name, current_population)
            
            # Set up stage-specific termination
            stage_termination = DefaultSingleObjectiveTermination(
                xtol=1e-8,
                cvtol=1e-6,
                ftol=self.ftol,
                period=10,
                n_max_gen=stage_gens,
                n_max_evals=stage_gens * self.pop_size * 2
            )
            
            # Set up plotting callback if enabled
            callback = None
            # TODO: Re-enable when IterationPlottingCallback is implemented
            # if hasattr(self, 'plot_iterations') and self.plot_iterations:
            #     callback = IterationPlottingCallback(
            #         stage_name=algorithm_name,
            #         boundaries=getattr(self, 'boundaries', None),
            #         plot_interval=getattr(self, 'iteration_interval', 10),
            #         output_dir="Output"
            #     )
            #     print(f"   📊 Iteration plotting enabled (every {getattr(self, 'iteration_interval', 10)} generations)")
            
            # Run stage optimization
            stage_start = time.time()
            # Build minimize kwargs
            minimize_kwargs = {
                'problem': problem,
                'algorithm': algorithm,
                'termination': stage_termination,
                'pf': True,
                'seed': stage_idx,
                'verbose': True,
                'return_least_infeasible': True,
                'save_history': False
            }
            
            # Only add callback if it's not None
            if callback is not None:
                minimize_kwargs['callback'] = callback
                
            stage_result = minimize(**minimize_kwargs)
            stage_time = time.time() - stage_start
            
            # Track stage performance
            if self.use_monitoring:
                self.monitor_data['stage_performance'][f'stage_{stage_idx}_{algorithm_name}'] = {
                    'time': stage_time,
                    'generations': stage_gens,
                    'best_f': float(stage_result.F) if stage_result.F is not None else -1e-6,
                    'n_evals': getattr(stage_result, 'n_evals', stage_gens * self.pop_size)
                }
            
            print(f"   ✅ Stage {stage_idx + 1} complete: {stage_time:.1f}s")
            print(f"   Best fitness: {stage_result.F}")
            
            # Update population for next stage
            if hasattr(stage_result, 'pop') and stage_result.pop is not None:
                current_population = stage_result.pop
            else:
                # Create population from result
                current_population = Population.new("X", stage_result.X.reshape(1, -1))
            
            # Store best solution
            if best_solution is None or (stage_result.F is not None and stage_result.F > (best_solution.F if best_solution.F is not None else -np.inf)):
                best_solution = stage_result
            
            all_results.append(stage_result)
        
        print(f"\n✅ Hybrid optimization complete!")
        print(f"   Final best fitness: {best_solution.F}")
        print(f"   Total evaluations: {sum([r.n_evals if hasattr(r, 'n_evals') else 0 for r in all_results])}")
        
        # Store final result
        self.residual_plant = best_solution
        return best_solution

    def _create_stage_algorithm(self, algorithm_name, population):
        """Create algorithm for specific stage"""
        # Common parameters
        common_params = {
            'pop_size': self.pop_size,
            'sampling': self.sampling,
            'crossover': self.crossover,
            'mutation': self.mutation
        }
        
        if algorithm_name.upper() == 'GA':
            from hybrid_algorithms_external import HybridGA_External
            return HybridGA_External(
                turbines_weights=getattr(self, 'original_turbines_weights', None),
                n_workers=self.n_workers,
                timeout_per_eval=self.timeout_per_eval,
                **common_params
            )
        elif algorithm_name.upper() == 'DE':
            from hybrid_algorithms_external import HybridDE_External
            return HybridDE_External(
                turbines_weights=getattr(self, 'original_turbines_weights', None),
                n_workers=self.n_workers,
                timeout_per_eval=self.timeout_per_eval,
                **common_params
            )
        elif algorithm_name.upper() == 'PSO':
            from hybrid_algorithms_external import HybridPSO_External
            return HybridPSO_External(
                turbines_weights=getattr(self, 'original_turbines_weights', None),
                n_workers=self.n_workers,
                timeout_per_eval=self.timeout_per_eval,
                **common_params
            )
        else:
            # Fallback to NSGA2
            return NSGA2(
                pop_size=self.pop_size,
                sampling=self.sampling,
                crossover=self.crossover,
                mutation=self.mutation
            )

    def get_optimized_locs(self):
        """Extract optimized locations with proper handling of internal/external turbines"""
        if self.residual_plant is None:
            print("Warning: No optimization result available")
            return self.fi.layout_x, self.fi.layout_y
        
        try:
            # Get the optimized solution
            x_opt = self.residual_plant.X
            
            # Reconstruct full layout
            full_layout_x = np.zeros(self.numberOfInternalWTGs + self.numberOfExternalWTGs)
            full_layout_y = np.zeros(self.numberOfInternalWTGs + self.numberOfExternalWTGs)
            
            # Place optimized internal turbines
            internal_x = self._unnorm(x_opt[:self.nturbines], self.xmin, self.xmax)
            internal_y = self._unnorm(x_opt[self.nturbines:], self.ymin, self.ymax)
            
            for i, turb_idx in enumerate(self.turbs_to_opt):
                full_layout_x[turb_idx] = internal_x[i]
                full_layout_y[turb_idx] = internal_y[i]
            
            # Keep external turbines at original positions
            if self.numberOfExternalWTGs > 0:
                external_x = self._unnorm(self.xtern[:self.numberOfExternalWTGs], self.xmin, self.xmax)
                external_y = self._unnorm(self.xtern[self.numberOfExternalWTGs:], self.ymin, self.ymax)
                
                for i, turb_idx in enumerate(self.turbs_extern):
                    full_layout_x[turb_idx] = external_x[i]
                    full_layout_y[turb_idx] = external_y[i]
            
            return full_layout_x, full_layout_y
            
        except Exception as e:
            print(f"Warning: Failed to extract optimized locations: {e}")
            return self.fi.layout_x, self.fi.layout_y


# Convenience function for backwards compatibility
def EnhancedLayoutOptimizationPymoo(*args, **kwargs):
    """Enhanced wrapper supporting hybrid optimization"""
    # Check if hybrid optimization is requested
    if 'hybrid_stages' in kwargs or 'use_hybrid' in kwargs:
        return HybridLayoutOptimizationPymoo(*args, **kwargs)
    else:
        # Import and use existing LayoutOptimizationPymoo
        from core.tools.optimization.layout_optimization.LayoutOptimizationPymooExternal import LayoutOptimizationPymoo
        return LayoutOptimizationPymoo(*args, **kwargs)