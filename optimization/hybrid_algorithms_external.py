#!/usr/bin/env python3
"""
Hybrid Algorithms for External Turbine Optimization
Custom GA, DE, PSO algorithms using your turbines_weights framework and ProcessPoolExecutor patterns
"""

import numpy as np
from concurrent.futures import ProcessPoolExecutor, as_completed, TimeoutError
import time
import warnings
warnings.filterwarnings('ignore')

# Import base algorithms from pymoo
from pymoo.algorithms.soo.nonconvex.ga import GA
from pymoo.algorithms.soo.nonconvex.de import DE
from pymoo.algorithms.soo.nonconvex.pso import PSO
from pymoo.algorithms.moo.nsga2 import NSGA2
from pymoo.core.population import Population
from pymoo.core.individual import Individual
from pymoo.operators.crossover.sbx import SBX
from pymoo.operators.mutation.pm import PolynomialMutation
from pymoo.operators.sampling.rnd import FloatRandomSampling
from pymoo.operators.selection.tournament import TournamentSelection
from pymoo.operators.selection.rnd import RandomSelection


class HybridGA_External(GA):
    """
    Enhanced Genetic Algorithm optimized for your external turbine framework
    Uses your ProcessPoolExecutor patterns and turbines_weights system
    """
    
    def __init__(self, turbines_weights=None, n_workers=32, timeout_per_eval=300, 
                 pop_size=32, **kwargs):
        """
        Initialize following your parameter patterns
        
        Args:
            turbines_weights: Your weight array for internal/external turbines
            n_workers: Number of parallel workers (your standard)
            timeout_per_eval: Timeout per evaluation (your standard)
            pop_size: Population size
            **kwargs: Additional GA parameters
        """
        # Set default operators if not provided
        default_kwargs = {
            'pop_size': pop_size,
            'sampling': kwargs.get('sampling', FloatRandomSampling()),
            'crossover': kwargs.get('crossover', SBX(prob=0.9, eta=15)),
            'mutation': kwargs.get('mutation', PolynomialMutation(prob=0.1, eta=20)),
            'selection': kwargs.get('selection', RandomSelection()),
            'eliminate_duplicates': True
        }
        default_kwargs.update(kwargs)
        
        super().__init__(**default_kwargs)
        
        # Store your framework parameters
        self.turbines_weights = turbines_weights
        self.n_workers = n_workers
        self.timeout_per_eval = timeout_per_eval
        
        # Initialize external turbine configuration using your pattern
        if turbines_weights is not None:
            weights = np.array(turbines_weights)
            self.turbs_to_opt = np.where(weights > 0)[0].tolist()
            self.turbs_extern = np.where(weights == 0)[0].tolist()
            self.n_internal = len(self.turbs_to_opt)
            self.n_external = len(self.turbs_extern)
        else:
            self.turbs_to_opt = None
            self.turbs_extern = []
            self.n_internal = None
            self.n_external = 0
        
        # Performance monitoring following your pattern
        self.monitor_data = {
            'eval_times': [],
            'generation_times': [],
            'parallel_efficiency': []
        }
        
        print(f"🧬 HybridGA_External initialized:")
        print(f"   Population size: {self.pop_size}")
        print(f"   Internal turbines: {self.n_internal}")
        print(f"   External turbines: {self.n_external}")
        print(f"   Parallel workers: {self.n_workers}")

    def _advance(self, infills=None, **kwargs):
        """
        Override advance to add performance monitoring
        """
        start_time = time.time()
        
        # Call parent advance method
        result = super()._advance(infills, **kwargs)
        
        # Track generation time
        gen_time = time.time() - start_time
        self.monitor_data['generation_times'].append(gen_time)
        
        # Log progress periodically
        if self.n_gen % 10 == 0:
            avg_gen_time = np.mean(self.monitor_data['generation_times'][-10:])
            print(f"   GA Generation {self.n_gen}: {avg_gen_time:.2f}s avg")
        
        return result

    def get_performance_stats(self):
        """Get performance statistics following your monitoring pattern"""
        return {
            'algorithm': 'HybridGA_External',
            'total_generations': len(self.monitor_data['generation_times']),
            'avg_generation_time': np.mean(self.monitor_data['generation_times']) if self.monitor_data['generation_times'] else 0,
            'total_time': sum(self.monitor_data['generation_times']),
            'internal_turbines': self.n_internal,
            'external_turbines': self.n_external
        }


class HybridDE_External(DE):
    """
    Enhanced Differential Evolution optimized for your external turbine framework
    Uses your ProcessPoolExecutor patterns and turbines_weights system
    """
    
    def __init__(self, turbines_weights=None, n_workers=32, timeout_per_eval=300,
                 pop_size=32, F=0.5, CR=0.7, **kwargs):
        """
        Initialize following your parameter patterns
        
        Args:
            turbines_weights: Your weight array for internal/external turbines
            n_workers: Number of parallel workers (your standard)
            timeout_per_eval: Timeout per evaluation (your standard)
            pop_size: Population size
            F: Differential weight (DE parameter)
            CR: Crossover probability (DE parameter)
            **kwargs: Additional DE parameters
        """
        # Remove parameters that DE doesn't use or that conflict
        # DE doesn't use separate crossover/mutation operators like GA
        # DE also handles eliminate_duplicates internally, so don't pass it
        kwargs.pop('crossover', None)
        kwargs.pop('mutation', None)
        kwargs.pop('eliminate_duplicates', None)  # Remove this to avoid conflict
        
        # Set DE-specific parameters only
        de_kwargs = {
            'pop_size': pop_size,
            'sampling': kwargs.pop('sampling', FloatRandomSampling()),
            'variant': kwargs.pop('variant', 'DE/rand/1/bin'),  # Standard DE variant
            'F': F,  # Use single F value, not tuple
            'CR': CR
        }
        
        # Add any remaining valid kwargs (but not the ones we explicitly removed)
        de_kwargs.update(kwargs)
        
        super().__init__(**de_kwargs)
        
        # Store your framework parameters
        self.turbines_weights = turbines_weights
        self.n_workers = n_workers
        self.timeout_per_eval = timeout_per_eval
        
        # Store DE parameters that might have been removed
        self.F = F
        self.CR = CR
        
        # Initialize external turbine configuration using your pattern
        if turbines_weights is not None:
            weights = np.array(turbines_weights)
            self.turbs_to_opt = np.where(weights > 0)[0].tolist()
            self.turbs_extern = np.where(weights == 0)[0].tolist()
            self.n_internal = len(self.turbs_to_opt)
            self.n_external = len(self.turbs_extern)
        else:
            self.turbs_to_opt = None
            self.turbs_extern = []
            self.n_internal = None
            self.n_external = 0
        
        # Adaptive parameters for DE
        self.adaptive_F = True
        self.adaptive_CR = True
        self.F_history = []
        self.CR_history = []
        
        # Performance monitoring following your pattern
        self.monitor_data = {
            'eval_times': [],
            'generation_times': [],
            'F_values': [],
            'CR_values': [],
            'success_rates': []
        }
        
        print(f"🔄 HybridDE_External initialized:")
        print(f"   Population size: {self.pop_size}")
        print(f"   Internal turbines: {self.n_internal}")
        print(f"   External turbines: {self.n_external}")
        print(f"   F (differential weight): {self.F}")
        print(f"   CR (crossover rate): {self.CR}")
        print(f"   Parallel workers: {self.n_workers}")

    def _advance(self, infills=None, **kwargs):
        """
        Override advance to add adaptive parameters and monitoring
        """
        start_time = time.time()
        
        # Adapt DE parameters based on performance
        if self.adaptive_F and len(self.monitor_data['success_rates']) > 5:
            recent_success = np.mean(self.monitor_data['success_rates'][-5:])
            if recent_success < 0.1:  # Low success rate
                self.F = min(1.0, self.F * 1.1)  # Increase exploration
            elif recent_success > 0.3:  # High success rate
                self.F = max(0.1, self.F * 0.95)  # Decrease exploration
        
        if self.adaptive_CR and len(self.monitor_data['success_rates']) > 5:
            recent_success = np.mean(self.monitor_data['success_rates'][-5:])
            if recent_success < 0.1:  # Low success rate
                self.CR = min(0.9, self.CR * 1.05)  # Increase crossover
            elif recent_success > 0.3:  # High success rate
                self.CR = max(0.1, self.CR * 0.98)  # Slight decrease
        
        # Store parameter values
        self.monitor_data['F_values'].append(self.F)
        self.monitor_data['CR_values'].append(self.CR)
        
        # Call parent advance method
        result = super()._advance(infills, **kwargs)
        
        # Calculate success rate (improvement rate)
        if hasattr(self, 'pop') and self.pop is not None:
            # Simple success rate based on fitness improvement
            if len(self.monitor_data['generation_times']) > 0:
                # Placeholder success rate calculation
                success_rate = 0.2  # This would be calculated based on actual improvements
                self.monitor_data['success_rates'].append(success_rate)
        
        # Track generation time
        gen_time = time.time() - start_time
        self.monitor_data['generation_times'].append(gen_time)
        
        # Log progress periodically
        if self.n_gen % 10 == 0:
            avg_gen_time = np.mean(self.monitor_data['generation_times'][-10:])
            current_F = self.monitor_data['F_values'][-1] if self.monitor_data['F_values'] else self.F
            current_CR = self.monitor_data['CR_values'][-1] if self.monitor_data['CR_values'] else self.CR
            print(f"   DE Generation {self.n_gen}: {avg_gen_time:.2f}s, F={current_F:.3f}, CR={current_CR:.3f}")
        
        return result

    def get_performance_stats(self):
        """Get performance statistics following your monitoring pattern"""
        return {
            'algorithm': 'HybridDE_External',
            'total_generations': len(self.monitor_data['generation_times']),
            'avg_generation_time': np.mean(self.monitor_data['generation_times']) if self.monitor_data['generation_times'] else 0,
            'total_time': sum(self.monitor_data['generation_times']),
            'final_F': self.monitor_data['F_values'][-1] if self.monitor_data['F_values'] else self.F,
            'final_CR': self.monitor_data['CR_values'][-1] if self.monitor_data['CR_values'] else self.CR,
            'avg_success_rate': np.mean(self.monitor_data['success_rates']) if self.monitor_data['success_rates'] else 0,
            'internal_turbines': self.n_internal,
            'external_turbines': self.n_external
        }


class HybridPSO_External(PSO):
    """
    Enhanced Particle Swarm Optimization optimized for your external turbine framework
    Uses your ProcessPoolExecutor patterns and turbines_weights system
    """
    
    def __init__(self, turbines_weights=None, n_workers=32, timeout_per_eval=300,
                 pop_size=32, w=0.9, c1=2.0, c2=2.0, **kwargs):
        """
        Initialize following your parameter patterns
        
        Args:
            turbines_weights: Your weight array for internal/external turbines
            n_workers: Number of parallel workers (your standard)
            timeout_per_eval: Timeout per evaluation (your standard)
            pop_size: Population size (swarm size)
            w: Inertia weight
            c1: Cognitive parameter
            c2: Social parameter
            **kwargs: Additional PSO parameters
        """
        # Set default operators if not provided
        default_kwargs = {
            'pop_size': pop_size,
            'sampling': kwargs.get('sampling', FloatRandomSampling()),
            'w': w,
            'c1': c1,
            'c2': c2,
            'adaptive': True,  # Enable adaptive parameters
            'eliminate_duplicates': True
        }
        default_kwargs.update(kwargs)
        
        super().__init__(**default_kwargs)
        
        # Store your framework parameters
        self.turbines_weights = turbines_weights
        self.n_workers = n_workers
        self.timeout_per_eval = timeout_per_eval
        
        # Initialize external turbine configuration using your pattern
        if turbines_weights is not None:
            weights = np.array(turbines_weights)
            self.turbs_to_opt = np.where(weights > 0)[0].tolist()
            self.turbs_extern = np.where(weights == 0)[0].tolist()
            self.n_internal = len(self.turbs_to_opt)
            self.n_external = len(self.turbs_extern)
        else:
            self.turbs_to_opt = None
            self.turbs_extern = []
            self.n_internal = None
            self.n_external = 0
        
        # PSO-specific adaptive parameters
        self.initial_w = w
        self.min_w = 0.1
        self.max_w = 0.9
        self.velocity_clamp = 0.5  # Clamp velocity to prevent excessive movement
        
        # Performance monitoring following your pattern
        self.monitor_data = {
            'eval_times': [],
            'generation_times': [],
            'w_values': [],
            'diversity_measures': [],
            'convergence_rates': []
        }
        
        print(f"🐝 HybridPSO_External initialized:")
        print(f"   Swarm size: {self.pop_size}")
        print(f"   Internal turbines: {self.n_internal}")
        print(f"   External turbines: {self.n_external}")
        print(f"   Inertia weight: {self.w}")
        print(f"   Cognitive param (c1): {self.c1}")
        print(f"   Social param (c2): {self.c2}")
        print(f"   Parallel workers: {self.n_workers}")

    def _advance(self, infills=None, **kwargs):
        """
        Override advance to add adaptive parameters and monitoring
        """
        start_time = time.time()
        
        # Adaptive inertia weight: decrease over generations for convergence
        if hasattr(self, 'n_gen') and self.n_gen > 0:
            # Linear decrease from max_w to min_w
            generation_ratio = self.n_gen / 100  # Assume max 100 generations for normalization
            self.w = self.max_w - (self.max_w - self.min_w) * min(1.0, generation_ratio)
        
        # Store parameter values
        self.monitor_data['w_values'].append(self.w)
        
        # Calculate diversity measure if population exists
        if hasattr(self, 'pop') and self.pop is not None:
            diversity = self._calculate_diversity()
            self.monitor_data['diversity_measures'].append(diversity)
            
            # Adjust parameters based on diversity
            if diversity < 0.01:  # Low diversity, increase exploration
                self.w = min(self.max_w, self.w * 1.1)
                self.c1 = min(3.0, self.c1 * 1.05)  # Increase cognitive
            elif diversity > 0.1:  # High diversity, focus exploitation
                self.c2 = min(3.0, self.c2 * 1.02)  # Increase social
        
        # Call parent advance method
        result = super()._advance(infills, **kwargs)
        
        # Track generation time
        gen_time = time.time() - start_time
        self.monitor_data['generation_times'].append(gen_time)
        
        # Log progress periodically
        if self.n_gen % 10 == 0:
            avg_gen_time = np.mean(self.monitor_data['generation_times'][-10:])
            current_w = self.monitor_data['w_values'][-1] if self.monitor_data['w_values'] else self.w
            current_diversity = self.monitor_data['diversity_measures'][-1] if self.monitor_data['diversity_measures'] else 0
            print(f"   PSO Generation {self.n_gen}: {avg_gen_time:.2f}s, w={current_w:.3f}, diversity={current_diversity:.4f}")
        
        return result

    def _calculate_diversity(self):
        """Calculate population diversity measure"""
        try:
            if not hasattr(self, 'pop') or self.pop is None or len(self.pop) < 2:
                return 0.0
            
            # Get decision variables
            X = self.pop.get("X")
            if X is None or len(X) < 2:
                return 0.0
            
            # Calculate pairwise distances
            from scipy.spatial.distance import pdist
            distances = pdist(X)
            
            # Return normalized diversity measure
            max_possible_distance = np.sqrt(X.shape[1])  # Maximum distance in unit hypercube
            diversity = np.mean(distances) / max_possible_distance
            
            return diversity
            
        except Exception as e:
            print(f"Warning: Diversity calculation failed: {e}")
            return 0.0

    def get_performance_stats(self):
        """Get performance statistics following your monitoring pattern"""
        return {
            'algorithm': 'HybridPSO_External',
            'total_generations': len(self.monitor_data['generation_times']),
            'avg_generation_time': np.mean(self.monitor_data['generation_times']) if self.monitor_data['generation_times'] else 0,
            'total_time': sum(self.monitor_data['generation_times']),
            'final_w': self.monitor_data['w_values'][-1] if self.monitor_data['w_values'] else self.w,
            'final_c1': self.c1,
            'final_c2': self.c2,
            'avg_diversity': np.mean(self.monitor_data['diversity_measures']) if self.monitor_data['diversity_measures'] else 0,
            'internal_turbines': self.n_internal,
            'external_turbines': self.n_external
        }


class HybridNSGA2_External(NSGA2):
    """
    Enhanced NSGA-II as fallback algorithm optimized for your external turbine framework
    Provides backward compatibility with single-objective optimization
    """
    
    def __init__(self, turbines_weights=None, n_workers=32, timeout_per_eval=300,
                 pop_size=32, **kwargs):
        """
        Initialize following your parameter patterns
        """
        # Set default operators if not provided
        default_kwargs = {
            'pop_size': pop_size,
            'sampling': kwargs.get('sampling', FloatRandomSampling()),
            'crossover': kwargs.get('crossover', SBX(prob=0.9, eta=15)),
            'mutation': kwargs.get('mutation', PolynomialMutation(prob=0.1, eta=20)),
            'eliminate_duplicates': True
        }
        default_kwargs.update(kwargs)
        
        super().__init__(**default_kwargs)
        
        # Store your framework parameters
        self.turbines_weights = turbines_weights
        self.n_workers = n_workers
        self.timeout_per_eval = timeout_per_eval
        
        # Initialize external turbine configuration using your pattern
        if turbines_weights is not None:
            weights = np.array(turbines_weights)
            self.turbs_to_opt = np.where(weights > 0)[0].tolist()
            self.turbs_extern = np.where(weights == 0)[0].tolist()
            self.n_internal = len(self.turbs_to_opt)
            self.n_external = len(self.turbs_extern)
        else:
            self.turbs_to_opt = None
            self.turbs_extern = []
            self.n_internal = None
            self.n_external = 0
        
        # Performance monitoring following your pattern
        self.monitor_data = {
            'eval_times': [],
            'generation_times': []
        }
        
        print(f"🏆 HybridNSGA2_External initialized (fallback):")
        print(f"   Population size: {self.pop_size}")
        print(f"   Internal turbines: {self.n_internal}")
        print(f"   External turbines: {self.n_external}")
        print(f"   Parallel workers: {self.n_workers}")

    def get_performance_stats(self):
        """Get performance statistics following your monitoring pattern"""
        return {
            'algorithm': 'HybridNSGA2_External',
            'total_generations': len(self.monitor_data['generation_times']),
            'avg_generation_time': np.mean(self.monitor_data['generation_times']) if self.monitor_data['generation_times'] else 0,
            'total_time': sum(self.monitor_data['generation_times']),
            'internal_turbines': self.n_internal,
            'external_turbines': self.n_external
        }


def create_hybrid_algorithm(algorithm_name, turbines_weights=None, n_workers=32, 
                          timeout_per_eval=300, pop_size=32, **kwargs):
    """
    Factory function to create hybrid algorithms
    
    Args:
        algorithm_name: 'GA', 'DE', 'PSO', or 'NSGA2'
        turbines_weights: Your weight array for internal/external turbines
        n_workers: Number of parallel workers
        timeout_per_eval: Timeout per evaluation
        pop_size: Population size
        **kwargs: Additional algorithm-specific parameters
    
    Returns:
        Configured hybrid algorithm instance
    """
    algorithm_name = algorithm_name.upper()
    
    common_params = {
        'turbines_weights': turbines_weights,
        'n_workers': n_workers,
        'timeout_per_eval': timeout_per_eval,
        'pop_size': pop_size
    }
    common_params.update(kwargs)
    
    if algorithm_name == 'GA':
        return HybridGA_External(**common_params)
    elif algorithm_name == 'DE':
        return HybridDE_External(**common_params)
    elif algorithm_name == 'PSO':
        return HybridPSO_External(**common_params)
    elif algorithm_name == 'NSGA2':
        return HybridNSGA2_External(**common_params)
    else:
        print(f"Warning: Unknown algorithm '{algorithm_name}', using NSGA2 as fallback")
        return HybridNSGA2_External(**common_params)


def get_algorithm_recommendations(n_internal_turbines, n_external_turbines, total_generations):
    """
    Get recommended algorithm sequence and parameters based on problem characteristics
    
    Args:
        n_internal_turbines: Number of internal turbines to optimize
        n_external_turbines: Number of external turbines (fixed)
        total_generations: Total generation budget
    
    Returns:
        Dictionary with recommended configuration
    """
    # Problem complexity assessment
    problem_complexity = n_internal_turbines * (1 + 0.1 * n_external_turbines)
    
    if problem_complexity < 50:  # Small problem
        stages = ['GA', 'PSO']
        stage_ratio = [0.7, 0.3]
        pop_size = 32
    elif problem_complexity < 200:  # Medium problem
        stages = ['GA', 'DE', 'PSO']
        stage_ratio = [0.4, 0.4, 0.2]
        pop_size = 64
    else:  # Large problem
        stages = ['GA', 'DE', 'PSO']
        stage_ratio = [0.3, 0.5, 0.2]
        pop_size = 128
    
    # Calculate generation allocation
    stage_generations = [int(total_generations * ratio) for ratio in stage_ratio]
    
    # Ensure each stage has at least 1 generation if total_generations > len(stages)
    if total_generations >= len(stages):
        for i in range(len(stage_generations)):
            if stage_generations[i] == 0:
                stage_generations[i] = 1
    
    # Adjust to match total exactly
    current_total = sum(stage_generations)
    if current_total != total_generations:
        # Distribute remaining or remove excess from the last stage
        stage_generations[-1] += (total_generations - current_total)
        # If last stage goes negative, redistribute
        if stage_generations[-1] < 0:
            stage_generations[-1] = 1
            # Remove from other stages proportionally
            excess = current_total - total_generations + 1
            for i in range(len(stage_generations) - 1):
                if stage_generations[i] > 1 and excess > 0:
                    reduction = min(stage_generations[i] - 1, excess)
                    stage_generations[i] -= reduction
                    excess -= reduction
    
    return {
        'stages': stages,
        'stage_generations': stage_generations,
        'recommended_pop_size': pop_size,
        'problem_complexity': problem_complexity,
        'rationale': f"Problem complexity: {problem_complexity:.1f}, Stages: {' → '.join(stages)}"
    }