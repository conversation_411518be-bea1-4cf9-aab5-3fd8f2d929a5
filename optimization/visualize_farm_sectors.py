#!/usr/bin/env python3
"""
Visualization tool for directional farm filtering
Shows how external farms are filtered based on wind direction
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.patches import Circle, Wedge, Rectangle, FancyBboxPatch
from matplotlib.collections import PatchCollection
import matplotlib.colors as mcolors
import os
import sys

sys.path.append('/project/bii69/ps/ps/optimization')
from test_enhanced_farm_filtering import EnhancedDirectionalFarmManager
from utilities import load_boundaries


def plot_farm_sectors(manager: EnhancedDirectionalFarmManager, wind_direction: float, 
                      output_file: str = "farm_sectors.png", boundaries=None):
    """
    Visualize farm locations and directional sectors
    
    Args:
        manager: EnhancedDirectionalFarmManager instance
        wind_direction: Wind direction to visualize (degrees)
        output_file: Output filename
        boundaries: Site boundaries (optional)
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
    
    # Common setup for both plots
    for ax in [ax1, ax2]:
        ax.set_aspect('equal')
        ax.grid(True, alpha=0.3, linestyle='--')
        ax.set_xlabel('Easting (m)', fontsize=12)
        ax.set_ylabel('Northing (m)', fontsize=12)
    
    # Left plot: All farms with bearings
    ax1.set_title('All External Farms with Bearings', fontsize=14, fontweight='bold')
    
    # Plot boundaries if provided
    if boundaries is not None:
        boundary_x = [pt[0] for pt in boundaries]
        boundary_y = [pt[1] for pt in boundaries]
        ax1.fill(boundary_x, boundary_y, alpha=0.1, color='gray', label='Site Boundary')
        ax2.fill(boundary_x, boundary_y, alpha=0.1, color='gray')
    
    # Plot internal site
    internal_x = manager.internal_layout['x'].values
    internal_y = manager.internal_layout['y'].values
    ax1.scatter(internal_x, internal_y, c='blue', s=100, marker='s', 
                edgecolors='darkblue', linewidth=2, label='Internal Turbines', zorder=5)
    ax2.scatter(internal_x, internal_y, c='blue', s=100, marker='s', 
                edgecolors='darkblue', linewidth=2, label='Internal Turbines', zorder=5)
    
    # Plot internal centroid
    cx, cy = manager.internal_centroid
    ax1.plot(cx, cy, 'b+', markersize=15, markeredgewidth=3, label='Internal Centroid')
    ax2.plot(cx, cy, 'b+', markersize=15, markeredgewidth=3)
    
    # Colors for different farms
    colors = plt.cm.Set3(np.linspace(0, 1, len(manager.external_farms)))
    
    if manager.has_external:
        # Plot all external farms with bearing lines
        for i, (farm_name, farm_data) in enumerate(manager.external_farms.items()):
            color = colors[i]
            farm_x = farm_data['turbines']['x'].values
            farm_y = farm_data['turbines']['y'].values
            
            # Plot turbines
            ax1.scatter(farm_x, farm_y, c=[color], s=50, alpha=0.7, 
                       label=f"{farm_name} ({farm_data['n_turbines']} turb)")
            
            # Plot farm centroid
            fcx, fcy = farm_data['centroid']
            ax1.plot(fcx, fcy, 'o', color=color, markersize=10, 
                    markeredgecolor='black', markeredgewidth=1)
            
            # Draw bearing line
            ax1.plot([cx, fcx], [cy, fcy], '--', color=color, alpha=0.5, linewidth=1)
            
            # Add bearing text
            mid_x = (cx + fcx) / 2
            mid_y = (cy + fcy) / 2
            ax1.text(mid_x, mid_y, f"{farm_data['bearing']:.0f}°", 
                    fontsize=9, ha='center', va='center',
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.7))
    
    # Right plot: Filtered for specific wind direction
    ax2.set_title(f'Active Farms for Wind from {wind_direction}°', fontsize=14, fontweight='bold')
    
    # Draw wind direction arrow
    arrow_length = max(ax2.get_xlim()[1] - ax2.get_xlim()[0], 
                      ax2.get_ylim()[1] - ax2.get_ylim()[0]) * 0.15
    wind_rad = np.radians(270 - wind_direction)  # Convert to mathematical angle
    arrow_dx = arrow_length * np.cos(wind_rad)
    arrow_dy = arrow_length * np.sin(wind_rad)
    
    ax2.arrow(cx - arrow_dx/2, cy - arrow_dy/2, arrow_dx, arrow_dy,
              head_width=arrow_length*0.1, head_length=arrow_length*0.1,
              fc='red', ec='darkred', linewidth=2, alpha=0.8, zorder=10)
    ax2.text(cx - arrow_dx*0.7, cy - arrow_dy*0.7, f'Wind\n{wind_direction}°',
             ha='center', va='center', fontsize=10, fontweight='bold',
             bbox=dict(boxstyle='round,pad=0.5', facecolor='yellow', alpha=0.8))
    
    # Draw sector wedge
    if manager.has_external:
        # Calculate the average distance to external farms for wedge size
        avg_distance = np.mean([f['distance'] for f in manager.external_farms.values()])
        wedge_radius = avg_distance * 1.5
        
        # Wind comes FROM this direction, so upwind sector is in that direction
        start_angle = wind_direction - manager.sector_width/2
        end_angle = wind_direction + manager.sector_width/2
        
        # Convert to matplotlib angles (counterclockwise from East)
        start_angle_mpl = 90 - end_angle
        end_angle_mpl = 90 - start_angle
        
        wedge = Wedge((cx, cy), wedge_radius, start_angle_mpl, end_angle_mpl,
                      facecolor='yellow', alpha=0.2, edgecolor='orange', linewidth=2)
        ax2.add_patch(wedge)
        
        # Get relevant farms
        relevant_farms = manager.get_relevant_farms_for_direction(wind_direction)
        
        # Plot external farms with highlighting
        for i, (farm_name, farm_data) in enumerate(manager.external_farms.items()):
            color = colors[i]
            farm_x = farm_data['turbines']['x'].values
            farm_y = farm_data['turbines']['y'].values
            
            if farm_name in relevant_farms:
                # Active farm - plot prominently
                ax2.scatter(farm_x, farm_y, c=[color], s=80, alpha=0.9,
                           edgecolors='black', linewidth=1.5,
                           label=f"{farm_name} (ACTIVE)")
                
                # Highlight centroid
                fcx, fcy = farm_data['centroid']
                ax2.plot(fcx, fcy, 'o', color=color, markersize=12,
                        markeredgecolor='black', markeredgewidth=2)
            else:
                # Inactive farm - plot faded
                ax2.scatter(farm_x, farm_y, c=[color], s=30, alpha=0.2,
                           label=f"{farm_name} (inactive)")
    
    # Add legends
    ax1.legend(loc='upper left', bbox_to_anchor=(0, 1), fontsize=9, 
               framealpha=0.9, ncol=1)
    ax2.legend(loc='upper left', bbox_to_anchor=(0, 1), fontsize=9,
               framealpha=0.9, ncol=1)
    
    # Add summary text
    if manager.has_external:
        n_active = len(relevant_farms) if 'relevant_farms' in locals() else 0
        n_turbines_active = sum(manager.external_farms[f]['n_turbines'] 
                               for f in relevant_farms) if 'relevant_farms' in locals() else 0
        
        summary = f"Wind Direction: {wind_direction}°\n"
        summary += f"Sector Width: ±{manager.sector_width/2}°\n"
        summary += f"Active Farms: {n_active}/{len(manager.external_farms)}\n"
        summary += f"Active External Turbines: {n_turbines_active}/{len(manager.external_layout)}"
        
        ax2.text(0.98, 0.02, summary, transform=ax2.transAxes,
                verticalalignment='bottom', horizontalalignment='right',
                bbox=dict(boxstyle='round,pad=0.5', facecolor='white', alpha=0.9),
                fontsize=10)
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Farm sector visualization saved to: {output_file}")
    plt.close()


def plot_directional_coverage(manager: EnhancedDirectionalFarmManager, 
                            output_file: str = "directional_coverage.png"):
    """
    Create a polar plot showing which farms are active for each wind direction
    """
    if not manager.has_external:
        print("No external farms to visualize")
        return
        
    fig = plt.figure(figsize=(10, 10))
    ax = fig.add_subplot(111, projection='polar')
    
    # Test all wind directions
    wind_dirs = np.arange(0, 360, 5)
    farm_names = list(manager.external_farms.keys())
    farm_activity = {farm: [] for farm in farm_names}
    
    for wd in wind_dirs:
        active_farms = manager.get_relevant_farms_for_direction(wd)
        for farm in farm_names:
            farm_activity[farm].append(1 if farm in active_farms else 0)
    
    # Convert to radians
    theta = np.radians(wind_dirs)
    
    # Plot each farm's activity
    colors = plt.cm.Set3(np.linspace(0, 1, len(farm_names)))
    
    for i, (farm_name, activity) in enumerate(farm_activity.items()):
        # Create filled area for when farm is active
        r = np.array(activity) * (i + 1)  # Stack farms radially
        ax.fill_between(theta, r - 0.4, r + 0.4, where=np.array(activity) > 0,
                       alpha=0.7, color=colors[i], label=farm_name)
    
    # Customize plot
    ax.set_theta_zero_location('N')
    ax.set_theta_direction(-1)  # Clockwise
    ax.set_ylim(0, len(farm_names) + 1)
    ax.set_yticks(range(1, len(farm_names) + 1))
    ax.set_yticklabels(farm_names)
    ax.set_title(f'External Farm Activity by Wind Direction\n(Sector width: ±{manager.sector_width/2}°)', 
                 fontsize=14, fontweight='bold', pad=20)
    
    # Add grid
    ax.grid(True, alpha=0.3)
    
    # Add legend
    plt.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0), fontsize=10)
    
    plt.tight_layout()
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Directional coverage plot saved to: {output_file}")
    plt.close()


def test_with_real_data():
    """Test visualization with actual wind farm data"""
    print("="*60)
    print("Testing Visualization with Real Data")
    print("="*60)
    
    # Load actual data
    try:
        internal_df = pd.read_csv('Input/Initial.layout.csv')
        external_df = pd.read_csv('Input/External.layout.csv')
        
        # Rename columns if needed
        if 'easting' in internal_df.columns:
            internal_df = internal_df.rename(columns={'easting': 'x', 'northing': 'y'})
        if 'easting' in external_df.columns:
            external_df = external_df.rename(columns={'easting': 'x', 'northing': 'y'})
        
        # Ensure external flags
        internal_df['external'] = False
        external_df['external'] = True
        
        print(f"Loaded {len(internal_df)} internal turbines")
        print(f"Loaded {len(external_df)} external turbines")
        
        # Load boundaries
        try:
            import params as pr
            boundaries = load_boundaries(pr.boundariesFile)
            print(f"Loaded boundaries with {len(boundaries)} points")
        except:
            boundaries = None
            print("Could not load boundaries")
        
        # Create manager with default settings
        manager = EnhancedDirectionalFarmManager(
            internal_df, external_df, 
            max_distance_km=20,  # Reasonable default
            sector_width=90,     # 90° sector (±45°)
            farm_grouping='auto'
        )
        
        # Create visualizations for different wind directions
        for wind_dir in [0, 45, 90, 135, 180, 225, 270, 315]:
            plot_farm_sectors(manager, wind_dir, 
                            f"farm_sectors_{wind_dir}deg.png", 
                            boundaries)
        
        # Create coverage plot
        plot_directional_coverage(manager, "farm_coverage.png")
        
        # Print statistics
        stats = manager.get_filtering_statistics()
        print("\nFiltering Statistics:")
        print(f"  Internal turbines: {stats['n_internal']}")
        print(f"  External turbines: {stats['n_external_total']}")
        print(f"  External farms: {stats['n_farms']}")
        
        print("\nTurbines active by direction:")
        for direction, data in stats['by_direction'].items():
            print(f"  {direction}: {data['n_turbines']} turbines from {data['n_farms']} farms")
        
        return manager
        
    except FileNotFoundError as e:
        print(f"Error: Could not find input files - {e}")
        print("Creating test visualization with sample data instead...")
        
        # Create sample data for testing
        from test_enhanced_farm_filtering import test_basic_functionality
        _, _, manager = test_basic_functionality()
        
        # Create sample visualizations
        for wind_dir in [0, 90, 180, 270]:
            plot_farm_sectors(manager, wind_dir, f"test_sectors_{wind_dir}deg.png")
        
        plot_directional_coverage(manager, "test_coverage.png")
        
        return manager


if __name__ == "__main__":
    # Run visualization tests
    manager = test_with_real_data()
    
    print("\n" + "="*60)
    print("Visualization complete! Check generated PNG files.")
    print("="*60)