# Wind Farm Optimization Files Guide

## 🎯 **MAIN SCRIPTS - Use These:**

### **`opt-pymoo-windrose-freq-ts-external_hybrid.py`** ⭐ **RECOMMENDED**
- **Purpose**: Complete hybrid optimization with internal park focus
- **Algorithm**: 4-step hybrid (GA → DE → PSO)
- **Features**:
  - Reports internal park AEP only (not combined)
  - Vectorized gross/net AEP calculations
  - Wake loss analysis before/after optimization
  - Smart external turbine filtering
  - All recent improvements and bug fixes
- **Use When**: You want the most advanced optimization with internal park focus

### **`opt-pymoo-windrose-freq-ts-external_smart.py`** ✅ **ALTERNATIVE**
- **Purpose**: Smart directional filtering optimization
- **Algorithm**: NSGA2 with directional filtering
- **Features**:
  - Smart external turbine filtering based on wind direction
  - Single algorithm approach (simpler, faster)
  - Fixed undefined variable errors
- **Use When**: You want simpler optimization with smart filtering

---

## 🔧 **SUPPORT LIBRARIES - Don't Run Directly:**

### **`hybrid_layout_optimization_pymoo_external.py`**
- **Purpose**: Core optimization framework class
- **Contains**: HybridLayoutOptimizationPymoo class
- **Used By**: Main hybrid script

### **`hybrid_algorithms_external.py`**
- **Purpose**: Custom algorithm implementations
- **Contains**: HybridGA_External, HybridDE_External, HybridPSO_External
- **Used By**: Main hybrid script

### **`hybrid_problem_external.py`**
- **Purpose**: Problem definition for optimization
- **Contains**: HybridLayoutOptimizationProblem class
- **Used By**: Main hybrid script

---

## 🧪 **TESTING FILES:**

### **`hybrid_test_runner.py`** & **`hybrid_test_runner_simple.py`**
- **Purpose**: Test suites for validation
- **Use**: Run to verify hybrid components work correctly

---

## 📦 **LEGACY FILES - For Reference Only:**

### **`opt-pymoo-windrose-freq-ts-external.py`**
- **Status**: Original version (superseded)
- **Note**: Use hybrid version instead

### **`opt-pymoo-windrose-freq-ts-external_corrected.py`**
- **Status**: Intermediate corrected version
- **Note**: Use hybrid version instead

---

## 🚀 **Quick Start:**

1. **For best results**: Run `python3 opt-pymoo-windrose-freq-ts-external_hybrid.py`
2. **For simpler approach**: Run `python3 opt-pymoo-windrose-freq-ts-external_smart.py`
3. **For testing**: Run `python3 hybrid_test_runner_simple.py`

---

## 📊 **What Each Script Outputs:**

### Hybrid Script Outputs:
- Internal park AEP (gross/net)
- Wake loss analysis
- Optimization progress plots
- Layout comparison visualizations
- Detailed performance metrics

### Smart Script Outputs:
- Combined AEP optimization
- Directional filtering results  
- NSGA2 optimization plots
- Performance summaries

---

## ⚙️ **Configuration:**

All scripts use:
- `params.py` - Main parameters
- `optimizer_params.py` - Optimization settings
- `utilities.py` - Helper functions

---

## 🔄 **Recent Improvements:**

✅ Fixed undefined 'layout' variable error in smart script
✅ Enhanced hybrid script with internal AEP focus
✅ Added vectorized gross/net calculations
✅ Fixed DE algorithm initialization issues
✅ Added comprehensive wake loss analysis
✅ Implemented smart external turbine filtering