#!/usr/bin/env python3
"""
Script to plot and compare site polygon geometry constraints
Visualizes boundaries, generated points, and shape file coordinates
"""

import sys
import os

# Add parent directory to path for utilities import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utilities import plot_site_polygon_comparison

def main():
    """
    Main function to generate site polygon comparison plots
    """
    print("=" * 80)
    print("Site Polygon Geometry Constraints Comparison")
    print("=" * 80)
    
    # Define input and output directories
    input_dir = 'Input/'
    output_dir = 'Input/'  # Results will be placed in Input/ as requested
    
    # Check if input directory exists
    if not os.path.exists(input_dir):
        print(f"Error: Input directory '{input_dir}' not found!")
        print("Please ensure you are running this script from the optimization directory.")
        return
    
    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)
    
    print(f"\nInput directory: {input_dir}")
    print(f"Output directory: {output_dir}")
    print("\nChecking for required files...")
    
    # Check for required files
    required_files = [
        'Boundaries.csv',
        'Boundaries-genPoints.csv',
        'shp/shp_coord_total.csv'
    ]
    
    files_found = []
    files_missing = []
    
    for file in required_files:
        file_path = os.path.join(input_dir, file)
        if os.path.exists(file_path):
            files_found.append(file)
            print(f"  ✓ Found: {file}")
        else:
            files_missing.append(file)
            print(f"  ✗ Missing: {file}")
    
    if files_missing:
        print(f"\nWarning: {len(files_missing)} files are missing. The plot will show available data only.")
    
    # Generate the comparison plots
    print("\nGenerating site polygon comparison plots...")
    try:
        summary = plot_site_polygon_comparison(input_dir=input_dir, output_dir=output_dir)
        
        print("\n" + "=" * 80)
        print("Summary of plotted data:")
        print("=" * 80)
        
        for dataset, info in summary.items():
            print(f"\n{dataset.replace('_', ' ').title()}:")
            print(f"  - Number of points: {info['count']}")
            print(f"  - X range: [{info['x_range'][0]:.1f}, {info['x_range'][1]:.1f}] m")
            print(f"  - Y range: [{info['y_range'][0]:.1f}, {info['y_range'][1]:.1f}] m")
            print(f"  - Area coverage: {(info['x_range'][1] - info['x_range'][0]) * (info['y_range'][1] - info['y_range'][0]) / 1e6:.2f} km²")
        
        print("\n" + "=" * 80)
        print("Output files generated:")
        print("=" * 80)
        print(f"  - {os.path.join(output_dir, 'site_polygon_comparison.png')} - Combined comparison plot")
        print(f"  - {os.path.join(output_dir, 'site_polygon_detailed.png')} - Detailed view")
        print(f"  - {os.path.join(output_dir, 'site_polygon_overview.png')} - Overview with statistics")
        print(f"  - {os.path.join(output_dir, 'site_polygon_comparison_summary.csv')} - Numerical summary")
        
    except Exception as e:
        print(f"\nError generating plots: {e}")
        import traceback
        traceback.print_exc()
        return
    
    print("\n" + "=" * 80)
    print("Site polygon comparison completed successfully!")
    print("=" * 80)

if __name__ == "__main__":
    main()