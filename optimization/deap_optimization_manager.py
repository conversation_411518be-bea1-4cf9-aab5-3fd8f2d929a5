#!/usr/bin/env python3
"""
DEAP Optimization Manager - Flexible Multi-Algorithm Wind Farm Layout Optimization
Provides unified interface for all DEAP optimization algorithms with consistent:
- Normalized coordinates [0,1] 
- Proper initialization (x0)
- Consistent constraint handling
- Easy algorithm switching

Supported Algorithms:
- GA (Genetic Algorithm)
- DE (Differential Evolution) 
- PSO (Particle Swarm Optimization)
- CMA-ES (Covariance Matrix Adaptation Evolution Strategy)
- NSGA-II (Non-dominated Sorting GA)
- NSGA-III (NSGA-III with reference points)
- SPEA2 (Strength Pareto Evolutionary Algorithm)
- ES (Evolution Strategies)
- EDA (Estimation of Distribution Algorithm)
- Multi-Swarm PSO
- Cooperative Coevolution
"""

import numpy as np
import time
import random
import copy
import warnings
import threading
from typing import Dict, List, Any, Optional, Union, Tuple
from concurrent.futures import ProcessPoolExecutor, as_completed, TimeoutError
from multiprocessing import cpu_count
from dataclasses import dataclass
from pathlib import Path

# DEAP imports
from deap import algorithms, base, creator, tools
from deap.tools import Logbook, Statistics, HallOfFame
import deap.benchmarks

# Optional imports
try:
    from scoop import futures
    HAS_SCOOP = True
except ImportError:
    HAS_SCOOP = False



# Suppress warnings
warnings.filterwarnings('ignore')


@dataclass
class OptimizationConfig:
    """Configuration for DEAP optimization algorithms"""
    algorithm: str = 'GA'
    variant: str = 'standard'
    pop_size: int = 32
    n_gen: int = 10
    n_workers: int = None
    timeout_per_eval: int = 300
    use_scoop: bool = False
    verbose: bool = True
    
    # Early termination (PyMoo-style convergence)
    enable_early_termination: bool = True
    convergence_tol: float = 1e-5  # Match PyMoo's tol=1e-5
    patience: int = 5              # Match PyMoo's n_skip=5
    
    # Operator configuration
    selection: str = 'tournament'
    crossover: str = 'blend'
    mutation: str = 'gaussian'
    
    # Algorithm-specific parameters
    tournament_size: int = 2
    crossover_prob: float = 0.9
    mutation_prob: float = 0.1
    eta_c: float = 10.0  # Crossover distribution index
    eta_m: float = 50.0  # Mutation distribution index
    
    # DE parameters
    de_F: float = 0.5  # Differential weight
    de_CR: float = 0.7  # Crossover rate
    
    # PSO parameters
    pso_w: float = 0.5  # Inertia weight
    pso_c1: float = 2.0  # Cognitive coefficient
    pso_c2: float = 2.0  # Social coefficient
    pso_smin: float = -0.1  # Minimum velocity
    pso_smax: float = 0.1   # Maximum velocity
    
    # CMA-ES parameters
    cmaes_sigma: float = 0.1  # Initial step size
    cmaes_lambda: int = None  # Population size (auto if None)
    
    # NSGA-III parameters
    nsga3_ref_points: int = 12  # Number of reference points per objective
    
    # Multi-objective parameters
    crowding_factor: float = 2.0
    
    # Monitoring parameters
    hall_of_fame_size: int = 10
    track_statistics: List[str] = None
    
    def __post_init__(self):
        if self.n_workers is None:
            # Try to use params.py n_workers, fallback to CPU count
            try:
                import params as pr
                self.n_workers = getattr(pr, 'n_workers', min(cpu_count(), 32))
            except ImportError:
                self.n_workers = min(cpu_count(), 32)
        if self.track_statistics is None:
            self.track_statistics = ['min', 'max', 'avg', 'std']


class DEAPProblemWrapper:
    """Unified problem wrapper ensuring consistency across all DEAP algorithms"""
    
    def __init__(self, fi, freq, layout, boundaries, baseline_aep=None, turbines_weights=None):
        """
        Initialize problem wrapper with PyMoo-consistent interface
        
        Args:
            fi: FLORIS interface
            freq: Wind frequency data
            layout: Turbine layout data
            boundaries: Farm boundaries
            baseline_aep: Baseline AEP for normalization (auto-calculated if None)
            turbines_weights: Array of weights (1=internal/optimizable, 0=external/fixed)
        """
        self.fi = fi
        self.freq = freq
        self.layout = layout
        self.boundaries = boundaries
        self.n_turbines = len(layout)
        
        # Handle turbine weights for external optimization (like PyMoo)
        self.turbines_weights = turbines_weights
        if turbines_weights is not None and len(turbines_weights) > 0:
            weights = np.array(turbines_weights)
            self.turbs_to_opt = np.where(weights > 0)[0].tolist()      # Internal turbines (weight=1)
            self.turbs_extern = np.where(weights == 0)[0].tolist()    # External turbines (weight=0)
            self.numberOfInternalWTGs = len(self.turbs_to_opt)
            self.numberOfExternalWTGs = len(self.turbs_extern)
            
            # Only internal turbines are optimization variables
            self.n_var = 2 * self.numberOfInternalWTGs  # x, y coordinates for internal turbines only
            self.n_constr = 2 * self.numberOfInternalWTGs  # Constraints for internal turbines only
            
            # Store original external turbine positions (fixed)
            self.external_x_orig = layout['x'].iloc[self.turbs_extern].values if len(self.turbs_extern) > 0 else np.array([])
            self.external_y_orig = layout['y'].iloc[self.turbs_extern].values if len(self.turbs_extern) > 0 else np.array([])
            
            print(f"   Turbine weights detected: {self.numberOfInternalWTGs} internal, {self.numberOfExternalWTGs} external")
        else:
            # No weights - all turbines are optimization variables (original behavior)
            self.turbs_to_opt = list(range(self.n_turbines))
            self.turbs_extern = []
            self.numberOfInternalWTGs = self.n_turbines
            self.numberOfExternalWTGs = 0
            self.n_var = 2 * self.n_turbines  # x, y coordinates (normalized [0,1])
            self.n_constr = 2 * self.n_turbines  # Space + boundary constraints (matching PyMoo)
            self.external_x_orig = np.array([])
            self.external_y_orig = np.array([])
            
        self.n_obj = 1
        
        # Setup boundary limits for normalization
        import pandas as pd
        if isinstance(boundaries, str):
            boundaries_df = pd.read_csv(boundaries)
        else:
            boundaries_df = pd.DataFrame(boundaries, columns=['X', 'Y'])
            
        self.xmin = boundaries_df['X'].min() if 'X' in boundaries_df.columns else boundaries_df['x'].min()
        self.xmax = boundaries_df['X'].max() if 'X' in boundaries_df.columns else boundaries_df['x'].max()
        self.ymin = boundaries_df['Y'].min() if 'Y' in boundaries_df.columns else boundaries_df['y'].min()
        self.ymax = boundaries_df['Y'].max() if 'Y' in boundaries_df.columns else boundaries_df['y'].max()
        
        # Calculate baseline AEP for objective normalization
        if baseline_aep is None:
            self.fi.reinitialize(layout=(layout['x'], layout['y']))
            self.baseline_aep = self.fi.get_farm_AEP(freq=self.freq) / 1E9  # GWh
        else:
            self.baseline_aep = baseline_aep
            
        if self.numberOfExternalWTGs > 0:
            print(f"   Problem initialized: {self.n_turbines} total turbines ({self.numberOfInternalWTGs} internal, {self.numberOfExternalWTGs} external), baseline AEP: {self.baseline_aep:.3f} GWh")
        else:
            print(f"   Problem initialized: {self.n_turbines} turbines, baseline AEP: {self.baseline_aep:.3f} GWh")
        
        # Create initial solution (x0) in normalized coordinates - only for internal turbines when weights are used
        if self.turbines_weights is not None and len(self.turbines_weights) > 0:
            # Only internal turbines (turbs_to_opt) are optimization variables
            internal_x = layout['x'].iloc[self.turbs_to_opt].values
            internal_y = layout['y'].iloc[self.turbs_to_opt].values
            self.x0 = np.concatenate([
                self._norm(internal_x, self.xmin, self.xmax),
                self._norm(internal_y, self.ymin, self.ymax)
            ])
        else:
            # No weights - all turbines are optimization variables (original behavior)
            self.x0 = np.concatenate([
                self._norm(layout['x'].values, self.xmin, self.xmax),
                self._norm(layout['y'].values, self.ymin, self.ymax)
            ])
        
        # Pre-compute boundary polygon/line for efficiency (matching PyMoo)
        self._prepare_boundaries()
        
    def _prepare_boundaries(self):
        """Pre-process boundaries for efficient distance calculations - matching PyMoo"""
        from shapely.geometry import Polygon
        try:
            if isinstance(self.boundaries, str):
                import pandas as pd
                boundaries_df = pd.read_csv(self.boundaries)
                if 'X' in boundaries_df.columns:
                    boundary_points = list(zip(boundaries_df['X'], boundaries_df['Y']))
                else:
                    boundary_points = list(zip(boundaries_df['x'], boundaries_df['y']))
            else:
                boundary_points = self.boundaries
                
            self._boundary_polygon = Polygon(boundary_points)
            self._boundary_line = self._boundary_polygon.boundary
        except Exception as e:
            raise ValueError(f"Failed to convert boundaries to Polygon: {e}")
        
    def _norm(self, val, x1, x2):
        """Normalize values to [0,1] - matching PyMoo base class"""
        return (np.array(val) - x1) / (x2 - x1)
        
    def _unnorm(self, val, x1, x2):
        """Unnormalize values from [0,1] - matching PyMoo base class"""
        return np.array(val) * (x2 - x1) + x1
        
    def _space_constraint(self, x_in):
        """Minimum distance constraint matching PyMoo implementation exactly"""
        import params as pr
        from scipy.spatial.distance import cdist
        
        # Use number of internal turbines when weights are applied
        n_turbines_constraint = self.numberOfInternalWTGs if self.turbines_weights is not None else self.n_turbines
        
        x = [
            self._unnorm(valx, self.xmin, self.xmax)
            for valx in x_in[0: n_turbines_constraint]
        ]
        y = [
            self._unnorm(valy, self.ymin, self.ymax)
            for valy in x_in[n_turbines_constraint: 2 * n_turbines_constraint]
        ]
    
        # Calculate distances between turbines
        locs = np.vstack((x, y)).T
        distances = cdist(locs, locs)
        np.fill_diagonal(distances, np.inf)  # Set diagonal elements to infinity
    
        # Check if any distance violates the minimum distance constraint
        violated_distances = distances < pr.min_dist
        g = np.sum(violated_distances, axis=1)
    
        return g

    def _distance_from_boundaries(self, x_in):
        """Boundary constraint matching PyMoo implementation exactly"""
        from shapely.geometry import Point
        
        # Use number of internal turbines when weights are applied
        n_turbines_constraint = self.numberOfInternalWTGs if self.turbines_weights is not None else self.n_turbines
        
        x = [
            self._unnorm(valx, self.xmin, self.xmax)
            for valx in x_in[0 : n_turbines_constraint]
        ]
        y = [
            self._unnorm(valy, self.ymin, self.ymax)
            for valy in x_in[n_turbines_constraint : 2 * n_turbines_constraint]
        ]
        boundary_con = np.zeros(n_turbines_constraint)
        for i in range(n_turbines_constraint):
            loc = Point(x[i], y[i])
            boundary_con[i] = loc.distance(self._boundary_line)
            if self._boundary_polygon.contains(loc) is True:
                boundary_con[i] *=  -1.0
            else:
                boundary_con[i] *=   1.0

        return boundary_con
        
    def evaluate(self, X):
        """
        Evaluate population for DEAP with normalized coordinates [0,1] - matching PyMoo exactly
        
        Args:
            X: Population or individual in normalized coordinates [0,1]
            
        Returns:
            Dictionary with 'F' (objectives) and 'G' (constraints)
        """
        # Handle both single individual and population
        if X.ndim == 1:
            X = X.reshape(1, -1)
            single_eval = True
        else:
            single_eval = False
            
        n_samples = X.shape[0]
        F = np.zeros((n_samples, 1))
        G = np.zeros((n_samples, self.n_constr))
        
        for i in range(n_samples):
            if self.turbines_weights is not None and len(self.turbines_weights) > 0:
                # With turbine weights: reconstruct full layout (internal + external)
                # Extract normalized coordinates for internal turbines only
                internal_x_norm = X[i, :self.numberOfInternalWTGs]
                internal_y_norm = X[i, self.numberOfInternalWTGs:]
                
                # Denormalize internal turbine coordinates
                internal_x_real = self._unnorm(internal_x_norm, self.xmin, self.xmax)
                internal_y_real = self._unnorm(internal_y_norm, self.ymin, self.ymax)
                
                # Reconstruct full layout: optimized internal + fixed external (like PyMoo)
                full_x = np.zeros(self.n_turbines)
                full_y = np.zeros(self.n_turbines)
                
                # Place optimized internal turbines
                for j, turb_idx in enumerate(self.turbs_to_opt):
                    full_x[turb_idx] = internal_x_real[j]
                    full_y[turb_idx] = internal_y_real[j]
                
                # Keep external turbines at fixed positions
                for j, turb_idx in enumerate(self.turbs_extern):
                    full_x[turb_idx] = self.external_x_orig[j]
                    full_y[turb_idx] = self.external_y_orig[j]
                
                # Calculate AEP using full layout (internal + external)
                try:
                    self.fi.reinitialize(layout=(full_x, full_y))
                    aep = self.fi.get_farm_AEP(freq=self.freq) / 1E9  # GWh
                    # Only count internal turbine AEP in objective (like PyMoo)
                    # For now, use full AEP as proxy - could be enhanced to separate internal/external contributions
                    objective = aep / self.baseline_aep
                    F[i, 0] = objective
                except Exception as e:
                    print(f"Warning: AEP calculation failed: {e}")
                    F[i, 0] = 0.0  # Penalty for failed evaluations
            else:
                # No weights - all turbines are optimization variables (original behavior)
                # Extract normalized coordinates [0,1]
                x_norm = X[i, :self.n_turbines]
                y_norm = X[i, self.n_turbines:]
                
                # Denormalize coordinates
                x_real = self._unnorm(x_norm, self.xmin, self.xmax)
                y_real = self._unnorm(y_norm, self.ymin, self.ymax)
                
                # Calculate AEP and normalize by baseline (corrected for maximization)
                try:
                    self.fi.reinitialize(layout=(x_real, y_real))
                    aep = self.fi.get_farm_AEP(freq=self.freq) / 1E9  # GWh
                    # Objective = AEP / initial_AEP (positive for maximization)
                    # This ensures higher AEP gives higher fitness (better for maximization)
                    objective = aep / self.baseline_aep
                    F[i, 0] = objective
                except Exception as e:
                    print(f"Warning: AEP calculation failed: {e}")
                    F[i, 0] = 0.0  # Penalty for failed evaluations
            
            # Use exact PyMoo constraint functions (handles both cases automatically)
            if self.turbines_weights is not None and len(self.turbines_weights) > 0:
                G[i, :self.numberOfInternalWTGs] = self._space_constraint(X[i])
                G[i, self.numberOfInternalWTGs:2 * self.numberOfInternalWTGs] = self._distance_from_boundaries(X[i])
            else:
                G[i, :self.n_turbines] = self._space_constraint(X[i])
                G[i, self.n_turbines:2 * self.n_turbines] = self._distance_from_boundaries(X[i])
        
        result = {"F": F, "G": G}
        
        # Return single values for single evaluation
        if single_eval:
            result["F"] = F[0]
            result["G"] = G[0]
            
        return result
    
    def safe_objective_function(self, x):
        """Safe wrapper for objective function evaluation - mirroring PyMoo pattern"""
        try:
            return self._objective_function_only(x)
        except Exception as e:
            print(f"Warning: AEP calculation failed: {e}")
            return -1e-6  # Return penalty value like PyMoo
    
    def safe_combined_evaluation(self, x):
        """Safe wrapper for combined F+G evaluation - exactly like PyMoo _evaluate method"""
        try:
            # Single combined evaluation like PyMoo (no double computation)
            result = self.evaluate(x)
            return {
                'F': result['F'],
                'G': result['G']
            }
        except Exception as e:
            print(f"Warning: Combined evaluation failed: {e}")
            # Return penalty values for both F and G
            return {
                'F': -1e-6,
                'G': np.ones(self.n_constr)  # Violate all constraints
            }
    
    def _objective_function_only(self, x):
        """Evaluate only objective function (AEP) - mirroring PyMoo _obj_func"""
        # Normalize coordinates [0,1] -> real coordinates
        x_norm = x[:self.n_turbines]
        y_norm = x[self.n_turbines:]
        
        # Denormalize to real coordinates
        x_real = self._unnorm(x_norm, self.xmin, self.xmax)
        y_real = self._unnorm(y_norm, self.ymin, self.ymax)
        
        # Update layout in FLORIS
        self.fi.reinitialize(layout=(x_real, y_real))
        
        # Calculate wake effects  
        self.fi.calculate_wake()
        
        # Get AEP and normalize
        if self.freq is not None:
            aep = self.fi.get_farm_AEP(self.freq) / 1E9  # GWh
        else:
            powers = self.fi.get_turbine_powers()
            aep = np.sum(powers) / 1e9  # GW
        
        # Normalize by baseline AEP (same as PyMoo)
        normalized_aep = aep / self.baseline_aep
        
        return normalized_aep


class DEAPOptimizationManager:
    """
    Flexible DEAP Optimization Manager for Wind Farm Layout Optimization
    Provides unified interface for all DEAP algorithms with consistent handling
    """
    
    # Supported algorithms registry
    SUPPORTED_ALGORITHMS = {
        'GA': 'Genetic Algorithm',
        'DE': 'Differential Evolution',
        'PSO': 'Particle Swarm Optimization', 
        'CMA-ES': 'Covariance Matrix Adaptation Evolution Strategy',
        'NSGA-II': 'Non-dominated Sorting Genetic Algorithm II',
        'NSGA-III': 'Non-dominated Sorting Genetic Algorithm III',
        'SPEA2': 'Strength Pareto Evolutionary Algorithm 2',
        'ES': 'Evolution Strategies',
        'EDA': 'Estimation of Distribution Algorithm',
        'MULTI-PSO': 'Multi-Swarm Particle Swarm Optimization',
        'COOP-COEV': 'Cooperative Coevolution'
    }
    
    def __init__(self, problem: DEAPProblemWrapper, config: OptimizationConfig = None):
        """
        Initialize DEAP Optimization Manager
        
        Args:
            problem: Problem wrapper instance
            config: Optimization configuration (uses defaults if None)
        """
        self.problem = problem
        self.config = config or OptimizationConfig()
        
        # Initialize parallel resources - persistent pool like PyMoo
        self.pool = None
        self.parallel_backend = "sequential"
        self._persistent_pool = None  # Reusable ProcessPoolExecutor
        self._thread_local = threading.local()
        self._floris_lock = threading.Lock()
        
        # Initialize DEAP components
        self._setup_deap_types()
        self._setup_toolbox()
        self._setup_statistics()
        
        # Results storage
        self.results = {}
        self.comparison_results = {}
        
        # Monitoring
        self.logbook = Logbook()
        self.hall_of_fame = HallOfFame(self.config.hall_of_fame_size)
        self.eval_count = 0
        self.start_time = None
        
        # Early termination tracking (PyMoo-style)
        self.best_fitness_history = []
        self.generations_without_improvement = 0
        
        if self.config.verbose:
            print(f"🔬 DEAP Optimization Manager initialized:")
            print(f"   Algorithm: {self.config.algorithm}")
            print(f"   Problem: {self.problem.n_var} vars, {self.problem.n_obj} obj, {self.problem.n_constr} constr")
            print(f"   Population: {self.config.pop_size}, Generations: {self.config.n_gen}")
            
    def _setup_deap_types(self):
        """Setup DEAP creator types for individuals and fitness"""
        # Clear any existing types
        if hasattr(creator, "FitnessMax"):
            del creator.FitnessMax
        if hasattr(creator, "Individual"):
            del creator.Individual
            
        # Create fitness class (maximization for AEP)
        if self.problem.n_obj == 1:
            creator.create("FitnessMax", base.Fitness, weights=(1.0,))
        else:
            # Multi-objective case
            creator.create("FitnessMax", base.Fitness, weights=tuple([1.0] * self.problem.n_obj))
            
        # Create individual class
        creator.create("Individual", list, fitness=creator.FitnessMax)
        
    def _setup_toolbox(self):
        """Setup DEAP toolbox with operators and parallel evaluation"""
        self.toolbox = base.Toolbox()
        
        # Attribute and individual generation (normalized [0,1])
        self.toolbox.register("attr_float", random.random)
        self.toolbox.register("individual", tools.initRepeat, creator.Individual,
                             self.toolbox.attr_float, n=self.problem.n_var)
        self.toolbox.register("population", tools.initRepeat, list, self.toolbox.individual)
        
        # Evaluation function
        self.toolbox.register("evaluate", self._evaluate_individual)
        
        # Configure parallel evaluation
        self._setup_parallel_evaluation()
        
        # Configure operators based on algorithm
        self._configure_operators()
        
    def _setup_parallel_evaluation(self):
        """Setup parallel evaluation backend"""
        try:
            if self.config.use_scoop and HAS_SCOOP:
                # Use SCOOP for distributed computing
                self.toolbox.register("map", futures.map)
                self.parallel_backend = "scoop"
                if self.config.verbose:
                    print(f"   🔀 Parallel backend: SCOOP")
            elif self.config.n_workers and self.config.n_workers > 1:
                # For FLORIS problems, use ProcessPoolExecutor instead of Pool for better pickle support
                self.parallel_backend = "multiprocessing"
                if self.config.verbose:
                    if hasattr(self.problem, 'fi'):
                        print(f"   🔀 Parallel backend: multiprocessing ({self.config.n_workers} workers) - FLORIS sequential")
                    else:
                        print(f"   🔀 Parallel backend: multiprocessing ({self.config.n_workers} workers)")
            else:
                # Sequential execution (fallback)
                self.parallel_backend = "sequential"
                if self.config.verbose:
                    print(f"   🔀 Parallel backend: sequential")
        except Exception as e:
            # Fallback to sequential if parallel setup fails
            self.parallel_backend = "sequential"
            if self.config.verbose:
                print(f"   ⚠️ Parallel setup failed, using sequential: {e}")
    
    def _configure_operators(self):
        """Configure genetic operators based on configuration"""
        # Selection operators
        if self.config.selection == 'tournament':
            self.toolbox.register("select", tools.selTournament, tournsize=self.config.tournament_size)
        elif self.config.selection == 'roulette':
            self.toolbox.register("select", tools.selRoulette)
        elif self.config.selection == 'nsga2':
            self.toolbox.register("select", tools.selNSGA2)
        elif self.config.selection == 'nsga3':
            ref_points = tools.uniform_reference_points(self.problem.n_obj, self.config.nsga3_ref_points)
            self.toolbox.register("select", tools.selNSGA3, ref_points=ref_points)
        elif self.config.selection == 'spea2':
            self.toolbox.register("select", tools.selSPEA2)
        else:
            # Default to tournament
            self.toolbox.register("select", tools.selTournament, tournsize=self.config.tournament_size)
            
        # Crossover operators
        if self.config.crossover == 'blend':
            self.toolbox.register("mate", tools.cxBlend, alpha=0.5)
        elif self.config.crossover == 'sbx':
            self.toolbox.register("mate", tools.cxSimulatedBinaryBounded, 
                                 low=0.0, up=1.0, eta=self.config.eta_c)
        elif self.config.crossover == 'uniform':
            self.toolbox.register("mate", tools.cxUniform, indpb=0.5)
        elif self.config.crossover == 'onepoint':
            self.toolbox.register("mate", tools.cxOnePoint)
        elif self.config.crossover == 'twopoint':
            self.toolbox.register("mate", tools.cxTwoPoint)
        else:
            # Default to blend
            self.toolbox.register("mate", tools.cxBlend, alpha=0.5)
            
        # Mutation operators
        if self.config.mutation == 'gaussian':
            self.toolbox.register("mutate", tools.mutGaussian, mu=0, sigma=0.1, indpb=0.1)
        elif self.config.mutation == 'polynomial':
            self.toolbox.register("mutate", tools.mutPolynomialBounded,
                                 low=0.0, up=1.0, eta=self.config.eta_m, indpb=0.1)
        elif self.config.mutation == 'uniform':
            self.toolbox.register("mutate", tools.mutUniformInt, low=0, up=1, indpb=0.1)
        else:
            # Default to gaussian
            self.toolbox.register("mutate", tools.mutGaussian, mu=0, sigma=0.1, indpb=0.1)
            
    def _setup_statistics(self):
        """Setup statistics tracking"""
        self.stats = Statistics(lambda ind: ind.fitness.values)
        
        if 'min' in self.config.track_statistics:
            self.stats.register("min", np.min)
        if 'max' in self.config.track_statistics:
            self.stats.register("max", np.max)
        if 'avg' in self.config.track_statistics:
            self.stats.register("avg", np.mean)
        if 'std' in self.config.track_statistics:
            self.stats.register("std", np.std)
            
    def _evaluate_individual(self, individual):
        """Evaluate single individual using problem wrapper"""
        # Use thread-safe evaluation for FLORIS problems
        if hasattr(self.problem, 'fi') and self.parallel_backend == "multiprocessing":
            result = self._evaluate_individual_thread_safe(np.array(individual))
        else:
            result = self.problem.evaluate(np.array(individual))
            
        fitness = result["F"]
        constraints = result["G"]
        
        # Handle constraints (positive values indicate violation)
        constraint_violation = np.sum(np.maximum(0, constraints))
        
        # Penalize for constraint violations (feasibility-preserving approach)
        if constraint_violation > 0:
            # Use logarithmic penalty to handle large violations gracefully
            penalty = 0.1 * np.log(1 + constraint_violation)  # Gentle logarithmic penalty
            penalty = min(penalty, fitness * 0.1)  # Cap penalty at 10% of objective
            fitness = fitness - penalty
            
        self.eval_count += 1
        return (fitness,) if isinstance(fitness, (int, float)) else tuple(fitness)
    
    def _get_thread_local_problem(self):
        """Get or create thread-local problem instance"""
        if not hasattr(self._thread_local, 'problem'):
            # Create a copy of the problem for this thread
            try:
                import copy
                self._thread_local.problem = copy.deepcopy(self.problem)
            except Exception:
                # If deepcopy fails, use the original (sequential fallback)
                self._thread_local.problem = self.problem
        return self._thread_local.problem
    
    def _evaluate_individual_thread_safe(self, individual_array):
        """Thread-safe evaluation for FLORIS problems using locks"""
        try:
            # Use lock to serialize FLORIS access
            with self._floris_lock:
                return self.problem.evaluate(individual_array)
        except Exception:
            # Fallback to original problem (sequential)
            return self.problem.evaluate(individual_array)
    
    def _evaluate_population_parallel(self, population):
        """Evaluate population with proper multiprocessing support for FLORIS"""
        if self.parallel_backend == "multiprocessing":
            # Use ProcessPoolExecutor for FLORIS problems due to pickle limitations
            return self._evaluate_with_process_pool(population)
        elif self.parallel_backend == "scoop":
            # Use SCOOP for distributed computing
            fitnesses = self.toolbox.map(self.toolbox.evaluate, population)
            self._add_constraint_info_to_population(population)
            return fitnesses
        else:
            # Sequential execution
            fitnesses = list(map(self.toolbox.evaluate, population))
            self._add_constraint_info_to_population(population)
            return fitnesses
    
    def _add_constraint_info_to_population(self, population):
        """Add constraint violation information to individuals for constraint-handling selection"""
        # Batch constraint evaluation for efficiency
        X = np.array([list(ind) for ind in population])
        
        try:
            # Evaluate constraints for entire population at once
            result = self.problem.evaluate(X)
            constraint_violations = np.sum(np.maximum(0, result['G']), axis=1)
            
            # Store constraint info in individuals
            for i, individual in enumerate(population):
                individual.constraint_violation = constraint_violations[i]
                individual.is_feasible = constraint_violations[i] <= 1e-6
                
        except Exception as e:
            # Fallback to individual evaluation if batch fails
            for individual in population:
                try:
                    result = self.problem.evaluate(np.array(individual))
                    constraint_violation = np.sum(np.maximum(0, result['G']))
                    individual.constraint_violation = constraint_violation
                    individual.is_feasible = constraint_violation <= 1e-6
                except:
                    individual.constraint_violation = 1e6
                    individual.is_feasible = False
    
    def _repair_infeasible_individual(self, individual):
        """Repair an infeasible individual by moving it back inside boundaries"""
        try:
            # Convert to numpy array for easier manipulation
            x = np.array(individual)
            n_vars = len(x) // 2
            x_coords = x[:n_vars]
            y_coords = x[n_vars:]
            
            # Denormalize to real coordinates
            real_x = self.problem._unnorm(x_coords, self.problem.xmin, self.problem.xmax)
            real_y = self.problem._unnorm(y_coords, self.problem.ymin, self.problem.ymax)
            
            # Check and repair boundary violations
            from shapely.geometry import Point
            repaired_x = real_x.copy()
            repaired_y = real_y.copy()
            
            for i in range(n_vars):
                point = Point(real_x[i], real_y[i])
                
                # If point is outside boundary, project it back inside
                if not self.problem._boundary_polygon.contains(point):
                    # Find closest point on boundary
                    boundary_point = self.problem._boundary_line.interpolate(
                        self.problem._boundary_line.project(point)
                    )
                    
                    # Move slightly inside (1% of distance to center)
                    center_x = (self.problem.xmin + self.problem.xmax) / 2
                    center_y = (self.problem.ymin + self.problem.ymax) / 2
                    
                    # Vector from boundary point to center
                    dx = center_x - boundary_point.x
                    dy = center_y - boundary_point.y
                    
                    # Move 1% towards center
                    repaired_x[i] = boundary_point.x + 0.01 * dx
                    repaired_y[i] = boundary_point.y + 0.01 * dy
            
            # Normalize back to [0,1]
            repaired_x_norm = self.problem._norm(repaired_x, self.problem.xmin, self.problem.xmax)
            repaired_y_norm = self.problem._norm(repaired_y, self.problem.ymin, self.problem.ymax)
            
            # Update individual
            repaired_coords = np.concatenate([repaired_x_norm, repaired_y_norm])
            for i in range(len(individual)):
                individual[i] = repaired_coords[i]
                
            # Mark as repaired
            individual.was_repaired = True
            
        except Exception as e:
            # If repair fails, mark for debugging
            individual.repair_failed = True
    
    def _constraint_tournament_selection(self, individuals, k, tournsize=3):
        """Tournament selection with constraint handling - mimics PyMoo's constraint handling"""
        def constraint_tournament(ind1, ind2):
            """Compare two individuals using constraint handling rules"""
            # Rule 1: Feasible solution beats infeasible solution
            if hasattr(ind1, 'is_feasible') and hasattr(ind2, 'is_feasible'):
                if ind1.is_feasible and not ind2.is_feasible:
                    return ind1
                elif ind2.is_feasible and not ind1.is_feasible:
                    return ind2
                
                # Rule 2: Both feasible - compare fitness
                if ind1.is_feasible and ind2.is_feasible:
                    return ind1 if ind1.fitness.values[0] > ind2.fitness.values[0] else ind2
                
                # Rule 3: Both infeasible - compare constraint violations
                if not ind1.is_feasible and not ind2.is_feasible:
                    cv1 = getattr(ind1, 'constraint_violation', 1e6)
                    cv2 = getattr(ind2, 'constraint_violation', 1e6)
                    return ind1 if cv1 < cv2 else ind2
            
            # Fallback to fitness comparison
            return ind1 if ind1.fitness.values[0] > ind2.fitness.values[0] else ind2
        
        # Perform tournament selection with constraint handling
        chosen = []
        for _ in range(k):
            tournament = random.sample(individuals, min(len(individuals), tournsize))
            winner = tournament[0]
            for contestant in tournament[1:]:
                winner = constraint_tournament(winner, contestant)
            chosen.append(winner)
        
        return chosen
    
    def _get_or_create_persistent_pool(self):
        """Get or create persistent ProcessPoolExecutor (PyMoo pattern)"""
        if self._persistent_pool is None:
            self._persistent_pool = ProcessPoolExecutor(max_workers=self.config.n_workers)
        return self._persistent_pool
    
    def _evaluate_with_process_pool(self, population):
        """Efficient ProcessPoolExecutor evaluation matching PyMoo's combined approach"""
        n_samples = len(population)
        fitnesses = [None] * n_samples  # Pre-allocate to maintain order
        
        try:
            # Use persistent pool for combined evaluation (PyMoo pattern)
            executor = self._get_or_create_persistent_pool()
            
            # Submit all combined evaluations (F + G together like PyMoo)
            future_to_idx = {}
            for i, individual in enumerate(population):
                future = executor.submit(self.problem.safe_combined_evaluation, np.array(individual))
                future_to_idx[future] = i
            
            # Collect combined results with timeout
            for future in as_completed(future_to_idx, timeout=self.config.timeout_per_eval):
                idx = future_to_idx[future]
                try:
                    result = future.result()
                    objective = result['F']
                    constraints = result['G']
                    
                    # No penalty functions - use raw objective like PyMoo
                    # Let DEAP handle constraints through its constraint mechanisms
                    fitness = objective
                    
                    # Store constraint values for later use if needed
                    constraint_violation = np.sum(np.maximum(0, constraints))
                    
                    # For maximization problems, return raw objective
                    fitnesses[idx] = (fitness,)  # Maintain correct order
                    self.eval_count += 1
                    
                except Exception as e:
                    if self.config.verbose:
                        print(f"Warning: Combined evaluation {idx} failed: {e}")
                    fitnesses[idx] = (-1e-6,)  # Penalty for failed evaluation
                    self.eval_count += 1
            
            # Check for any None values (failed/timeout evaluations)
            for i, fitness in enumerate(fitnesses):
                if fitness is None:
                    if self.config.verbose:
                        print(f"Warning: Evaluation {i} timed out, using fallback")
                    fitnesses[i] = self._evaluate_individual(population[i])
                    
        except TimeoutError:
            if self.config.verbose:
                print("Warning: Evaluation timeout occurred, using sequential fallback")
            # Fallback to sequential evaluation
            fitnesses = [self._evaluate_individual(ind) for ind in population]
        except Exception as e:
            if self.config.verbose:
                print(f"Warning: Parallel evaluation failed: {e}, using sequential fallback")
            # Fallback to sequential evaluation
            fitnesses = [self._evaluate_individual(ind) for ind in population]
        
        return fitnesses
    
    
    def _evaluate_constraints(self, individual_array):
        """Evaluate only constraints (sequential, fast) - mirroring PyMoo pattern"""
        result = self.problem.evaluate(individual_array)
        return result["G"]
    
    
    def set_algorithm(self, algorithm_name: str, **params):
        """
        Change optimization algorithm and update configuration
        
        Args:
            algorithm_name: Algorithm name from SUPPORTED_ALGORITHMS
            **params: Algorithm-specific parameters to override
        """
        if algorithm_name not in self.SUPPORTED_ALGORITHMS:
            raise ValueError(f"Unsupported algorithm: {algorithm_name}. "
                           f"Supported: {list(self.SUPPORTED_ALGORITHMS.keys())}")
        
        self.config.algorithm = algorithm_name
        
        # Update configuration with provided parameters
        for key, value in params.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                
        # Reconfigure operators if needed
        self._configure_operators()
        
        if self.config.verbose:
            print(f"🔄 Algorithm changed to: {algorithm_name} ({self.SUPPORTED_ALGORITHMS[algorithm_name]})")
            
    def configure_operators(self, selection: str = None, crossover: str = None, 
                          mutation: str = None, **params):
        """
        Configure genetic operators
        
        Args:
            selection: Selection method name
            crossover: Crossover method name  
            mutation: Mutation method name
            **params: Additional operator parameters
        """
        if selection:
            self.config.selection = selection
        if crossover:
            self.config.crossover = crossover
        if mutation:
            self.config.mutation = mutation
            
        # Update configuration with additional parameters
        for key, value in params.items():
            if hasattr(self.config, key):
                setattr(self.config, key, value)
                
        # Reconfigure toolbox
        self._configure_operators()
        
        if self.config.verbose:
            print(f"🛠️  Operators configured: {self.config.selection}, {self.config.crossover}, {self.config.mutation}")

    def get_supported_algorithms(self):
        """Get list of supported algorithms with descriptions"""
        return self.SUPPORTED_ALGORITHMS.copy()
        
    def get_current_config(self):
        """Get current optimization configuration"""
        return self.config
        
    @property
    def algorithm_info(self):
        """Get information about current algorithm"""
        return {
            'name': self.config.algorithm,
            'description': self.SUPPORTED_ALGORITHMS.get(self.config.algorithm, 'Unknown'),
            'config': self.config.__dict__
        }
    
    def optimize(self):
        """
        Run optimization using the configured algorithm
        
        Returns:
            dict: Optimization results with unified format
        """
        self.start_time = time.time()
        self.eval_count = 0
        
        if self.config.verbose:
            print(f"\n🚀 Starting {self.config.algorithm} optimization...")
            
        try:
            # Route to appropriate algorithm implementation
            if self.config.algorithm == 'GA':
                return self._run_ga()
            elif self.config.algorithm == 'DE':
                return self._run_de()
            elif self.config.algorithm == 'PSO':
                return self._run_pso()
            elif self.config.algorithm == 'CMA-ES':
                return self._run_cmaes()
            elif self.config.algorithm == 'NSGA-II':
                return self._run_nsga2()
            elif self.config.algorithm == 'NSGA-III':
                return self._run_nsga3()
            elif self.config.algorithm == 'SPEA2':
                return self._run_spea2()
            elif self.config.algorithm == 'ES':
                return self._run_es()
            elif self.config.algorithm == 'EDA':
                return self._run_eda()
            elif self.config.algorithm == 'MULTI-PSO':
                return self._run_multi_pso()
            elif self.config.algorithm == 'COOP-COEV':
                return self._run_cooperative_coevolution()
            else:
                raise ValueError(f"Algorithm {self.config.algorithm} not implemented yet")
        finally:
            # Ensure cleanup of parallel resources
            self.cleanup()
            
    def _create_initial_population(self):
        """Create initial population exactly like PyMoo (NO perturbation)"""
        population = []
        
        if hasattr(self.problem, 'x0') and self.problem.x0 is not None:
            # Create ALL individuals identical to x0 (exactly like PyMoo)
            # Diversity will come from crossover and mutation during evolution
            for i in range(self.config.pop_size):
                individual = creator.Individual(self.problem.x0.tolist())
                population.append(individual)
            
            if self.config.verbose:
                print(f"   Initialized population from x0: {len(population)} identical individuals (PyMoo approach)")
        else:
            # Fallback to random population
            population = self.toolbox.population(n=self.config.pop_size)
            if self.config.verbose:
                print(f"   Random initial population: {len(population)} individuals")
        
        return population
        
    def _run_ga(self):
        """Run Genetic Algorithm with enhanced progress monitoring"""
        # Initialize population from x0 if available
        population = self._create_initial_population()
        
        # Initialize progress tracking
        convergence_history = {
            'generations': [],
            'best_fitness': [],
            'avg_fitness': [],
            'worst_fitness': [],
            'std_fitness': [],
            'evaluations': [],
            'time_per_gen': []
        }
        
        # Evaluate initial population
        gen_start_time = time.time()
        fitnesses = self._evaluate_population_parallel(population)
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit
            
        # Update hall of fame and statistics
        self.hall_of_fame.update(population)
        record = self.stats.compile(population)
        self.logbook.record(gen=0, evals=len(population), **record)
        
        # Record convergence data
        gen_time = time.time() - gen_start_time
        convergence_history['generations'].append(0)
        convergence_history['best_fitness'].append(record['max'])
        convergence_history['avg_fitness'].append(record['avg'])
        convergence_history['worst_fitness'].append(record['min'])
        convergence_history['std_fitness'].append(record['std'])
        convergence_history['evaluations'].append(len(population))
        convergence_history['time_per_gen'].append(gen_time)
        
        if self.config.verbose:
            # Convert fitness back to AEP for display (fitness = AEP/baseline_aep)
            best_aep = float(record['max']) * self.problem.baseline_aep
            avg_aep = float(record['avg']) * self.problem.baseline_aep
            print(f"Gen 0: Best={best_aep:.3f} GWh, Avg={avg_aep:.3f} GWh, Time={gen_time:.2f}s")
            
        # Evolution loop
        for gen in range(1, self.config.n_gen + 1):
            gen_start_time = time.time()
            
            # Selection with constraint handling
            offspring = self._constraint_tournament_selection(population, len(population))
            offspring = list(map(self.toolbox.clone, offspring))
            
            # Crossover and mutation
            for child1, child2 in zip(offspring[::2], offspring[1::2]):
                if random.random() < self.config.crossover_prob:
                    self.toolbox.mate(child1, child2)
                    del child1.fitness.values
                    del child2.fitness.values
                    
            for mutant in offspring:
                if random.random() < self.config.mutation_prob:
                    self.toolbox.mutate(mutant)
                    del mutant.fitness.values
            
            # Repair infeasible individuals (boundary constraint handling)
            for individual in offspring:
                if not individual.fitness.valid:  # Only repair modified individuals
                    self._repair_infeasible_individual(individual)
                    
            # Evaluate invalid individuals
            invalid_ind = [ind for ind in offspring if not ind.fitness.valid]
            fitnesses = self._evaluate_population_parallel(invalid_ind)
            for ind, fit in zip(invalid_ind, fitnesses):
                ind.fitness.values = fit
                
            # Replace population
            population[:] = offspring
            
            # Check for early termination (PyMoo-style)
            if self._check_early_termination(population):
                break
                
            # Update termination stats
            self._update_termination_stats(gen, population)
            
            # Update statistics
            self.hall_of_fame.update(population)
            record = self.stats.compile(population)
            self.logbook.record(gen=gen, evals=len(invalid_ind), **record)
            
            # Record convergence data
            gen_time = time.time() - gen_start_time
            convergence_history['generations'].append(gen)
            convergence_history['best_fitness'].append(record['max'])
            convergence_history['avg_fitness'].append(record['avg'])
            convergence_history['worst_fitness'].append(record['min'])
            convergence_history['std_fitness'].append(record['std'])
            convergence_history['evaluations'].append(len(invalid_ind))
            convergence_history['time_per_gen'].append(gen_time)
            
            if self.config.verbose:
                # Convert fitness back to AEP for display (fitness = AEP/baseline_aep)
                best_aep = float(record['max']) * self.problem.baseline_aep
                avg_aep = float(record['avg']) * self.problem.baseline_aep
                print(f"Gen {gen}: Best={best_aep:.3f} GWh, Avg={avg_aep:.3f} GWh, Time={gen_time:.2f}s")
                
        total_time = time.time() - self.start_time
        
        # Return best solution with convergence data
        best_individual = self.hall_of_fame[0]
        
        return {
            'x': np.array(best_individual),
            'f': np.array(best_individual.fitness.values),
            'n_evals': self.eval_count,
            'exec_time': total_time,
            'algorithm': self.config.algorithm,
            'framework': 'DEAP',
            'hall_of_fame': [np.array(ind) for ind in self.hall_of_fame],
            'logbook': self.logbook,
            'convergence_history': convergence_history
        }

    def _run_de(self):
        """Run Differential Evolution"""
        # Initialize population
        population = self._create_initial_population()
        
        # Evaluate initial population
        fitnesses = self._evaluate_population_parallel(population)
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit
            
        self.hall_of_fame.update(population)
        
        # DE parameters
        F = self.config.de_F  # Differential weight
        CR = self.config.de_CR  # Crossover probability
        
        convergence_history = {
            'generations': [0],
            'best_fitness': [max(fit[0] for fit in fitnesses)],
            'avg_fitness': [np.mean([fit[0] for fit in fitnesses])],
            'worst_fitness': [min(fit[0] for fit in fitnesses)],
            'std_fitness': [np.std([fit[0] for fit in fitnesses])],
            'evaluations': [len(population)],
            'time_per_gen': [0]
        }
        
        for gen in range(1, self.config.n_gen + 1):
            gen_start_time = time.time()
            
            for i, target in enumerate(population):
                # Select three random individuals different from target
                candidates = [ind for j, ind in enumerate(population) if j != i]
                if len(candidates) >= 3:
                    a, b, c = random.sample(candidates, 3)
                    
                    # Create trial vector
                    trial = creator.Individual()
                    for j in range(self.problem.n_var):
                        if random.random() < CR or j == random.randrange(self.problem.n_var):
                            trial.append(a[j] + F * (b[j] - c[j]))
                        else:
                            trial.append(target[j])
                            
                    # Bound trial vector to [0,1]
                    for j in range(self.problem.n_var):
                        trial[j] = max(0.0, min(1.0, trial[j]))
                        
                    # Evaluate trial
                    trial.fitness.values = self.toolbox.evaluate(trial)
                    
                    # Selection
                    if trial.fitness.values[0] > target.fitness.values[0]:
                        population[i] = trial
                        
            # Update statistics
            self.hall_of_fame.update(population)
            record = self.stats.compile(population)
            self.logbook.record(gen=gen, evals=self.config.pop_size, **record)
            
            gen_time = time.time() - gen_start_time
            convergence_history['generations'].append(gen)
            convergence_history['best_fitness'].append(record['max'])
            convergence_history['avg_fitness'].append(record['avg'])
            convergence_history['worst_fitness'].append(record['min'])
            convergence_history['std_fitness'].append(record['std'])
            convergence_history['evaluations'].append(self.config.pop_size)
            convergence_history['time_per_gen'].append(gen_time)
            
            if self.config.verbose and gen % 5 == 0:
                # Convert fitness back to AEP for display (fitness = AEP/baseline_aep)
                best_aep = float(record['max']) * self.problem.baseline_aep
                avg_aep = float(record['avg']) * self.problem.baseline_aep
                print(f"Gen {gen}: Best={best_aep:.3f} GWh, Avg={avg_aep:.3f} GWh")
                
        total_time = time.time() - self.start_time
        
        best_individual = self.hall_of_fame[0]
        
        return {
            'x': np.array(best_individual),
            'f': np.array(best_individual.fitness.values),
            'n_evals': self.eval_count,
            'exec_time': total_time,
            'algorithm': self.config.algorithm,
            'framework': 'DEAP',
            'hall_of_fame': [np.array(ind) for ind in self.hall_of_fame],
            'logbook': self.logbook,
            'convergence_history': convergence_history
        }

    def _run_pso(self):
        """Run Particle Swarm Optimization"""
        # Initialize particles with position and velocity
        population = self._create_initial_population()
        
        # Initialize velocities
        velocities = []
        for _ in range(self.config.pop_size):
            velocity = [random.uniform(self.config.pso_smin, self.config.pso_smax) 
                       for _ in range(self.problem.n_var)]
            velocities.append(velocity)
        
        # Evaluate initial population
        fitnesses = self._evaluate_population_parallel(population)
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit
        
        # Initialize personal bests
        personal_bests = [copy.deepcopy(ind) for ind in population]
        personal_best_fits = [ind.fitness.values for ind in population]
        
        # Find global best
        best_idx = max(range(len(population)), key=lambda i: population[i].fitness.values[0])
        global_best = copy.deepcopy(population[best_idx])
        
        self.hall_of_fame.update(population)
        
        convergence_history = {
            'generations': [0],
            'best_fitness': [global_best.fitness.values[0]],
            'avg_fitness': [np.mean([ind.fitness.values[0] for ind in population])],
            'worst_fitness': [min(ind.fitness.values[0] for ind in population)],
            'std_fitness': [np.std([ind.fitness.values[0] for ind in population])],
            'evaluations': [len(population)],
            'time_per_gen': [0]
        }
        
        for gen in range(1, self.config.n_gen + 1):
            gen_start_time = time.time()
            
            for i, (particle, velocity) in enumerate(zip(population, velocities)):
                # Update velocity
                for j in range(self.problem.n_var):
                    r1, r2 = random.random(), random.random()
                    velocity[j] = (self.config.pso_w * velocity[j] +
                                 self.config.pso_c1 * r1 * (personal_bests[i][j] - particle[j]) +
                                 self.config.pso_c2 * r2 * (global_best[j] - particle[j]))
                    
                    # Clamp velocity
                    velocity[j] = max(self.config.pso_smin, min(self.config.pso_smax, velocity[j]))
                    
                    # Update position
                    particle[j] += velocity[j]
                    
                    # Bound position to [0,1]
                    particle[j] = max(0.0, min(1.0, particle[j]))
                
                # Evaluate particle
                particle.fitness.values = self.toolbox.evaluate(particle)
                
                # Update personal best
                if particle.fitness.values[0] > personal_best_fits[i][0]:
                    personal_bests[i] = copy.deepcopy(particle)
                    personal_best_fits[i] = particle.fitness.values
                    
                    # Update global best
                    if particle.fitness.values[0] > global_best.fitness.values[0]:
                        global_best = copy.deepcopy(particle)
            
            # Update statistics
            self.hall_of_fame.update(population)
            record = self.stats.compile(population)
            self.logbook.record(gen=gen, evals=self.config.pop_size, **record)
            
            gen_time = time.time() - gen_start_time
            convergence_history['generations'].append(gen)
            convergence_history['best_fitness'].append(record['max'])
            convergence_history['avg_fitness'].append(record['avg'])
            convergence_history['worst_fitness'].append(record['min'])
            convergence_history['std_fitness'].append(record['std'])
            convergence_history['evaluations'].append(self.config.pop_size)
            convergence_history['time_per_gen'].append(gen_time)
            
            if self.config.verbose and gen % 5 == 0:
                # Convert fitness back to AEP for display (fitness = AEP/baseline_aep)
                best_aep = float(record['max']) * self.problem.baseline_aep
                avg_aep = float(record['avg']) * self.problem.baseline_aep
                print(f"Gen {gen}: Best={best_aep:.3f} GWh, Avg={avg_aep:.3f} GWh")
                
        total_time = time.time() - self.start_time
        
        return {
            'x': np.array(global_best),
            'f': np.array(global_best.fitness.values),
            'n_evals': self.eval_count,
            'exec_time': total_time,
            'algorithm': self.config.algorithm,
            'framework': 'DEAP',
            'hall_of_fame': [np.array(ind) for ind in self.hall_of_fame],
            'logbook': self.logbook,
            'convergence_history': convergence_history
        }
    
    def _run_nsga2(self):
        """Run NSGA-II Multi-objective Algorithm"""
        # Configure for multi-objective
        self.toolbox.register("select", tools.selNSGA2)
        
        # Initialize population
        population = self._create_initial_population()
        
        # Evaluate initial population
        fitnesses = self._evaluate_population_parallel(population)
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit
            
        # Run NSGA-II using built-in algorithm
        population, logbook = algorithms.eaMuPlusLambda(
            population, self.toolbox, 
            mu=self.config.pop_size, 
            lambda_=self.config.pop_size,
            cxpb=self.config.crossover_prob, 
            mutpb=self.config.mutation_prob, 
            ngen=self.config.n_gen, 
            stats=self.stats,
            halloffame=self.hall_of_fame, 
            verbose=self.config.verbose
        )
        
        total_time = time.time() - self.start_time
        
        # Get Pareto front
        pareto_front = tools.sortNondominated(population, len(population), first_front_only=True)[0]
        
        # Select representative solution from Pareto front
        best_individual = self._select_representative_solution(pareto_front)
        
        # Build convergence history from logbook
        convergence_history = {
            'generations': [record['gen'] for record in logbook],
            'best_fitness': [record['max'] for record in logbook],
            'avg_fitness': [record['avg'] for record in logbook],
            'worst_fitness': [record['min'] for record in logbook],
            'std_fitness': [record['std'] for record in logbook],
            'evaluations': [record.get('evals', self.config.pop_size) for record in logbook],
            'time_per_gen': [0] * len(logbook)  # NSGA-II doesn't track per-generation time
        }
        
        return {
            'x': best_individual,
            'f': np.array([ind for ind in pareto_front if np.array_equal(np.array(ind), best_individual)][0].fitness.values),
            'n_evals': self.eval_count,
            'exec_time': total_time,
            'algorithm': self.config.algorithm,
            'framework': 'DEAP',
            'hall_of_fame': [np.array(ind) for ind in pareto_front[:min(10, len(pareto_front))]],
            'logbook': logbook,
            'convergence_history': convergence_history,
            'pareto_front': pareto_front
        }

    def _run_nsga3(self):
        """Run NSGA-III Multi-objective Algorithm"""
        # Generate reference points
        ref_points = tools.uniform_reference_points(self.problem.n_obj, self.config.nsga3_ref_points)
        
        # Configure NSGA-III selection
        self.toolbox.register("select", tools.selNSGA3, ref_points=ref_points)
        
        # Initialize population
        population = self._create_initial_population()
        
        # Evaluate initial population
        fitnesses = self._evaluate_population_parallel(population)
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit
            
        # Run NSGA-III
        population, logbook = algorithms.eaMuPlusLambda(
            population, self.toolbox,
            mu=self.config.pop_size,
            lambda_=self.config.pop_size,
            cxpb=self.config.crossover_prob,
            mutpb=self.config.mutation_prob,
            ngen=self.config.n_gen,
            stats=self.stats,
            halloffame=self.hall_of_fame,
            verbose=self.config.verbose
        )
        
        total_time = time.time() - self.start_time
        
        # Get best solutions from Pareto front
        best_individuals = tools.sortNondominated(population, len(population), first_front_only=True)[0]
        
        # Select representative solution
        best_individual = self._select_representative_solution(best_individuals)
        
        # Build convergence history from logbook
        convergence_history = {
            'generations': [record['gen'] for record in logbook],
            'best_fitness': [record['max'] for record in logbook],
            'avg_fitness': [record['avg'] for record in logbook],
            'worst_fitness': [record['min'] for record in logbook],
            'std_fitness': [record['std'] for record in logbook],
            'evaluations': [record.get('evals', self.config.pop_size) for record in logbook],
            'time_per_gen': [0] * len(logbook)  # NSGA-III doesn't track per-generation time
        }
        
        # Find fitness for best individual with better error handling
        try:
            best_fitness = None
            for ind in best_individuals:
                if np.array_equal(np.array(ind), best_individual):
                    best_fitness = np.array(ind.fitness.values)
                    break
            if best_fitness is None and len(best_individuals) > 0:
                # Fallback: use first individual's fitness
                best_fitness = np.array(best_individuals[0].fitness.values)
            elif best_fitness is None:
                # Ultimate fallback
                best_fitness = np.array([1.0])
        except Exception as e:
            if self.config.verbose:
                print(f"❌ NSGA-III fitness extraction failed: {e}")
            best_fitness = np.array([1.0])  # Default fallback
        
        return {
            'x': best_individual,
            'f': best_fitness,
            'n_evals': self.eval_count,
            'exec_time': total_time,
            'algorithm': self.config.algorithm,
            'framework': 'DEAP',
            'hall_of_fame': [np.array(ind) for ind in best_individuals[:min(10, len(best_individuals))]],
            'logbook': logbook,
            'convergence_history': convergence_history,
            'best_individuals': best_individuals
        }

    def _run_spea2(self):
        """Run SPEA2 Multi-objective Algorithm"""
        self.toolbox.register("select", tools.selSPEA2)
        
        # Initialize population
        population = self._create_initial_population()
        archive = []
        
        # Evaluate initial population
        fitnesses = self._evaluate_population_parallel(population)
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit
            
        # Run SPEA2 evolution
        for gen in range(self.config.n_gen):
            # Combine population and archive
            archive = tools.selSPEA2(population + archive, self.config.pop_size)
            
            # Generate offspring
            offspring = algorithms.varAnd(archive, self.toolbox, 
                                        cxpb=self.config.crossover_prob,
                                        mutpb=self.config.mutation_prob)
            
            # Evaluate offspring
            fitnesses = self._evaluate_population_parallel(offspring)
            for ind, fit in zip(offspring, fitnesses):
                ind.fitness.values = fit
                
            population = offspring
            
        total_time = time.time() - self.start_time
        
        # Select representative solution from archive
        best_individual = self._select_representative_solution(archive)
        
        # Build basic convergence history (SPEA2 doesn't use logbook)
        convergence_history = {
            'generations': list(range(self.config.n_gen + 1)),
            'best_fitness': [0] * (self.config.n_gen + 1),  # Would need more complex tracking
            'avg_fitness': [0] * (self.config.n_gen + 1),
            'worst_fitness': [0] * (self.config.n_gen + 1),
            'std_fitness': [0] * (self.config.n_gen + 1),
            'evaluations': [self.config.pop_size] * (self.config.n_gen + 1),
            'time_per_gen': [0] * (self.config.n_gen + 1)
        }
        
        return {
            'x': best_individual,
            'f': np.array([ind for ind in archive if np.array_equal(np.array(ind), best_individual)][0].fitness.values),
            'n_evals': self.eval_count,
            'exec_time': total_time,
            'algorithm': self.config.algorithm,
            'framework': 'DEAP',
            'hall_of_fame': [np.array(ind) for ind in archive[:min(10, len(archive))]],
            'convergence_history': convergence_history,
            'archive': archive
        }

    def _run_cmaes(self):
        """Run CMA-ES Algorithm"""
        try:
            import cma
        except ImportError:
            print("CMA-ES requires 'cma' package. Install with: pip install cma")
            print("Falling back to GA...")
            return self._run_ga()
            
        # CMA-ES parameters
        sigma = self.config.cmaes_sigma
        lambda_pop = self.config.cmaes_lambda or (4 + int(3 * np.log(self.problem.n_var)))
        
        # Initialize CMA-ES
        if hasattr(self.problem, 'x0') and self.problem.x0 is not None:
            x0 = self.problem.x0
        else:
            x0 = np.random.random(self.problem.n_var)
            
        es = cma.CMAEvolutionStrategy(x0, sigma, {'popsize': lambda_pop})
        
        convergence_history = {
            'generations': [],
            'best_fitness': [],
            'avg_fitness': [],
            'worst_fitness': [],
            'std_fitness': [],
            'evaluations': [],
            'time_per_gen': []
        }
        
        gen = 0
        while not es.stop() and gen < self.config.n_gen:
            gen_start_time = time.time()
            
            # Generate and evaluate offspring
            solutions = es.ask()
            
            # Bound solutions to [0,1]
            bounded_solutions = []
            for sol in solutions:
                bounded_sol = np.clip(sol, 0.0, 1.0)
                bounded_solutions.append(bounded_sol)
            
            # Evaluate solutions using CMA-ES specific evaluation
            fitnesses = []
            for sol in bounded_solutions:
                # Evaluate with combined F+G like other algorithms
                result = self.problem.safe_combined_evaluation(sol)
                objective = result['F']
                constraints = result['G']
                
                # Handle constraints properly for minimization
                constraint_violation = np.sum(np.maximum(0, constraints))
                
                # For CMA-ES (minimization): minimize -objective + constraint_penalty
                # This ensures constraint violations make fitness worse (higher)
                if constraint_violation > 0:
                    # Use logarithmic penalty to handle large violations gracefully
                    penalty = 0.1 * np.log(1 + constraint_violation)  # Gentle logarithmic penalty
                    penalty = min(penalty, objective * 0.1)  # Cap penalty at 10% of objective
                    fitness_for_cmaes = -objective + penalty  # Higher = worse for minimization
                else:
                    fitness_for_cmaes = -objective  # Pure objective (negated for minimization)
                
                fitnesses.append(fitness_for_cmaes)
                self.eval_count += 1
                
            # Update CMA-ES with bounded solutions (CRITICAL FIX)
            es.tell(bounded_solutions, fitnesses)
            
            # Check for early termination (convert CMA-ES population for compatibility)
            cma_population = [creator.Individual(sol) for sol in bounded_solutions]
            for i, fitness_val in enumerate(fitnesses):
                # Convert back to maximization format for early termination check
                maximization_fitness = -fitness_val  # Convert back from minimization
                cma_population[i].fitness.values = (maximization_fitness,)
            
            if self._check_early_termination(cma_population):
                if self.config.verbose:
                    print(f"   CMA-ES early termination at generation {gen}")
                break
            
            gen_time = time.time() - gen_start_time
            convergence_history['generations'].append(gen)
            convergence_history['best_fitness'].append(-min(fitnesses))
            convergence_history['avg_fitness'].append(-np.mean(fitnesses))
            convergence_history['worst_fitness'].append(-max(fitnesses))
            convergence_history['std_fitness'].append(np.std(fitnesses))
            convergence_history['evaluations'].append(len(solutions))
            convergence_history['time_per_gen'].append(gen_time)
            
            if self.config.verbose and gen % 5 == 0:
                # Convert fitness back to AEP for display
                # CMA-ES minimizes -objective, so best = min(fitnesses)
                # Since objective = aep/baseline, CMA-ES fitness = -aep/baseline
                # So: AEP = -fitness * baseline_aep
                best_val = -min(fitnesses) if fitnesses else 0
                avg_val = -np.mean(fitnesses) if fitnesses else 0
                best_aep = float(best_val) * self.problem.baseline_aep
                avg_aep = float(avg_val) * self.problem.baseline_aep
                print(f"Gen {gen}: Best={best_aep:.3f} GWh, Avg={avg_aep:.3f} GWh")
                
            gen += 1
            
        total_time = time.time() - self.start_time
        
        # Get best solution
        best_solution = es.result.xbest
        best_solution = np.clip(best_solution, 0.0, 1.0)
        
        result = self.problem.evaluate(best_solution)
        best_fitness = result["F"]
        self.eval_count += 1  # Count final evaluation
        
        return {
            'x': best_solution,
            'f': best_fitness,
            'n_evals': self.eval_count,
            'exec_time': total_time,
            'algorithm': self.config.algorithm,
            'framework': 'DEAP',
            'convergence_history': convergence_history,
            'cmaes_result': es.result
        }

    def _run_es(self):
        """Run Evolution Strategies (μ,λ) or (μ+λ)"""
        # ES parameters
        mu = self.config.pop_size // 4  # Parent population size
        lambda_ = self.config.pop_size   # Offspring population size
        
        # Initialize population
        population = self._create_initial_population()[:mu]  # Only use mu parents
        
        # Evaluate initial population
        fitnesses = self._evaluate_population_parallel(population)
        for ind, fit in zip(population, fitnesses):
            ind.fitness.values = fit
            
        convergence_history = {
            'generations': [0],
            'best_fitness': [max(fit[0] for fit in fitnesses)],
            'avg_fitness': [np.mean([fit[0] for fit in fitnesses])],
            'worst_fitness': [min(fit[0] for fit in fitnesses)],
            'std_fitness': [np.std([fit[0] for fit in fitnesses])],
            'evaluations': [len(population)],
            'time_per_gen': [0]
        }
        
        for gen in range(1, self.config.n_gen + 1):
            gen_start_time = time.time()
            
            # Generate lambda offspring
            offspring = []
            for _ in range(lambda_):
                parent = random.choice(population)
                child = creator.Individual(parent[:])
                
                # Mutate offspring
                self.toolbox.mutate(child)
                del child.fitness.values
                offspring.append(child)
            
            # Evaluate offspring
            fitnesses = self._evaluate_population_parallel(offspring)
            for ind, fit in zip(offspring, fitnesses):
                ind.fitness.values = fit
            
            # Selection: (μ,λ) strategy - select mu best from offspring only
            offspring.sort(key=lambda x: x.fitness.values[0], reverse=True)
            population = offspring[:mu]
            
            self.hall_of_fame.update(population)
            record = self.stats.compile(population)
            self.logbook.record(gen=gen, evals=lambda_, **record)
            
            gen_time = time.time() - gen_start_time
            convergence_history['generations'].append(gen)
            convergence_history['best_fitness'].append(record['max'])
            convergence_history['avg_fitness'].append(record['avg'])
            convergence_history['worst_fitness'].append(record['min'])
            convergence_history['std_fitness'].append(record['std'])
            convergence_history['evaluations'].append(lambda_)
            convergence_history['time_per_gen'].append(gen_time)
            
            if self.config.verbose and gen % 5 == 0:
                # Convert fitness back to AEP for display (fitness = AEP/baseline_aep)
                best_aep = float(record['max']) * self.problem.baseline_aep
                avg_aep = float(record['avg']) * self.problem.baseline_aep
                print(f"Gen {gen}: Best={best_aep:.3f} GWh, Avg={avg_aep:.3f} GWh")
                
        total_time = time.time() - self.start_time
        
        best_individual = self.hall_of_fame[0]
        
        return {
            'x': np.array(best_individual),
            'f': np.array(best_individual.fitness.values),
            'n_evals': self.eval_count,
            'exec_time': total_time,
            'algorithm': self.config.algorithm,
            'framework': 'DEAP',
            'hall_of_fame': [np.array(ind) for ind in self.hall_of_fame],
            'logbook': self.logbook,
            'convergence_history': convergence_history
        }

    def _run_eda(self):
        """Run Estimation of Distribution Algorithm"""
        # EDA implementation - placeholder for now
        print("EDA implementation in progress - using GA")
        return self._run_ga()

    def _run_multi_pso(self):
        """Run Multi-Swarm Particle Swarm Optimization"""
        # Multi-swarm PSO implementation - placeholder for now
        print("Multi-Swarm PSO implementation in progress - using PSO")
        return self._run_pso()

    def _run_cooperative_coevolution(self):
        """Run Cooperative Coevolution"""
        # Cooperative coevolution implementation - placeholder for now
        print("Cooperative Coevolution implementation in progress - using GA")
        return self._run_ga()
    
    def compare_algorithms(self, algorithms: List[str], runs_per_algorithm: int = 1):
        """
        Compare multiple algorithms on the same problem
        
        Args:
            algorithms: List of algorithm names to compare
            runs_per_algorithm: Number of runs per algorithm for statistical significance
            
        Returns:
            dict: Comparison results with statistics
        """
        comparison_results = {}
        
        print(f"\n🔬 Comparing {len(algorithms)} algorithms with {runs_per_algorithm} runs each...")
        
        for algorithm in algorithms:
            if algorithm not in self.SUPPORTED_ALGORITHMS:
                print(f"⚠️  Skipping unsupported algorithm: {algorithm}")
                continue
                
            print(f"\n🧬 Running {algorithm}...")
            algorithm_results = []
            
            for run in range(runs_per_algorithm):
                # Set algorithm
                original_algorithm = self.config.algorithm
                self.set_algorithm(algorithm)
                
                # Run optimization
                result = self.optimize()
                algorithm_results.append(result)
                
                if runs_per_algorithm > 1:
                    print(f"   Run {run+1}/{runs_per_algorithm}: Best={result['f'][0]:.6f}")
                
                # Restore original algorithm
                self.config.algorithm = original_algorithm
            
            # Calculate statistics
            best_fitnesses = [result['f'][0] for result in algorithm_results]
            exec_times = [result['exec_time'] for result in algorithm_results]
            n_evals = [result['n_evals'] for result in algorithm_results]
            
            comparison_results[algorithm] = {
                'algorithm': algorithm,
                'runs': algorithm_results,
                'statistics': {
                    'fitness': {
                        'mean': np.mean(best_fitnesses),
                        'std': np.std(best_fitnesses),
                        'min': np.min(best_fitnesses),
                        'max': np.max(best_fitnesses)
                    },
                    'time': {
                        'mean': np.mean(exec_times),
                        'std': np.std(exec_times),
                        'min': np.min(exec_times),
                        'max': np.max(exec_times)
                    },
                    'evaluations': {
                        'mean': np.mean(n_evals),
                        'std': np.std(n_evals),
                        'min': np.min(n_evals),
                        'max': np.max(n_evals)
                    }
                }
            }
            
            print(f"   ✅ {algorithm}: Best={float(np.max(best_fitnesses)):.6f}, "
                  f"Avg={float(np.mean(best_fitnesses)):.6f}±{float(np.std(best_fitnesses)):.6f}")
        
        self.comparison_results = comparison_results
        return comparison_results
    
    def run_hybrid_sequence(self, sequence: List[Dict[str, Any]]):
        """
        Run hybrid optimization sequence with different algorithms
        
        Args:
            sequence: List of algorithm configurations
                     [{'algorithm': 'GA', 'generations': 10}, ...]
                     
        Returns:
            dict: Combined optimization results
        """
        print(f"\n🔗 Running hybrid sequence with {len(sequence)} stages...")
        
        # Start with initial population
        current_best = None
        total_evals = 0
        total_time = 0
        stage_results = []
        
        for i, stage in enumerate(sequence):
            algorithm = stage['algorithm']
            generations = stage.get('generations', self.config.n_gen)
            
            print(f"\n📍 Stage {i+1}: {algorithm} for {generations} generations")
            
            # Configure algorithm
            original_algorithm = self.config.algorithm
            original_n_gen = self.config.n_gen
            
            self.set_algorithm(algorithm)
            self.config.n_gen = generations
            
            # If we have a previous best, use it as starting point
            if current_best is not None:
                self.problem.x0 = current_best
            
            # Run optimization stage
            stage_start_time = time.time()
            result = self.optimize()
            stage_time = time.time() - stage_start_time
            
            # Update current best
            current_best = result['x']
            total_evals += result['n_evals']
            total_time += stage_time
            
            stage_results.append({
                'stage': i + 1,
                'algorithm': algorithm,
                'generations': generations,
                'result': result,
                'stage_time': stage_time
            })
            
            print(f"   ✅ Stage {i+1} completed: Best={result['f'][0]:.6f}, Time={stage_time:.2f}s")
            
            # Restore configuration
            self.config.algorithm = original_algorithm
            self.config.n_gen = original_n_gen
        
        return {
            'sequence': sequence,
            'stage_results': stage_results,
            'final_best_x': current_best,
            'final_best_f': stage_results[-1]['result']['f'],
            'total_evaluations': total_evals,
            'total_time': total_time,
            'framework': 'DEAP-Hybrid'
        }
    
    def _select_representative_solution(self, solutions):
        """
        Select single representative solution from multiple solutions (Pareto front/population)
        
        Args:
            solutions: List of individuals or numpy array of solutions
            
        Returns:
            Single representative solution (numpy array)
        """
        if len(solutions) == 0:
            raise ValueError("Empty solution set provided")
        
        if len(solutions) == 1:
            return np.array(solutions[0])
        
        # Strategy: Select solution with best fitness in first objective (AEP)
        best_individual = None
        best_fitness = float('-inf')
        
        for individual in solutions:
            if hasattr(individual, 'fitness') and individual.fitness.values:
                fitness = individual.fitness.values[0]  # First objective (AEP)
                if fitness > best_fitness:
                    best_fitness = fitness
                    best_individual = individual
        
        if best_individual is not None:
            return np.array(best_individual)
        else:
            # Fallback: return first solution if no fitness information
            return np.array(solutions[0])
    
    def get_results(self):
        """Get stored optimization results"""
        return {
            'last_result': getattr(self, '_last_result', None),
            'comparison_results': self.comparison_results,
            'algorithm_info': self.algorithm_info
        }
    
    def _check_early_termination(self, population):
        """Check if optimization should terminate early (PyMoo-style convergence)"""
        if not self.config.enable_early_termination:
            return False
            
        # Get current best fitness
        if population:
            current_best = max(ind.fitness.values[0] for ind in population)
            self.best_fitness_history.append(current_best)
            
            # Check for improvement
            if len(self.best_fitness_history) >= 2:
                improvement = abs(self.best_fitness_history[-1] - self.best_fitness_history[-2])
                
                if improvement < self.config.convergence_tol:
                    self.generations_without_improvement += 1
                else:
                    self.generations_without_improvement = 0
                
                # Terminate if no improvement for patience generations
                if self.generations_without_improvement >= self.config.patience:
                    if self.config.verbose:
                        print(f"   🏁 Early termination: no improvement for {self.config.patience} generations")
                        print(f"      Best fitness: {float(current_best):.6f}")
                        print(f"      Convergence tolerance: {float(self.config.convergence_tol)}")
                    return True
        
        return False
    
    def _update_termination_stats(self, generation, population):
        """Update termination statistics for monitoring"""
        if population:
            current_best = max(ind.fitness.values[0] for ind in population)
            if self.config.verbose and generation % 5 == 0:  # Print every 5 generations
                # Convert fitness back to AEP for display (fitness = AEP/baseline_aep)
                best_aep = float(current_best) * self.problem.baseline_aep
                print(f"   Gen {generation:2d}: Best={best_aep:.3f} GWh, No improvement: {self.generations_without_improvement}")
    
    def cleanup(self):
        """Clean up parallel resources including persistent pool"""
        # Clean up persistent ProcessPoolExecutor
        if hasattr(self, '_persistent_pool') and self._persistent_pool is not None:
            try:
                self._persistent_pool.shutdown(wait=True)
                self._persistent_pool = None
                if self.config.verbose:
                    print("   🧹 Persistent ProcessPoolExecutor cleaned up")
            except Exception as e:
                if self.config.verbose:
                    print(f"   ⚠️  Error cleaning up persistent pool: {e}")
        
        # Clean up legacy pool
        if hasattr(self, 'pool') and self.pool is not None:
            try:
                self.pool.close()
                self.pool.join()
                self.pool = None
                if self.config.verbose:
                    print("   🧹 Multiprocessing pool cleaned up")
            except Exception as e:
                if self.config.verbose:
                    print(f"   ⚠️ Pool cleanup warning: {e}")
    
    def __del__(self):
        """Destructor to ensure cleanup"""
        self.cleanup()
