#LATEX=xelatex
# BIB=biber


#normal 
# LATEX=pdflatex --shell-escape 
LATEX=pdflatex
BIB=bibtex




all:
	$(LATEX) wind_farm_optimization_report.tex 
	$(BIB) wind_farm_optimization_report
	makeindex wind_farm_optimization_report.nlo -s nomencl.ist -o wind_farm_optimization_report.nls
	makeindex wind_farm_optimization_report.idx
	$(LATEX) wind_farm_optimization_report.tex
	$(LATEX) wind_farm_optimization_report.tex

clean:
	rm -f wind_farm_optimization_report.toc wind_farm_optimization_report.aux wind_farm_optimization_report.lo*  
	rm -f wind_farm_optimization_report.bbl wind_farm_optimization_report.bcf wind_farm_optimization_report.synctex.gz 
	rm -f wind_farm_optimization_report.blg wind_farm_optimization_report.log wind_farm_optimization_report.i* 
	rm -f wind_farm_optimization_report.out   
	rm -rf dev.log dev.out dev.toc 
	rm -f wind_farm_optimization_report.run.xml *.backup  
	find . -iname "*.aux" -type f -delete
	find . -type f \( \
	  -name "*.aux" \
	  -o -name "*.log" \
	  -o -name "*.out" \
	  -o -name "*.toc" \
	  -o -name "*.lof" \
	  -o -name "*.lot" \
	  -o -name "*.bbl" \
	  -o -name "*.blg" \
	  -o -name "*.synctex.gz" \
	  -o -name "*.fls" \
	  -o -name "*.fdb_latexmk" \
	  -o -name "*.run.xml" \
	  -o -name "*.bcf" \
	  -o -name "*.nav" \
	  -o -name "*.snm" \
	  -o -name "*.vrb" \
	  -o -name "*.dvi" \
	  -o -name "*.idx" \
	  -o -name "*.ilg" \
	  -o -name "*.ind" \
	  -o -name "*.lol" \
	  -o -name "*.thm" \
 	  -o -name "*.brf" \
	  -o -name "*.ent" \
	  -o -name "*.acn" \
	  -o -name "*.acr" \
	  -o -name "*.alg" \
	  -o -name "*.glg" \
	  -o -name "*.glo" \
	  -o -name "*.gls" \
	  -o -name "*.ist" \
	\) -delete

dev:
	$(LATEX) dev.tex
	$(BIB) dev
	makeindex dev.nlo -s nomencl.ist -o wind_farm_optimization_report.nls
	makeindex dev.idx
	$(LATEX) dev.tex
	$(LATEX) dev.tex



git:
	git add -A ; git commit -m "small changes" ; git push
