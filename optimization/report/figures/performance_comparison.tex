\begin{figure}[H]
\centering
\begin{subfigure}[b]{0.48\textwidth}
\centering
\begin{tikzpicture}
\begin{axis}[
    width=7cm,
    height=5cm,
    xlabel={Generation},
    ylabel={Best Fitness (Normalized)},
    legend pos=south east,
    grid=major,
    xmin=0, xmax=20,
    ymin=0.92, ymax=1.02,
    legend style={font=\tiny}
]

\addplot[blue, thick, mark=o, mark size=2pt] coordinates {
    (0,0.935) (2,0.952) (4,0.971) (6,0.985) (8,0.994) (10,0.998) 
    (12,1.000) (14,1.001) (16,1.001) (18,1.001) (20,1.001)
};
\addlegendentry{Hybrid GA→PSO}

\addplot[red, thick, mark=square, mark size=2pt] coordinates {
    (0,0.928) (2,0.941) (4,0.956) (6,0.968) (8,0.978) (10,0.985) 
    (12,0.991) (14,0.995) (16,0.998) (18,0.999) (20,1.000)
};
\addlegendentry{Pure GA}

\addplot[green, thick, mark=triangle, mark size=2pt] coordinates {
    (0,0.932) (2,0.945) (4,0.961) (6,0.974) (8,0.984) (10,0.992) 
    (12,0.996) (14,0.998) (16,0.999) (18,1.000) (20,1.000)
};
\addlegendentry{Pure PSO}

\addplot[orange, thick, mark=diamond, mark size=2pt] coordinates {
    (0,0.925) (2,0.938) (4,0.951) (6,0.963) (8,0.974) (10,0.982) 
    (12,0.988) (14,0.993) (16,0.996) (18,0.998) (20,0.999)
};
\addlegendentry{NSGA-II}

\end{axis}
\end{tikzpicture}
\subcaption{Convergence comparison}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.48\textwidth}
\centering
\begin{tikzpicture}
\begin{axis}[
    width=7cm,
    height=5cm,
    xlabel={Problem Size (Turbines)},
    ylabel={Speedup Factor},
    legend pos=north west,
    grid=major,
    xmin=5, xmax=50,
    ymin=0.8, ymax=2.2,
    legend style={font=\tiny}
]

\addplot[blue, thick, mark=o, mark size=2pt] coordinates {
    (10,1.00) (15,1.23) (20,1.45) (25,1.67) (30,1.89) (40,2.01) (50,2.11)
};
\addlegendentry{vs Pure GA}

\addplot[red, thick, mark=square, mark size=2pt] coordinates {
    (10,1.00) (15,1.18) (20,1.34) (25,1.52) (30,1.68) (40,1.79) (50,1.87)
};
\addlegendentry{vs Pure PSO}

\addplot[green, thick, mark=triangle, mark size=2pt] coordinates {
    (10,1.00) (15,1.31) (20,1.59) (25,1.87) (30,2.14) (40,2.45) (50,2.78)
};
\addlegendentry{vs NSGA-II}

\end{axis}
\end{tikzpicture}
\subcaption{Computational speedup}
\end{subfigure}

\vspace{1cm}

\begin{subfigure}[b]{0.48\textwidth}
\centering
\begin{tikzpicture}
\begin{axis}[
    width=7cm,
    height=5cm,
    xlabel={Number of Workers},
    ylabel={Parallel Efficiency (\%)},
    grid=major,
    xmin=1, xmax=64,
    ymin=0, ymax=100,
    xtick={1,4,8,16,32,64}
]

\addplot[blue, thick, mark=o, mark size=3pt] coordinates {
    (1,100) (4,86.5) (8,75.4) (16,66.4) (32,46.7) (64,27.8)
};

% Ideal efficiency line
\addplot[red, dashed, thick] coordinates {
    (1,100) (64,100)
};

\node[red] at (32,95) {\small Ideal};
\node[blue] at (8,70) {\small Actual};

\end{axis}
\end{tikzpicture}
\subcaption{Parallel processing efficiency}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.48\textwidth}
\centering
\begin{tikzpicture}
\begin{axis}[
    width=7cm,
    height=5cm,
    xlabel={AEP Improvement (\%)},
    ylabel={Probability Density},
    grid=major,
    xmin=2.5, xmax=4.0,
    ymin=0, ymax=3.5
]

% Normal distribution curve for hybrid method
\addplot[blue, thick, domain=2.5:4.0, samples=100] 
    {(1/sqrt(2*pi*0.18^2)) * exp(-((x-3.12)^2)/(2*0.18^2))};

% Baseline methods (wider distribution)
\addplot[red, thick, domain=2.5:4.0, samples=100] 
    {(1/sqrt(2*pi*0.45^2)) * exp(-((x-2.8)^2)/(2*0.45^2))};

\addlegendentry{Hybrid Method}
\addlegendentry{Traditional Methods}

% Mean lines
\draw[blue, dashed] (3.12,0) -- (3.12,2.2);
\draw[red, dashed] (2.8,0) -- (2.8,0.9);

\end{axis}
\end{tikzpicture}
\subcaption{Performance distribution}
\end{subfigure}

\caption{Comprehensive Performance Analysis of Hybrid Optimization Framework}
\label{fig:performance_analysis}
\end{figure}