\begin{figure}[H]
\centering
\begin{tikzpicture}[scale=0.8]

% Define colors
\definecolor{internal}{RGB}{34, 139, 34}
\definecolor{external}{RGB}{220, 20, 60}
\definecolor{relevant}{RGB}{255, 140, 0}
\definecolor{irrelevant}{RGB}{128, 128, 128}

% Draw optimization domain boundary
\draw[thick, black] (0,0) rectangle (10,8);
\node[below] at (5,0) {Optimization Domain ($\Omega$)};

% Draw internal turbines (optimizable)
\foreach \x/\y in {2/2, 3.5/3, 5/1.5, 6.5/4, 8/2.5, 4/5.5, 7/6}
    \filldraw[internal] (\x,\y) circle (0.15);

% Legend for internal turbines
\filldraw[internal] (0.5,7.5) circle (0.1);
\node[right] at (0.7,7.5) {\small Internal Turbines (Weight = 1)};

% Draw external turbines - relevant (upwind for 240° wind)
\foreach \x/\y in {-1/3, -0.5/5, 11/4, 11.5/6, 12/2}
    \filldraw[relevant] (\x,\y) circle (0.15);

% Legend for relevant external turbines
\filldraw[relevant] (0.5,7) circle (0.1);
\node[right] at (0.7,7) {\small Relevant External (Weight = 0, Upwind)};

% Draw external turbines - irrelevant (downwind for 240° wind)
\foreach \x/\y in {-1/1, 12/7, 11/0.5}
    \filldraw[irrelevant] (\x,\y) circle (0.15);

% Legend for irrelevant external turbines
\filldraw[irrelevant] (0.5,6.5) circle (0.1);
\node[right] at (0.7,6.5) {\small Irrelevant External (Filtered Out)};

% Draw wind direction arrow (240°)
\draw[->, very thick, blue] (1,1) -- +(-2.5,-2) node[below left] {Wind: 240$^\circ$};

% Draw 20km radius circles from domain center
\draw[dashed, gray] (5,4) circle (6);
\node[gray] at (9,8.5) {\small 20 km radius};

% Wake zones from relevant external turbines
\foreach \x/\y in {-1/3, -0.5/5, 11/4, 11.5/6}
{
    \fill[blue!20, opacity=0.3] (\x,\y) -- ++(2.5,2) -- ++(0.5,0) -- ++(-2.5,-2) -- cycle;
}

% Distance constraints visualization
\draw[<->, red] (2,2) -- (3.5,3) node[midway, above] {\tiny $d_{min}$};

% Coordinate system
\draw[->] (-2.5,-1) -- (-1.5,-1) node[right] {X (East)};
\draw[->] (-2.5,-1) -- (-2.5,0) node[above] {Y (North)};

% External turbine filtering equation
\node[draw, rectangle, fill=yellow!10, text width=4cm] at (5,9.5) {
    \small $Relevant_{\theta} = \{j \in External :$ \\
    \small $IsUpwind(j,\theta) \land Distance(j) < 20km\}$
};

\end{tikzpicture}
\caption{External Turbine Integration and Wind Direction-Based Filtering}
\label{fig:external_turbine_system}
\end{figure}