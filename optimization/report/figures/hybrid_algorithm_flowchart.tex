\begin{figure}[H]
\centering
\begin{tikzpicture}[
    node distance=1.5cm,
    every node/.style={align=center},
    process/.style={rectangle, draw, fill=blue!20, text width=2.5cm, minimum height=1cm},
    decision/.style={diamond, draw, fill=yellow!20, text width=2cm, minimum height=1cm},
    start/.style={circle, draw, fill=green!20, text width=2cm, minimum height=0.8cm},
    end/.style={circle, draw, fill=red!20, text width=2cm, minimum height=0.8cm},
    arrow/.style={->, thick}
]

% Simplified flowchart for compatibility
\node[start] (start) at (0,8) {Problem\\Input};
\node[process] (complexity) at (0,6.5) {Assess Problem\\Complexity};
\node[process] (allocation) at (0,5) {Allocate Generation Budget};
\node[process] (init) at (0,3.5) {Smart Initialization};
\node[process] (ga) at (0,2) {Stage 1: GA\\(Exploration)};
\node[process] (de) at (3,2) {Stage 2: DE\\(Refinement)};
\node[process] (pso) at (0,0.5) {Stage 3: PSO\\(Convergence)};
\node[process] (final) at (0,-1) {Final Solution};
\node[end] (end) at (0,-2.5) {Optimized Layout};

% Arrows
\draw[arrow] (start) -- (complexity);
\draw[arrow] (complexity) -- (allocation);
\draw[arrow] (allocation) -- (init);
\draw[arrow] (init) -- (ga);
\draw[arrow] (ga) -- (de);
\draw[arrow] (de) -- (pso);
\draw[arrow] (pso) -- (final);
\draw[arrow] (final) -- (end);

\end{tikzpicture}
\caption{Hybrid Multi-Stage Optimization Framework Flowchart}
\label{fig:hybrid_flowchart}
\end{figure}