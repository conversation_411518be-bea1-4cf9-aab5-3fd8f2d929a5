\begin{figure}[H]
\centering
\begin{subfigure}[b]{0.48\textwidth}
\centering
\begin{tikzpicture}[scale=0.6]

% Voronoi initialization
\draw[thick] (0,0) rectangle (8,6);
\node[below] at (4,0) {Voronoi Tessellation Initialization};

% Voronoi seed points
\foreach \x/\y in {1.5/1.5, 3/3.5, 5.5/1, 6.5/4.5, 2/5, 7/2.5, 4.5/4.5}
    \filldraw[blue] (\x,\y) circle (0.1);

% Voronoi cells (simplified)
\draw[gray, thin] (0,0) -- (2.25,2.25) -- (4,1.75) -- (8,0);
\draw[gray, thin] (0,2.25) -- (2.25,2.25) -- (1.5,6);
\draw[gray, thin] (2.25,2.25) -- (4,3.5) -- (1.5,6);
\draw[gray, thin] (4,1.75) -- (6,2.75) -- (8,0);
\draw[gray, thin] (4,1.75) -- (4,3.5) -- (6,2.75);
\draw[gray, thin] (4,3.5) -- (5.5,6) -- (8,6) -- (6,2.75);
\draw[gray, thin] (1.5,6) -- (4,3.5) -- (5.5,6);

% Turbines at cell centers
\foreach \x/\y in {1.5/1.5, 3/3.5, 5.5/1, 6.5/4.5, 2/5, 7/2.5, 4.5/4.5}
    \filldraw[red] (\x,\y) circle (0.15);

\end{tikzpicture}
\subcaption{Voronoi-based spatial distribution}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.48\textwidth}
\centering
\begin{tikzpicture}[scale=0.6]

% Wind-rose weighted initialization
\draw[thick] (0,0) rectangle (8,6);
\node[below] at (4,0) {Wind-Rose Weighted Initialization};

% Dominant wind direction (240°)
\draw[->, very thick, blue] (1,1) -- +(-1.5,-1) node[below left] {\small 240$^\circ$};

% Grid aligned perpendicular to wind (staggered)
% Row 1
\foreach \x in {1.5, 4, 6.5}
    \filldraw[green!70!black] (\x,1.5) circle (0.15);

% Row 2 (offset)
\foreach \x in {2.75, 5.25}
    \filldraw[green!70!black] (\x,3) circle (0.15);

% Row 3
\foreach \x in {1.5, 4, 6.5}
    \filldraw[green!70!black] (\x,4.5) circle (0.15);

% Spacing annotations
\draw[<->, gray] (1.5,0.8) -- (4,0.8) node[midway, below] {\tiny 3D};
\draw[<->, gray] (0.8,1.5) -- (0.8,3) node[midway, left] {\tiny 8D};

% Wind direction lines
\draw[dashed, blue!50] (0,0) -- (8,6);
\draw[dashed, blue!50] (0,1.5) -- (6.5,6);
\draw[dashed, blue!50] (1.5,0) -- (8,4.5);

\end{tikzpicture}
\subcaption{Wind-aligned grid with staggered rows}
\end{subfigure}

\vspace{1cm}

\begin{subfigure}[b]{0.48\textwidth}
\centering
\begin{tikzpicture}[scale=0.6]

% Random initialization
\draw[thick] (0,0) rectangle (8,6);
\node[below] at (4,0) {Random Initialization (Baseline)};

% Random turbine positions
\foreach \x/\y in {0.8/2.1, 2.2/4.7, 3.8/1.3, 5.1/3.9, 6.7/2.4, 1.9/5.2, 7.1/4.1, 4.2/0.7}
    \filldraw[orange] (\x,\y) circle (0.15);

% Constraint violations highlighted
\draw[red, thick] (0.8,2.1) circle (0.4);
\draw[red, thick] (2.2,4.7) circle (0.4);
\node[red, font=\small, align=center] at (0.8,1.4) {Constraint\\Violation};

\end{tikzpicture}
\subcaption{Random initialization with constraint issues}
\end{subfigure}
\hfill
\begin{subfigure}[b]{0.48\textwidth}
\centering
\begin{tikzpicture}[scale=0.6]

% Performance comparison chart
\begin{axis}[
    width=6cm,
    height=4cm,
    xlabel={Generations},
    ylabel={Best Fitness},
    legend pos=south east,
    grid=major,
    legend style={font=\tiny}
]

\addplot[blue, thick] coordinates {
    (0,0.7) (10,0.75) (20,0.82) (30,0.86) (40,0.89) (50,0.91)
};
\addlegendentry{Voronoi}

\addplot[green!70!black, thick] coordinates {
    (0,0.72) (10,0.78) (20,0.84) (30,0.88) (40,0.90) (50,0.92)
};
\addlegendentry{Wind-Rose}

\addplot[orange, thick] coordinates {
    (0,0.65) (10,0.68) (20,0.75) (30,0.79) (40,0.83) (50,0.86)
};
\addlegendentry{Random}

\end{axis}
\end{tikzpicture}
\subcaption{Convergence comparison}
\end{subfigure}

\caption{Advanced Initialization Strategies for Wind Farm Layout Optimization}
\label{fig:initialization_strategies}
\end{figure}