This is BibTeX, Version 0.99d (TeX Live 2023/Debian)
Capacity: max_strings=200000, hash_size=200000, hash_prime=170003
The top-level auxiliary file: wind_farm_optimization_report.aux
The style file: apalike.bst
I found no \citation commands---while reading file wind_farm_optimization_report.aux
Database file #1: references.bib
You've used 0 entries,
            1935 wiz_defined-function locations,
            471 strings with 3720 characters,
and the built_in function-call counts, 17 in all, are:
= -- 0
> -- 0
< -- 0
+ -- 0
- -- 0
* -- 0
:= -- 7
add.period$ -- 0
call.type$ -- 0
change.case$ -- 0
chr.to.int$ -- 0
cite$ -- 0
duplicate$ -- 0
empty$ -- 1
format.name$ -- 0
if$ -- 1
int.to.chr$ -- 1
int.to.str$ -- 0
missing$ -- 0
newline$ -- 3
num.names$ -- 0
pop$ -- 0
preamble$ -- 1
purify$ -- 0
quote$ -- 0
skip$ -- 1
stack$ -- 0
substring$ -- 0
swap$ -- 0
text.length$ -- 0
text.prefix$ -- 0
top$ -- 0
type$ -- 0
warning$ -- 0
while$ -- 0
width$ -- 0
write$ -- 2
(There was 1 error message)
