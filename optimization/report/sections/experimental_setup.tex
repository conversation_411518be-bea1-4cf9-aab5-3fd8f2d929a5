\subsection{Test Configuration and Wind Farm Specifications}

\subsubsection{Wind Resource Characterization}

The experimental validation employs realistic wind resource data spanning multiple years of measurements. The wind rose comprises 72 directional bins (5$^\circ$ resolution) and 319 wind speed bins with frequencies ranging from 0.25 m/s to 25+ m/s. Key characteristics include:

\begin{table}[H]
\centering
\caption{Wind Resource Characteristics}
\begin{tabular}{@{}ll@{}}
\toprule
Parameter & Value \\
\midrule
Dominant Wind Direction & 240$^\circ$ (Southwest) \\
Mean Wind Speed & 8.2 m/s \\
Wind Speed Range & 0.25 - 25.0 m/s \\
Directional Bins & 72 (5$^\circ$ resolution) \\
Speed Bins & 319 \\
Data Period & 8760 hours (annual) \\
Weibull Shape Parameter & 2.1 \\
Weibull Scale Parameter & 9.3 m/s \\
\bottomrule
\end{tabular}
\end{table}

The wind resource exhibits typical characteristics of coastal/offshore environments with strong southwest prevailing winds and significant seasonal variability. This configuration provides a challenging optimization scenario with pronounced directional preferences requiring careful wake management.

\subsubsection{Turbine Specifications and Power Characteristics}

The test configuration incorporates multiple turbine types representative of modern utility-scale installations:

\begin{table}[H]
\centering
\caption{Turbine Technical Specifications}
\begin{tabular}{@{}lccc@{}}
\toprule
Parameter & Zeevonk East & Zeevonk West & NREL 5MW \\
\midrule
Rated Power & 3.6 MW & 3.6 MW & 5.0 MW \\
Rotor Diameter & 107 m & 107 m & 126 m \\
Hub Height & 90 m & 90 m & 90 m \\
Cut-in Speed & 3.0 m/s & 3.0 m/s & 3.0 m/s \\
Rated Speed & 13.0 m/s & 13.0 m/s & 11.4 m/s \\
Cut-out Speed & 25.0 m/s & 25.0 m/s & 25.0 m/s \\
Thrust Coefficient (Peak) & 0.85 & 0.85 & 0.88 \\
Power Coefficient (Peak) & 0.47 & 0.47 & 0.49 \\
\bottomrule
\end{tabular}
\end{table}

Power and thrust curves are implemented through high-resolution lookup tables with linear interpolation for intermediate wind speeds. The turbine models incorporate realistic operational characteristics including variable pitch control and aerodynamic efficiency variations.

\subsubsection{FLORIS Wake Modeling Configuration}

The FLORIS framework is configured with the Navarro Scout wake model, providing enhanced accuracy for offshore and complex terrain applications:

\begin{table}[H]
\centering
\caption{FLORIS Wake Model Parameters}
\begin{tabular}{@{}ll@{}}
\toprule
Parameter & Value \\
\midrule
Wake Model & Navarro Scout \\
Velocity Model & Jensen (enhanced) \\
Deflection Model & Gauss \\
Combination Model & Sosfs \\
Atmospheric Density & 1.225 kg/m$^3$ \\
Reference Height & 90 m \\
Surface Roughness & 0.0001 m (offshore) \\
Turbulence Intensity & 0.06 \\
Wind Shear Exponent & 0.12 \\
Wake Expansion Rate & 0.05 \\
\bottomrule
\end{tabular}
\end{table}

The wake model parameters are calibrated for offshore conditions with low surface roughness and moderate turbulence intensity. The Jensen velocity model with Gaussian deflection provides a balance between computational efficiency and physical accuracy suitable for optimization applications.

\subsection{Optimization Problem Configurations}

\subsubsection{Test Case Scenarios}

Three primary test scenarios are employed to validate the optimization framework:

\textbf{Scenario 1: Internal-Only Optimization}
\begin{itemize}
    \item 10 internal turbines to be optimized
    \item No external turbine constraints
    \item Focus on algorithm performance and convergence characteristics
    \item Baseline for comparative analysis
\end{itemize}

\textbf{Scenario 2: External Turbine Integration}
\begin{itemize}
    \item 10 internal turbines (optimizable)
    \item 5-15 external turbines (fixed positions)
    \item Distance-based filtering with 20 km threshold
    \item Wind direction-specific external turbine relevance
\end{itemize}

\textbf{Scenario 3: Large-Scale Optimization}
\begin{itemize}
    \item 25-50 internal turbines
    \item 10-30 external turbines
    \item Complex geometric boundaries
    \item Multiple constraint types and optimization objectives
\end{itemize}

\subsubsection{Boundary Constraints and Geometric Limitations}

The optimization domain is defined by realistic geometric constraints representing typical offshore wind development areas:

\begin{table}[H]
\centering
\caption{Geometric Constraint Parameters}
\begin{tabular}{@{}ll@{}}
\toprule
Constraint Type & Specification \\
\midrule
Domain Area & 25 km$^2$ (5 km $\times$ 5 km) \\
Minimum Turbine Separation & 3481.5 m (3.25D) \\
Boundary Buffer Zone & 500 m \\
Exclusion Zones & Navigation channels, cables \\
Water Depth Range & 20-50 m \\
Seabed Slope Limitation & < 2$^\circ$ \\
Environmental Setbacks & 1 km from sensitive areas \\
\bottomrule
\end{tabular}
\end{table}

Boundary constraints are implemented using high-precision computational geometry with UTM coordinate systems. Complex polygonal boundaries account for irregular coastlines, shipping lanes, and environmental protection zones typical of real offshore developments.

\subsection{Algorithm Configuration and Parameter Settings}

\subsubsection{Optimization Algorithm Parameters}

The hybrid optimization framework employs carefully tuned parameters based on preliminary sensitivity analysis:

\begin{table}[H]
\centering
\caption{Algorithm Configuration Parameters}
\begin{tabular}{@{}lccc@{}}
\toprule
Parameter & GA Stage & DE Stage & PSO Stage \\
\midrule
Population Size & 32 & 32 & 32 \\
Crossover Probability & 0.9 & - & - \\
Mutation Probability & 0.1 & - & - \\
Differential Weight (F) & - & 0.5-1.0 & - \\
Crossover Rate (CR) & - & 0.3-0.9 & - \\
Inertia Weight (w) & - & - & 0.1-0.9 \\
Cognitive Parameter (c$_1$) & - & - & 2.0 \\
Social Parameter (c$_2$) & - & - & 2.0 \\
Selection Pressure & 2 & - & - \\
Elite Preservation & 5\% & 5\% & 5\% \\
\bottomrule
\end{tabular}
\end{table}

Parameters are dynamically adjusted during optimization based on performance metrics and convergence characteristics. Adaptive parameter control ensures robust performance across diverse problem configurations.

\subsubsection{Termination Criteria and Convergence Settings}

Multiple termination criteria ensure reliable convergence detection:

\begin{table}[H]
\centering
\caption{Termination and Convergence Criteria}
\begin{tabular}{@{}ll@{}}
\toprule
Criterion & Threshold \\
\midrule
Maximum Generations & 10-100 (problem-dependent) \\
Function Tolerance & $1 \times 10^{-6}$ \\
Variable Tolerance & $1 \times 10^{-8}$ \\
Constraint Violation Tolerance & $1 \times 10^{-6}$ \\
Stagnation Generations & 20 \\
Improvement Threshold & $1 \times 10^{-4}$ \\
Maximum Evaluations & 100,000 \\
Wall Clock Time Limit & 3600 seconds \\
\bottomrule
\end{tabular}
\end{table}

\subsection{Computational Environment and Performance Metrics}

\subsubsection{Hardware and Software Configuration}

All experiments are conducted on a high-performance computing cluster with standardized configurations:

\begin{table}[H]
\centering
\caption{Computational Environment Specifications}
\begin{tabular}{@{}ll@{}}
\toprule
Component & Specification \\
\midrule
CPU & Intel Xeon Gold 6248R (3.0 GHz) \\
Cores per Node & 48 cores \\
Memory & 192 GB DDR4-2933 \\
Storage & NVMe SSD (local), Lustre (shared) \\
Network & InfiniBand HDR (200 Gb/s) \\
Operating System & CentOS Linux 8 \\
Python Version & 3.13 \\
PyMoo Version & 0.6.0+ \\
NumPy Version & 1.24+ \\
SciPy Version & 1.10+ \\
\bottomrule
\end{tabular}
\end{table}

\subsubsection{Performance Evaluation Metrics}

Comprehensive performance evaluation employs multiple metrics across different dimensions:

\textbf{Optimization Quality Metrics:}
\begin{itemize}
    \item Annual Energy Production (AEP) improvement
    \item Wake loss reduction percentage
    \item Constraint violation levels
    \item Solution feasibility rate
    \item Pareto front quality (for multi-objective cases)
\end{itemize}

\textbf{Computational Performance Metrics:}
\begin{itemize}
    \item Total optimization time
    \item Convergence rate (generations to target)
    \item Parallel efficiency and scalability
    \item Memory utilization patterns
    \item Cache hit rates and evaluation efficiency
\end{itemize}

\textbf{Algorithm Robustness Metrics:}
\begin{itemize}
    \item Solution diversity maintenance
    \item Parameter sensitivity analysis
    \item Repeatability across multiple runs
    \item Fault tolerance and error recovery
    \item Adaptability to problem variations
\end{itemize}

\subsection{Validation and Benchmarking Framework}

\subsubsection{Baseline Comparison Methods}

The hybrid optimization framework is validated against established baseline approaches:

\begin{enumerate}
    \item \textbf{Single Algorithm Methods}: Pure GA, DE, PSO, and NSGA-II implementations
    \item \textbf{Grid-based Layouts}: Regular and staggered grid arrangements
    \item \textbf{Random Search}: Monte Carlo sampling with constraint handling
    \item \textbf{Literature Benchmarks}: Published optimization results for similar problems
\end{enumerate}

\subsubsection{Statistical Analysis and Repeatability}

Each optimization configuration is executed across 30 independent runs to ensure statistical significance. Statistical analysis includes:

\begin{itemize}
    \item Mean and standard deviation of objective function values
    \item Convergence time distributions and confidence intervals
    \item Non-parametric statistical tests (Wilcoxon, Kruskal-Wallis)
    \item Effect size calculations for performance improvements
    \item Bootstrap confidence intervals for robustness assessment
\end{itemize}

This comprehensive experimental framework ensures reliable validation of the optimization methodology while providing detailed insights into performance characteristics, computational efficiency, and practical applicability for real-world wind farm development scenarios.