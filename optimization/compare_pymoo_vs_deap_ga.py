#!/usr/bin/env python3
"""
Comparison Script: PyMoo GA vs DEAP GA for Internal Layout Optimization
Based on opt-pymoo-windrose-freq-ts_corrected.py
Compares performance between PyMoo and DEAP genetic algorithms for internal-only wind farm layout optimization
"""

import inspect
import sys
import copy
import os
import re
import shutil
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from multiprocessing import Process
from numpy import genfromtxt
from scipy.spatial.distance import cdist
from scipy.interpolate import NearestNDInterpolator
from datetime import datetime
from time import perf_counter as timerpc
from shapely.geometry import Polygon, LineString
import matplotlib.ticker as ticker
import matplotlib.patches as patches
from matplotlib.lines import Line2D
from pathlib import Path
import json
import warnings
warnings.filterwarnings('ignore')

# Project-specific imports
import params as pr
import optimizer_params as optimizer_etc
import wind_dir_dominant as winddir

# Import core utilities
from utilities import (
    sanitize_name,
    GetAEPParallel,
    memory_footprint,
    write_log,
    custom_round,
    ts_to_freq_df
)

# Set up work directory and paths
workDir = str(Path(__file__).parent.absolute())
print(f"GA Comparison Script Path: {workDir}")
OverAllProgress = f"{workDir}/OverAllProgress.txt"

# Set FLORIS paths
floris_path = f"{workDir}/FLORIS_311_VF1_Operational"
turb_lib_path = f"{floris_path}/core/turbine_library"
sys.path.append(floris_path)

try:
    import oyaml as yaml
except ImportError:
    import yaml

from core.tools import FlorisInterface
from core.tools.optimization.layout_optimization.LayoutOptimizationPymoo import LayoutOptimizationPymoo

# Import DEAP components for comparison
from deap_algorithms_external import create_deap_algorithm


class GAComparisonResults:
    """Class to store and manage comparison results"""
    
    def __init__(self):
        self.results = {
            'pymoo_ga': {},
            'deap_ga': {},
            'comparison': {}
        }
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def add_result(self, framework, algorithm, result_data):
        """Add optimization result"""
        key = f"{framework}_{algorithm}".lower()
        self.results[key] = result_data
        
    def save_results(self, filename=None):
        """Save comparison results to JSON file"""
        if filename is None:
            filename = f"ga_comparison_results_{self.timestamp}.json"
            
        # Convert numpy arrays to lists for JSON serialization
        serializable_results = {}
        for key, value in self.results.items():
            if isinstance(value, dict):
                serializable_results[key] = {}
                for subkey, subvalue in value.items():
                    if isinstance(subvalue, np.ndarray):
                        serializable_results[key][subkey] = subvalue.tolist()
                    elif isinstance(subvalue, (np.integer, np.floating)):
                        serializable_results[key][subkey] = float(subvalue)
                    else:
                        serializable_results[key][subkey] = subvalue
            else:
                serializable_results[key] = value
                
        with open(filename, 'w') as f:
            json.dump(serializable_results, f, indent=2, default=str)
        print(f"📁 Results saved to: {filename}")
        
    def generate_comparison_report(self):
        """Generate detailed comparison report"""
        report = []
        report.append("=" * 80)
        report.append("GA COMPARISON REPORT")
        report.append("=" * 80)
        
        if 'pymoo_ga' in self.results and 'deap_ga' in self.results:
            pymoo = self.results['pymoo_ga']
            deap = self.results['deap_ga']
            
            report.append(f"\n📊 PERFORMANCE COMPARISON:")
            report.append(f"PyMoo GA:")
            report.append(f"  Best AEP: {pymoo.get('best_aep', 'N/A'):.3f} GWh")
            report.append(f"  Runtime: {pymoo.get('total_time', 'N/A'):.2f} seconds")
            report.append(f"  Evaluations: {pymoo.get('n_evals', 'N/A')}")
            
            report.append(f"\nDEAP GA:")
            report.append(f"  Best AEP: {deap.get('best_aep', 'N/A'):.3f} GWh")
            report.append(f"  Runtime: {deap.get('total_time', 'N/A'):.2f} seconds")
            report.append(f"  Evaluations: {deap.get('n_evals', 'N/A')}")
            
            # Calculate performance differences
            if all(key in pymoo and key in deap for key in ['best_aep', 'total_time', 'n_evals']):
                aep_diff = ((deap['best_aep'] - pymoo['best_aep']) / pymoo['best_aep']) * 100
                time_diff = ((deap['total_time'] - pymoo['total_time']) / pymoo['total_time']) * 100
                eval_diff = ((deap['n_evals'] - pymoo['n_evals']) / pymoo['n_evals']) * 100
                
                report.append(f"\n📈 RELATIVE PERFORMANCE (DEAP vs PyMoo):")
                report.append(f"  AEP Difference: {aep_diff:+.2f}%")
                report.append(f"  Runtime Difference: {time_diff:+.2f}%")
                report.append(f"  Evaluation Difference: {eval_diff:+.2f}%")
                
                # Determine winner
                if aep_diff > 1.0:  # DEAP significantly better
                    winner = "DEAP GA"
                elif aep_diff < -1.0:  # PyMoo significantly better
                    winner = "PyMoo GA"
                else:
                    winner = "Similar performance"
                    
                report.append(f"\n🏆 WINNER: {winner}")
        
        report_text = "\n".join(report)
        print(report_text)
        
        # Save report to file
        report_filename = f"ga_comparison_report_{self.timestamp}.txt"
        with open(report_filename, 'w') as f:
            f.write(report_text)
        print(f"📄 Report saved to: {report_filename}")
        
        return report_text


def setup_floris_and_data():
    """Setup FLORIS interface and data (based on corrected script)"""
    print("\n🔄 Step 1: Setting up FLORIS and data...")
    
    # Create simple wind rose data for testing since timeseries.txt has different format
    # Use a simplified wind rose with common wind conditions
    wind_directions = np.array([0., 45., 90., 135., 180., 225., 270., 315.])
    wind_speeds = np.array([6., 8., 10., 12., 14.])
    
    # Create frequency matrix (simplified uniform distribution)
    freq = np.ones((len(wind_directions), len(wind_speeds))) / (len(wind_directions) * len(wind_speeds))
    
    print(f"Wind rose data created: {len(wind_directions)} directions, {len(wind_speeds)} speeds")
    
    # Process turbine definitions with boundary conditions
    input_dir = "Input"
    for file in os.listdir(input_dir):
        if 'turb.csv' in file:
            turb_df = pd.read_csv(os.path.join(input_dir, file))
            safe_name = sanitize_name(str(turb_df['name'][0]))
            
            # Add boundary conditions for wind speed
            new_rows = pd.DataFrame({
                'ws': [-10.0, 60.0],
                'cp': [0, 0],
                'p': [0, 0],
                'ct': [0, 0],
                'hh': [None, None],
                'dia': [None, None],
                'name': [None, None]
            })
            
            # Ensure consistent dtypes
            for col in turb_df.columns:
                if col in new_rows.columns:
                    new_rows[col] = new_rows[col].astype(turb_df[col].dtype, errors='ignore')
            
            # Concatenate and sort
            turb_df = pd.concat([turb_df, new_rows], ignore_index=True)
            turb_df = turb_df.sort_values(by='ws')
            
            new_turb = {
                'turbine_type': safe_name,
                'generator_efficiency': float(1.0),
                'hub_height': float(turb_df['hh'][0]),
                'pP': float(1.88),
                'pT': float(1.88),
                'rotor_diameter': float(turb_df['dia'][0]),
                'TSR': float(8.0),
                'power_thrust_table': {
                    'power': turb_df['cp'].tolist(),
                    'thrust': turb_df['ct'].tolist(),
                    'wind_speed': turb_df['ws'].tolist()
                }
            }
            
            outname = f'{turb_lib_path}/{safe_name}.yaml'
            with open(outname, 'w') as yaml_file:
                yaml.dump(new_turb, yaml_file)
    
    # Load layout and setup FLORIS
    layout = pd.read_csv("Input/Initial.layout.csv")
    layout = layout.rename(columns={'easting':'x','northing':'y'})
    layout['turb'] = layout['turb'].apply(sanitize_name)
    
    # Filter to internal turbines only for this comparison
    internal_layout = layout[layout['external'] == False].copy()
    print(f"Internal turbines for optimization: {len(internal_layout)}")
    
    # Initialize FLORIS
    input_config = pr.FLORIS_CONFIG
    fi = FlorisInterface(input_config)
    
    fi.reinitialize(layout=(internal_layout['x'], internal_layout['y']),
                    turbine_type=internal_layout['turb'],
                    wind_directions=wind_directions,
                    wind_speeds=wind_speeds)
    
    print(f"FLORIS setup complete:")
    print(f"  Wind directions: {fi.floris.flow_field.n_wind_directions}")
    print(f"  Wind speeds: {fi.floris.flow_field.n_wind_speeds}")
    print(f"  Turbines: {len(internal_layout)}")
    
    # Set reference height
    ref_ht = fi.floris.farm.hub_heights[0]  # Use first turbine height
    fi.floris.flow_field.reference_wind_height = ref_ht
    
    # Calculate baseline AEP
    fi.calculate_wake()
    baseline_aep = fi.get_farm_AEP(freq=freq) / 1E9  # Convert to GWh
    print(f"Baseline AEP: {baseline_aep:.3f} GWh")
    
    return fi, freq, internal_layout, baseline_aep


def run_pymoo_ga_optimization(fi, freq, layout, baseline_aep):
    """Run PyMoo GA optimization"""
    print("\n🧬 Running PyMoo GA Optimization...")
    
    # Load boundaries
    boundaries_df = pd.read_csv(pr.boundariesFile)
    boundaries = list(zip(boundaries_df['X'], boundaries_df['Y']))
    
    start_time = timerpc()
    
    # Create PyMoo optimizer with reduced parameters for quick testing
    test_generations = min(pr.MAXGEN, 5)  # Limit to 5 generations for quick test
    #test_pop_size = min(pr.PopSize, 16)   # Limit to 16 population for quick test
    test_pop_size = pr.PopSize   # Limit to 16 population for quick test
    
    pymoo_optimizer = LayoutOptimizationPymoo(
        fi,
        boundaries,
        min_dist=optimizer_etc.min_dist,
        freq=freq,
        bnds=None,
        optOptions=None,
        problem_name="PyMoo_GA_Comparison",
        n_gen=test_generations,
        pop_size=test_pop_size,
        ftol=pr.ftol
    )
    
    # Run optimization
    result = pymoo_optimizer.optimize()
    
    total_time = timerpc() - start_time
    
    # Extract optimized layout and calculate final AEP
    opt_x, opt_y = pymoo_optimizer.get_optimized_locs()
    fi.reinitialize(layout=(opt_x, opt_y))
    final_aep = fi.get_farm_AEP(freq=freq) / 1E9  # Convert to GWh
    
    results = {
        'framework': 'PyMoo',
        'algorithm': 'GA',
        'best_aep': final_aep,
        'baseline_aep': baseline_aep,
        'aep_improvement': final_aep - baseline_aep,
        'aep_improvement_pct': ((final_aep - baseline_aep) / baseline_aep) * 100,
        'total_time': total_time,
        'n_evals': getattr(pymoo_optimizer, 'evaluator', {}).get('n_eval', test_generations * test_pop_size),
        'final_layout_x': opt_x,
        'final_layout_y': opt_y,
        'parameters': {
            'pop_size': pr.PopSize,
            'n_gen': pr.MAXGEN,
            'crossover_prob': pr.pCross_real,
            'eta_c': pr.eta_c,
            'eta_m': pr.eta_m
        }
    }
    
    print(f"✅ PyMoo GA completed:")
    print(f"  Final AEP: {final_aep:.3f} GWh")
    print(f"  Improvement: {results['aep_improvement_pct']:.2f}%")
    print(f"  Runtime: {total_time:.2f} seconds")
    
    # For comparison with DEAP normalization
    normalized_objective = final_aep / baseline_aep
    print(f"  Normalized objective: {normalized_objective:.6f} (baseline=1.0)")
    
    return results


def run_deap_ga_optimization(fi, freq, layout, baseline_aep):
    """Run DEAP GA optimization"""
    print("\n🧬 Running DEAP GA Optimization...")
    
    # Create a problem wrapper for DEAP with proper normalization
    class DEAPProblemWrapper:
        def __init__(self, fi, freq, layout, boundaries):
            self.fi = fi
            self.freq = freq
            self.layout = layout
            self.boundaries = boundaries
            self.n_turbines = len(layout)
            self.n_var = 2 * self.n_turbines  # x, y coordinates (normalized [0,1])
            self.n_obj = 1
            self.n_constr = 2 * self.n_turbines  # Space + boundary constraints (matching PyMoo)
            
            # Setup boundary limits for normalization
            boundaries_df = pd.read_csv(pr.boundariesFile)
            self.xmin = boundaries_df['X'].min()
            self.xmax = boundaries_df['X'].max()
            self.ymin = boundaries_df['Y'].min()
            self.ymax = boundaries_df['Y'].max()
            
            # Calculate baseline AEP for objective normalization
            self.fi.reinitialize(layout=(layout['x'], layout['y']))
            self.baseline_aep = self.fi.get_farm_AEP(freq=self.freq) / 1E9  # GWh
            print(f"   DEAP Baseline AEP: {self.baseline_aep:.3f} GWh (for normalization)")
            
            # Create initial solution (x0) in normalized coordinates - matching PyMoo
            self.x0 = np.concatenate([
                self._norm(layout['x'].values, self.xmin, self.xmax),
                self._norm(layout['y'].values, self.ymin, self.ymax)
            ])
            print(f"   Initial solution (x0) created with normalized coordinates [0,1]")
            
            # Pre-compute boundary polygon/line for efficiency (matching PyMoo)
            self._prepare_boundaries()
            
        def _prepare_boundaries(self):
            """Pre-process boundaries for efficient distance calculations - matching PyMoo"""
            from shapely.geometry import Polygon
            try:
                self._boundary_polygon = Polygon(self.boundaries)
                self._boundary_line = self._boundary_polygon.boundary
            except Exception as e:
                raise ValueError(f"Failed to convert boundaries to Polygon: {e}")
            
        def _norm(self, val, x1, x2):
            """Normalize values to [0,1] - matching PyMoo base class"""
            return (val - x1) / (x2 - x1)
            
        def _unnorm(self, val, x1, x2):
            """Unnormalize values from [0,1] - matching PyMoo base class"""
            return np.array(val) * (x2 - x1) + x1
            
        def _space_constraint(self, x_in, rho=500):
            """Minimum distance constraint matching PyMoo implementation exactly"""
            x = [
                self._unnorm(valx, self.xmin, self.xmax)
                for valx in x_in[0: self.n_turbines]
            ]
            y = [
                self._unnorm(valy, self.ymin, self.ymax)
                for valy in x_in[self.n_turbines: 2 * self.n_turbines]
            ]
        
            # Calculate distances between turbines
            locs = np.vstack((x, y)).T
            distances = cdist(locs, locs)
            np.fill_diagonal(distances, np.inf)  # Set diagonal elements to infinity
        
            # Check if any distance violates the minimum distance constraint
            violated_distances = distances < optimizer_etc.min_dist
            g = np.sum(violated_distances, axis=1)
        
            return g

        def _distance_from_boundaries(self, x_in):
            """Boundary constraint matching PyMoo implementation exactly"""
            from shapely.geometry import Point
            x = [
                self._unnorm(valx, self.xmin, self.xmax)
                for valx in x_in[0 : self.n_turbines]
            ]
            y =  [
                self._unnorm(valy, self.ymin, self.ymax)
                for valy in x_in[self.n_turbines : 2 * self.n_turbines]
            ]
            boundary_con = np.zeros(self.n_turbines)
            for i in range(self.n_turbines):
                loc = Point(x[i], y[i])
                boundary_con[i] = loc.distance(self._boundary_line)
                if self._boundary_polygon.contains(loc) is True:
                    boundary_con[i] *=  -1.0
                else:
                    boundary_con[i] *=   1.0

            return boundary_con
            
        def _evaluate(self, X, out):
            """Evaluate population for DEAP with normalized coordinates [0,1] - matching PyMoo exactly"""
            n_samples = X.shape[0] if X.ndim > 1 else 1
            if X.ndim == 1:
                X = X.reshape(1, -1)
                
            F = np.zeros((n_samples, 1))
            G = np.zeros((n_samples, self.n_constr))
            
            for i in range(n_samples):
                # Extract normalized coordinates [0,1]
                x_norm = X[i, :self.n_turbines]
                y_norm = X[i, self.n_turbines:]
                
                # Denormalize coordinates using base class method
                x_real = self._unnorm(x_norm, self.xmin, self.xmax)
                y_real = self._unnorm(y_norm, self.ymin, self.ymax)
                
                # Calculate AEP and normalize by baseline
                try:
                    self.fi.reinitialize(layout=(x_real, y_real))
                    aep = self.fi.get_farm_AEP(freq=self.freq) / 1E9  # GWh
                    # Normalize objective by baseline AEP (same as PyMoo approach)
                    normalized_aep = aep / self.baseline_aep
                    F[i, 0] = normalized_aep
                except:
                    F[i, 0] = 0.0  # Penalty for failed evaluations
                
                # Use exact PyMoo constraint functions 
                G[i, :self.n_turbines] = self._space_constraint(X[i])
                G[i, self.n_turbines:2 * self.n_turbines] = self._distance_from_boundaries(X[i])
            
            out["F"] = F
            out["G"] = G
    
    # Load boundaries
    boundaries_df = pd.read_csv(pr.boundariesFile)
    boundaries = list(zip(boundaries_df['X'], boundaries_df['Y']))
    
    # Create problem
    problem = DEAPProblemWrapper(fi, freq, layout, boundaries)
    
    start_time = timerpc()
    
    # Create DEAP optimizer with matching reduced parameters
    test_generations = min(pr.MAXGEN, 5)  # Limit to 5 generations for quick test  
    test_pop_size = min(pr.PopSize, 16)   # Limit to 16 population for quick test
    
    # Create initial population from x0 (matching PyMoo approach)
    initial_population = np.tile(problem.x0, (test_pop_size, 1))
    print(f"   Created initial population from x0: {initial_population.shape}")
    
    deap_optimizer = create_deap_algorithm(
        problem,
        algorithm_name="GA",
        pop_size=test_pop_size,
        n_gen=test_generations,
        n_workers=1,  # Use single worker for stability
        verbose=True,
        initial_population=initial_population  # Pass initial population
    )
    
    # Run optimization
    result = deap_optimizer.optimize()
    
    total_time = timerpc() - start_time
    
    # Extract best solution and calculate final AEP
    best_x = result['x']
    x_norm = best_x[:problem.n_turbines]  # Normalized coordinates [0,1]
    y_norm = best_x[problem.n_turbines:]  # Normalized coordinates [0,1]
    
    # Denormalize coordinates using base class method
    opt_x = problem._unnorm(x_norm, problem.xmin, problem.xmax)
    opt_y = problem._unnorm(y_norm, problem.ymin, problem.ymax)
    
    fi.reinitialize(layout=(opt_x, opt_y))
    final_aep = fi.get_farm_AEP(freq=freq) / 1E9  # Convert to GWh
    
    results = {
        'framework': 'DEAP',
        'algorithm': 'GA',
        'best_aep': final_aep,
        'baseline_aep': baseline_aep,
        'aep_improvement': final_aep - baseline_aep,
        'aep_improvement_pct': ((final_aep - baseline_aep) / baseline_aep) * 100,
        'total_time': total_time,
        'n_evals': result['n_evals'],
        'final_layout_x': opt_x,
        'final_layout_y': opt_y,
        'parameters': {
            'pop_size': test_pop_size,
            'n_gen': test_generations,
            'crossover_prob': 0.9,  # DEAP default
            'mutation_prob': 0.1,   # DEAP default
        },
        'convergence_history': result.get('convergence_history', {})
    }
    
    print(f"✅ DEAP GA completed:")
    print(f"  Final AEP: {final_aep:.3f} GWh")
    print(f"  Improvement: {results['aep_improvement_pct']:.2f}%")
    print(f"  Runtime: {total_time:.2f} seconds")
    print(f"  Normalized objective: {float(result['f'][0]):.6f} (should be ~1.0 for baseline)")
    
    # Validation: Check that normalization is working correctly
    expected_normalized = final_aep / problem.baseline_aep
    actual_normalized = -float(result['f'][0])  # Convert back from maximization
    print(f"  Normalization check: Expected={expected_normalized:.6f}, DEAP returned={actual_normalized:.6f}")
    
    return results


def create_comparison_plots(comparison_results):
    """Create comprehensive comparison visualization plots"""
    print("\n📊 Creating comparison plots...")
    
    pymoo_results = comparison_results.results.get('pymoo_ga', {})
    deap_results = comparison_results.results.get('deap_ga', {})
    
    if not pymoo_results or not deap_results:
        print("Warning: Missing results for plotting")
        return
    
    # Create comparison plots with convergence history
    fig = plt.figure(figsize=(20, 15))
    
    # Create grid layout for multiple plots
    gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
    
    # Performance comparison plots (top row)
    ax1 = fig.add_subplot(gs[0, 0])  # AEP comparison
    ax2 = fig.add_subplot(gs[0, 1])  # Runtime comparison  
    ax3 = fig.add_subplot(gs[0, 2])  # Evaluations comparison
    
    # Convergence plots (middle and bottom rows)
    ax4 = fig.add_subplot(gs[1, :])  # Convergence history
    ax5 = fig.add_subplot(gs[2, 0])  # DEAP detailed convergence
    ax6 = fig.add_subplot(gs[2, 1])  # Time per generation
    ax7 = fig.add_subplot(gs[2, 2])  # Improvement comparison
    
    # Plot 1: AEP Comparison
    frameworks = ['PyMoo GA', 'DEAP GA']
    aeps = [pymoo_results.get('best_aep', 0), deap_results.get('best_aep', 0)]
    baseline = pymoo_results.get('baseline_aep', 0)
    
    bars = ax1.bar(frameworks, aeps, color=['#1f77b4', '#ff7f0e'], alpha=0.7)
    ax1.axhline(y=baseline, color='red', linestyle='--', label=f'Baseline: {baseline:.3f} GWh')
    ax1.set_ylabel('AEP (GWh)')
    ax1.set_title('Annual Energy Production')
    ax1.legend()
    
    for bar, aep in zip(bars, aeps):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'{aep:.3f}', ha='center', va='bottom')
    
    # Plot 2: Runtime Comparison
    runtimes = [pymoo_results.get('total_time', 0), deap_results.get('total_time', 0)]
    bars = ax2.bar(frameworks, runtimes, color=['#1f77b4', '#ff7f0e'], alpha=0.7)
    ax2.set_ylabel('Runtime (seconds)')
    ax2.set_title('Optimization Runtime')
    
    for bar, runtime in zip(bars, runtimes):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                f'{runtime:.1f}s', ha='center', va='bottom')
    
    # Plot 3: Evaluations Comparison
    evals = [pymoo_results.get('n_evals', 0), deap_results.get('n_evals', 0)]
    bars = ax3.bar(frameworks, evals, color=['#1f77b4', '#ff7f0e'], alpha=0.7)
    ax3.set_ylabel('Number of Evaluations')
    ax3.set_title('Function Evaluations')
    
    for bar, eval_count in zip(bars, evals):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 10,
                f'{eval_count}', ha='center', va='bottom')
    
    # Plot 4: Convergence History Comparison
    deap_convergence = deap_results.get('convergence_history', {})
    if deap_convergence and 'generations' in deap_convergence:
        ax4.plot(deap_convergence['generations'], deap_convergence['best_fitness'], 
                'o-', label='DEAP Best', color='#ff7f0e', linewidth=2)
        ax4.plot(deap_convergence['generations'], deap_convergence['avg_fitness'], 
                '--', label='DEAP Average', color='#ff7f0e', alpha=0.7)
    
    ax4.set_xlabel('Generation')
    ax4.set_ylabel('Fitness (AEP GWh)')
    ax4.set_title('Convergence History Comparison')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    # Plot 5: DEAP Detailed Convergence
    if deap_convergence and 'generations' in deap_convergence:
        ax5.fill_between(deap_convergence['generations'], 
                        np.array(deap_convergence['avg_fitness']) - np.array(deap_convergence['std_fitness']),
                        np.array(deap_convergence['avg_fitness']) + np.array(deap_convergence['std_fitness']),
                        alpha=0.3, color='#ff7f0e', label='Std Dev')
        ax5.plot(deap_convergence['generations'], deap_convergence['best_fitness'], 
                'o-', label='Best', color='#d62728', linewidth=2)
        ax5.plot(deap_convergence['generations'], deap_convergence['avg_fitness'], 
                '-', label='Average', color='#ff7f0e', linewidth=2)
        ax5.plot(deap_convergence['generations'], deap_convergence['worst_fitness'], 
                ':', label='Worst', color='#1f77b4')
    
    ax5.set_xlabel('Generation')
    ax5.set_ylabel('Fitness (AEP GWh)')
    ax5.set_title('DEAP GA Detailed Convergence')
    ax5.legend()
    ax5.grid(True, alpha=0.3)
    
    # Plot 6: Time per Generation
    if deap_convergence and 'time_per_gen' in deap_convergence:
        ax6.bar(deap_convergence['generations'], deap_convergence['time_per_gen'], 
               color='#2ca02c', alpha=0.7)
    ax6.set_xlabel('Generation')
    ax6.set_ylabel('Time (seconds)')
    ax6.set_title('Time per Generation (DEAP)')
    ax6.grid(True, alpha=0.3)
    
    # Plot 7: Improvement Comparison
    improvements = [
        pymoo_results.get('aep_improvement_pct', 0),
        deap_results.get('aep_improvement_pct', 0)
    ]
    bars = ax7.bar(frameworks, improvements, color=['#1f77b4', '#ff7f0e'], alpha=0.7)
    ax7.set_ylabel('AEP Improvement (%)')
    ax7.set_title('AEP Improvement from Baseline')
    ax7.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    for bar, improvement in zip(bars, improvements):
        height = bar.get_height()
        ax7.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                f'{improvement:.2f}%', ha='center', va='bottom')
    
    # Save plot
    timestamp = comparison_results.timestamp
    plot_filename = f"ga_comparison_plots_{timestamp}.png"
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    print(f"📈 Plots saved to: {plot_filename}")
    
    plt.show()


def main():
    """Main comparison function"""
    print("🚀 PyMoo vs DEAP GA Comparison for Internal Layout Optimization")
    print("=" * 80)
    
    # Initialize results manager
    comparison_results = GAComparisonResults()
    
    try:
        # Setup FLORIS and data
        fi, freq, layout, baseline_aep = setup_floris_and_data()
        
        # Run PyMoo GA optimization
        pymoo_results = run_pymoo_ga_optimization(fi, freq, layout, baseline_aep)
        comparison_results.add_result('pymoo', 'ga', pymoo_results)
        
        # Run DEAP GA optimization
        deap_results = run_deap_ga_optimization(fi, freq, layout, baseline_aep)
        comparison_results.add_result('deap', 'ga', deap_results)
        
        # Generate comparison report
        comparison_results.generate_comparison_report()
        
        # Create comparison plots
        create_comparison_plots(comparison_results)
        
        # Save results
        comparison_results.save_results()
        
        print("\n✅ GA Comparison completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Comparison failed: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
