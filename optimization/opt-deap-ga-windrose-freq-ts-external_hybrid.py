#!/usr/bin/env python3
"""
DEAP-GA Hybrid External Layout Optimization Script
=================================================
Pure DEAP-GA hybrid approach with multi-stage optimization strategy
Replicates opt-pymoo-windrose-freq-ts-external_hybrid.py using only DEAP-GA algorithms

Hybrid Strategy (Pure DEAP-GA):
- Stage 1: Exploration GA (15 generations, higher mutation rate)
- Stage 2: Exploitation GA (20 generations, balanced parameters)
- Stage 3: Fine-tuning GA (15 generations, lower mutation rate)

Features:
- Multi-stage DEAP-GA optimization with parameter adaptation
- Population seeding between stages for continuity
- External turbine handling with smart filtering
- Enhanced convergence monitoring across stages
- Same 4-step workflow with hybrid optimization

4-Step Workflow:
1. Calculate baseline AEP (internal + relevant external)
2. Run hybrid layout optimization (3-stage DEAP-GA on internal turbines)
3. Simulate internal turbines only
4. Full wind farm simulation (internal + external)
5. Summary with gross/net AEP reporting
"""

import inspect
import sys
import copy
import os
import re
import shutil
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from multiprocessing import Process
from numpy import genfromtxt
from scipy.spatial.distance import cdist
from scipy.interpolate import NearestNDInterpolator
from datetime import datetime
from time import perf_counter as timerpc
from shapely.geometry import Polygon, LineString
import matplotlib.ticker as ticker
import matplotlib.patches as patches
from matplotlib.lines import Line2D
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Project-specific imports
import params as pr
import optimizer_params as optimizer_etc
import wind_dir_dominant as winddir

# Import enhanced utilities
from utilities import (
    sanitize_name,
    GetAEPParallel,
    memory_footprint,
    write_log,
    ts_to_freq_df,
    plot_layout_beforeOptim,
    plot_external_comparison,
    plot_external_comparison_enhanced,
    plot_iteration_solution,
    load_boundaries,
    create_enhanced_farm_manager,
    plot_enhanced_farm_analysis,
    plot_farm_sectors,
    calculate_gross_net_aep_efficient,
    plot_directional_coverage
)

# Import DEAP optimization components
from deap_optimization_manager import DEAPOptimizationManager, DEAPProblemWrapper, OptimizationConfig

# Set up work directory and paths
workDir = str(Path(__file__).parent.absolute())
print(f"DEAP-GA Hybrid External Layout Optimization Path: {workDir}")
OverAllProgress = f"{workDir}/OverAllProgress.txt"
print(f"OverAllProgress: {OverAllProgress}")

# Set FLORIS paths
floris_path = f"{workDir}/FLORIS_311_VF1_Operational"
turb_lib_path = f"{floris_path}/core/turbine_library"
print(f"floris_path: {floris_path}")
print(f"turb_lib_path: {turb_lib_path}")

# Add FLORIS to path
sys.path.append(floris_path)
try:
    import oyaml as yaml
except ImportError:
    import yaml

from core.tools import FlorisInterface


class SmartExternalLayoutManager:
    """
    Manages smart filtering of external turbines (identical to smart external script)
    """
    
    def __init__(self, internal_layout, external_layout, max_distance_km=20):
        self.internal_layout = internal_layout.copy()
        self.external_layout = external_layout.copy()
        self.max_distance_km = max_distance_km
        self.max_distance_m = max_distance_km * 1000
        
        print(f"🔍 Smart External Manager initialized:")
        print(f"   Internal turbines: {len(internal_layout)}")
        print(f"   External turbines: {len(external_layout)}")
        print(f"   Max distance: {max_distance_km} km")
        
        self.relevant_external = self._filter_by_distance()
        print(f"   Relevant external (within {max_distance_km}km): {len(self.relevant_external)}")
    
    def _filter_by_distance(self):
        """Pre-filter external turbines by distance to any internal turbine"""
        if len(self.external_layout) == 0:
            return pd.DataFrame()
        
        external_coords = self.external_layout[['x', 'y']].values
        internal_coords = self.internal_layout[['x', 'y']].values
        
        distances = cdist(external_coords, internal_coords)
        min_distances = np.min(distances, axis=1)
        
        within_distance = min_distances <= self.max_distance_m
        relevant_external = self.external_layout[within_distance].copy()
        
        return relevant_external
    
    def get_relevant_external_for_direction(self, wind_direction):
        """Get external turbines that are upwind for a specific wind direction"""
        if len(self.relevant_external) == 0:
            return pd.DataFrame()
        
        upwind_direction = (wind_direction + 180) % 360
        upwind_rad = np.radians(upwind_direction)
        
        internal_center_x = self.internal_layout['x'].mean()
        internal_center_y = self.internal_layout['y'].mean()
        
        relevant_for_direction = []
        
        for idx, ext_turbine in self.relevant_external.iterrows():
            dx = ext_turbine['x'] - internal_center_x
            dy = ext_turbine['y'] - internal_center_y
            
            ext_angle = np.degrees(np.arctan2(dy, dx)) % 360
            
            angle_diff = min(abs(ext_angle - upwind_direction), 
                           360 - abs(ext_angle - upwind_direction))
            
            if angle_diff <= 90:
                relevant_for_direction.append(ext_turbine)
        
        if relevant_for_direction:
            return pd.DataFrame(relevant_for_direction)
        else:
            return pd.DataFrame()
    
    def get_all_relevant_external(self):
        """Get all external turbines that are relevant for any wind direction"""
        all_directions = pr.analysis_directions
        all_relevant = set()
        
        for direction in all_directions:
            relevant = self.get_relevant_external_for_direction(direction)
            if len(relevant) > 0:
                for idx in relevant.index:
                    all_relevant.add(idx)
        
        if all_relevant:
            relevant_indices = list(all_relevant)
            return self.relevant_external.loc[relevant_indices].copy()
        else:
            return pd.DataFrame()


class DEAPGAHybridStageManager:
    """
    Manages individual stages of the hybrid DEAP-GA optimization
    Each stage uses different GA parameters for different purposes
    """
    
    def __init__(self, problem, stage_configs):
        """
        Initialize hybrid stage manager
        
        Args:
            problem: DEAPProblemWrapper instance
            stage_configs: List of OptimizationConfig objects for each stage
        """
        self.problem = problem
        self.stage_configs = stage_configs
        self.stage_results = []
        self.combined_history = {
            'generations': [],
            'best_fitness': [],
            'avg_fitness': [],
            'stage_markers': []
        }
        
    def run_stage(self, stage_num, config, initial_population=None):
        """
        Run a single optimization stage
        
        Args:
            stage_num: Stage number (1, 2, 3)
            config: OptimizationConfig for this stage
            initial_population: Optional initial population from previous stage
            
        Returns:
            Optimization result and final population
        """
        print(f"\n🔬 HYBRID STAGE {stage_num}: {config.stage_name}")
        print(f"   Generations: {config.n_gen}")
        print(f"   Population: {config.pop_size}")
        print(f"   Mutation prob: {config.mutation_prob:.3f}")
        print(f"   Crossover prob: {config.crossover_prob:.3f}")
        
        # Create stage-specific optimization manager
        manager = DEAPOptimizationManager(self.problem, config)
        
        # Set initial population if provided
        if initial_population is not None:
            manager.initial_population_override = initial_population
            print(f"   Seeding with {len(initial_population)} individuals from previous stage")
        
        # Run optimization
        stage_start = timerpc()
        result = manager.optimize()
        stage_time = timerpc() - stage_start
        
        # Extract convergence data
        convergence_data = result.get('convergence_history', {})
        
        # Add to combined history
        gen_offset = len(self.combined_history['generations'])
        if 'generations' in convergence_data and convergence_data['generations']:
            stage_generations = [g + gen_offset for g in convergence_data['generations']]
            self.combined_history['generations'].extend(stage_generations)
            self.combined_history['best_fitness'].extend(convergence_data.get('best_fitness', []))
            self.combined_history['avg_fitness'].extend(convergence_data.get('avg_fitness', []))
            self.combined_history['stage_markers'].append(gen_offset)
        
        # Store stage results
        stage_result = {
            'stage': stage_num,
            'config': config,
            'result': result,
            'stage_time': stage_time,
            'n_evals': result.get('n_evals', 0)
        }
        self.stage_results.append(stage_result)
        
        # Get final population for seeding next stage
        final_population = getattr(manager, 'final_population', None)
        
        print(f"   ✅ Stage {stage_num} completed in {stage_time:.2f}s")
        print(f"   Best fitness: {result['f']}")
        print(f"   Evaluations: {result.get('n_evals', 0)}")
        
        return result, final_population
    
    def run_all_stages(self):
        """Run all hybrid optimization stages sequentially"""
        print(f"\n🔗 Starting {len(self.stage_configs)}-stage hybrid DEAP-GA optimization")
        
        current_population = None
        final_result = None
        
        for i, config in enumerate(self.stage_configs, 1):
            result, final_population = self.run_stage(i, config, current_population)
            current_population = final_population
            final_result = result  # Keep the last result as final
        
        # Combine all stage statistics
        total_time = sum(stage['stage_time'] for stage in self.stage_results)
        total_evals = sum(stage['n_evals'] for stage in self.stage_results)
        
        # Create combined result
        combined_result = final_result.copy() if final_result else {}
        combined_result.update({
            'total_stages': len(self.stage_configs),
            'total_time': total_time,
            'total_evals': total_evals,
            'stage_results': self.stage_results,
            'combined_convergence_history': self.combined_history
        })
        
        print(f"\n✅ Hybrid optimization completed:")
        print(f"   Total stages: {len(self.stage_configs)}")
        print(f"   Total time: {total_time:.2f} seconds")
        print(f"   Total evaluations: {total_evals}")
        print(f"   Final best fitness: {final_result['f'] if final_result else 'N/A'}")
        
        return combined_result


class DEAPGAHybridExternalWorkflowManager:
    """
    Manages the complete 4-step DEAP-GA hybrid optimization workflow with external turbines
    """
    
    def __init__(self, max_distance_km=20):
        self.fi = None
        self.freq = None
        self.internal_layout = None
        self.external_layout = None
        self.boundaries = None
        self.baseline_aep = 0.0
        self.results = {}
        self.max_distance_km = max_distance_km
        self.external_manager = None
        self.optimized_internal_layout = None
        
        # Create output directories
        self._create_output_directories()
        
    def _create_output_directories(self):
        """Create output directories matching PyMoo structure"""
        self.output_dirs = {
            'input': workDir + '/Input/',
            'output': workDir + '/Output/',
            'output_initial': workDir + '/OutputInitial/',
            'output_internal': workDir + '/OutputInternal/'
        }
        
        for dir_path in self.output_dirs.values():
            os.makedirs(dir_path, exist_ok=True)
            
    def _plot_initial_layout(self):
        """Plot initial layout before optimization (matching PyMoo)"""
        try:
            boundaries_df = pd.read_csv(pr.boundariesFile)
            boundaries = list(zip(boundaries_df['X'], boundaries_df['Y']))
            
            # Plot combined internal + external layout
            if self.external_manager:
                all_relevant_external = self.external_manager.get_all_relevant_external()
                if len(all_relevant_external) > 0:
                    combined_layout = pd.concat([self.internal_layout, all_relevant_external], ignore_index=True)
                else:
                    combined_layout = self.internal_layout
            else:
                combined_layout = self.internal_layout
                
            x0 = combined_layout['x'].values
            y0 = combined_layout['y'].values
            
            plot_layout_beforeOptim(
                x0, y0, boundaries, 
                os.path.join(self.output_dirs['input'], "0.png"),
                "Initial Layout Before Optimization (Hybrid: Internal + External)"
            )
            print(f"📊 Initial layout plot saved: {self.output_dirs['input']}0.png")
        except Exception as e:
            print(f"Warning: Could not create initial layout plot: {e}")
            
    def _plot_optimized_layout(self):
        """Plot optimized layout after optimization (matching PyMoo)"""
        try:
            if self.optimized_internal_layout is not None:
                boundaries_df = pd.read_csv(pr.boundariesFile)
                boundaries = list(zip(boundaries_df['X'], boundaries_df['Y']))
                
                # Plot hybrid optimized internal + external layout
                if self.external_manager:
                    all_relevant_external = self.external_manager.get_all_relevant_external()
                    if len(all_relevant_external) > 0:
                        combined_layout = pd.concat([self.optimized_internal_layout, all_relevant_external], ignore_index=True)
                    else:
                        combined_layout = self.optimized_internal_layout
                else:
                    combined_layout = self.optimized_internal_layout
                    
                opt_x = combined_layout['x'].values
                opt_y = combined_layout['y'].values
                
                plot_layout_beforeOptim(
                    opt_x, opt_y, boundaries,
                    os.path.join(self.output_dirs['output'], "optimized_layout.png"),
                    "Final Hybrid Optimized Layout (Internal + External)"
                )
                print(f"📊 Optimized layout plot saved: {self.output_dirs['output']}optimized_layout.png")
        except Exception as e:
            print(f"Warning: Could not create optimized layout plot: {e}")
            
    def _export_csv_files(self):
        """Export CSV files matching DEAP internal structure exactly"""
        try:
            # Export initial layout (internal only - same as DEAP internal)
            if self.internal_layout is not None:
                initial_df = self.internal_layout.copy()
                initial_df.to_csv(os.path.join(self.output_dirs['output_initial'], "Initial.layout.csv"), index=False)
                print(f"📄 Initial layout CSV saved: {self.output_dirs['output_initial']}Initial.layout.csv")
            
            # Export optimized layout (internal only - same as DEAP internal)
            if self.optimized_internal_layout is not None:
                opt_df = self.optimized_internal_layout.copy()
                opt_df.to_csv(os.path.join(self.output_dirs['output'], "Optimized.layout.csv"), index=False)
                print(f"📄 Optimized layout CSV saved: {self.output_dirs['output']}Optimized.layout.csv")
                
                # Also save as internal layout (PyMoo compatibility)
                opt_df.to_csv(os.path.join(self.output_dirs['output_internal'], "InternalLayout.csv"), index=False)
                print(f"📄 Internal layout CSV saved: {self.output_dirs['output_internal']}InternalLayout.csv")
                
                # Final layout (same as optimized for internal-only output)
                opt_df.to_csv(os.path.join(self.output_dirs['output'], "Final.layout.csv"), index=False)
                print(f"📄 Final layout CSV saved: {self.output_dirs['output']}Final.layout.csv")
                
                # Hybrid-specific: Export stage results if available
                if hasattr(self, 'stage_results') and self.stage_results:
                    for i, stage_result in enumerate(self.stage_results):
                        stage_file = os.path.join(self.output_dirs['output'], f"Stage{i+1}.results.csv")
                        # Save stage-specific data if available
                        try:
                            if 'optimized_layout' in stage_result:
                                stage_layout_df = stage_result['optimized_layout']
                                stage_layout_df.to_csv(stage_file, index=False)
                                print(f"📄 Stage {i+1} results CSV saved: {stage_file}")
                        except Exception as e:
                            print(f"Warning: Could not save stage {i+1} results: {e}")
                
        except Exception as e:
            print(f"Warning: Could not export CSV files: {e}")
    
    def create_hybrid_stage_configs(self):
        """
        Create configuration for 3-stage hybrid DEAP-GA optimization
        
        Returns:
            List of OptimizationConfig objects for each stage
        """
        # Base parameters from PyMoo settings
        base_pop_size = pr.PopSize
        total_generations = pr.MAXGEN
        
        # Distribute generations across stages
        stage1_gens = int(total_generations * 0.3)  # 30% for exploration
        stage2_gens = int(total_generations * 0.4)  # 40% for exploitation
        stage3_gens = total_generations - stage1_gens - stage2_gens  # Rest for fine-tuning
        
        # Stage 1: Exploration (high mutation, diverse search)
        stage1_config = OptimizationConfig(
            algorithm='GA',
            pop_size=base_pop_size,
            n_gen=stage1_gens,
            crossover_prob=0.8,      # Slightly lower for more diversity
            mutation_prob=0.2,       # Higher mutation for exploration
            eta_c=pr.eta_c,
            eta_m=pr.eta_m,
            verbose=True,
            enable_early_termination=False  # No early termination in exploration
        )
        stage1_config.stage_name = "Exploration GA (High Mutation)"
        
        # Stage 2: Exploitation (balanced parameters)
        stage2_config = OptimizationConfig(
            algorithm='GA',
            pop_size=base_pop_size,
            n_gen=stage2_gens,
            crossover_prob=pr.pCross_real,  # Standard crossover
            mutation_prob=0.1,             # Standard mutation
            eta_c=pr.eta_c,
            eta_m=pr.eta_m,
            verbose=True,
            enable_early_termination=True,
            convergence_tol=pr.ftol,
            patience=3  # Shorter patience for intermediate stage
        )
        stage2_config.stage_name = "Exploitation GA (Balanced)"
        
        # Stage 3: Fine-tuning (low mutation, precise optimization)
        stage3_config = OptimizationConfig(
            algorithm='GA',
            pop_size=base_pop_size,
            n_gen=stage3_gens,
            crossover_prob=0.95,     # High crossover for exploitation
            mutation_prob=0.05,      # Low mutation for fine-tuning
            eta_c=pr.eta_c * 1.5,    # Higher eta_c for more precise crossover
            eta_m=pr.eta_m * 1.5,    # Higher eta_m for more precise mutation
            verbose=True,
            enable_early_termination=True,
            convergence_tol=pr.ftol * 0.1,  # Tighter tolerance
            patience=5
        )
        stage3_config.stage_name = "Fine-tuning GA (Low Mutation)"
        
        return [stage1_config, stage2_config, stage3_config]
    
    def setup_floris_and_data(self):
        """Step 0: Setup FLORIS and load data with external turbine handling"""
        print("\n🔄 Setting up FLORIS and loading time series data...")
        
        # Load layout data - EXACT SAME as PyMoo: Load multiple files for external turbines
        from glob import glob
        csv_files = glob(f"{workDir}/Input/External.layout.csv") + glob(f"{workDir}/Input/Initial.layout.csv")
        
        extra_file_path = os.path.join(workDir, 'Input', 'ExtraExternal.csv')
        if os.path.exists(extra_file_path):
            csv_files.append(extra_file_path)
        
        # Load and filter valid dataframes
        df_list = [pd.read_csv(file) for file in csv_files]
        filtered_df_list = [df for df in df_list if not df.empty and not df.isna().all().all()]
        all_layout = pd.concat(filtered_df_list, ignore_index=True)
        
        # Prepare layout data
        all_layout = all_layout.rename(columns={'easting': 'x', 'northing': 'y'})
        all_layout['turb'] = all_layout['turb'].apply(sanitize_name)
        
        # Separate internal and external layouts
        internal_layout = all_layout[all_layout['external'] == False].copy()
        external_layout = all_layout[all_layout['external'] == True].copy()
        
        print(f"Internal turbines for optimization: {len(internal_layout)}")
        print(f"External turbines for wake modeling: {len(external_layout)}")
        
        # Initialize smart external manager
        if len(external_layout) > 0:
            self.external_manager = SmartExternalLayoutManager(
                internal_layout, external_layout, self.max_distance_km
            )
        
        # Create turbine library files if needed
        self._setup_turbine_library(pd.concat([internal_layout, external_layout]))
        
        # Load time series data and convert to frequency (matches PyMoo pattern)
        try:
            # Use proper file path - timeseries.txt
            inputs_loc = workDir + "/Input/"
            ts = pd.read_csv(inputs_loc + "timeseries.txt", sep=' ')
            print(f"Time series data loaded: {len(ts)} records")
            
            # Handle timestamp format properly (matches PyMoo exactly)
            if 'time' in ts.columns and ts.index.name is None:
                ts.index.name = 'date'
                ts.reset_index(inplace=True)
                ts['timestamp'] = pd.to_datetime(ts['date'].astype(str) + ' ' + ts['time'])
                ts = ts[['timestamp', 'wd', 'ws']]
            elif 'timestamp' not in ts.columns:
                if pd.api.types.is_datetime64_any_dtype(ts.index):
                    ts['timestamp'] = ts.index
                    ts.reset_index(drop=True, inplace=True)
                else:
                    # Assume first column is time if no timestamp
                    ts = ts[['time', 'wd', 'ws']].rename(columns={'time': 'timestamp'})
            
            # Convert to frequency DataFrame using utilities function
            WR = ts_to_freq_df(ts)
            WR['wd'] = pd.to_numeric(WR['wd'], errors='coerce')
            WR['ws'] = pd.to_numeric(WR['ws'], errors='coerce')
            print(f"Size of WR: {len(WR.index)}")
            
            # Extract wind data arrays - Ensure proper frequency array formatting
            wd_array = np.array(sorted(WR["wd"].unique()), dtype=float)
            ws_array = np.array(sorted(WR["ws"].unique()), dtype=float)
            
            # Create frequency matrix that matches FLORIS interface expectations
            freq = WR.set_index(['wd','ws']).unstack().values
            print(f"Frequency data shape: {freq.shape}")
            
            # Store wind arrays as instance variables for use throughout the class
            self.wd_array = wd_array
            self.ws_array = ws_array
            
        except Exception as e:
            print(f"Warning: Could not load time series data: {e}")
            print("Using simplified frequency data for testing")
            wind_directions = np.array(pr.analysis_directions)
            wind_speeds = np.array([6., 8., 10., 12., 14.])
            freq = np.ones((len(wind_directions), len(wind_speeds)))
            freq = freq / np.sum(freq)  # Normalize
            
            # Store wind arrays as instance variables
            self.wd_array = wind_directions
            self.ws_array = wind_speeds
        
        # Initialize FLORIS with combined layout
        if self.external_manager:
            all_relevant_external = self.external_manager.get_all_relevant_external()
            if len(all_relevant_external) > 0:
                combined_layout = pd.concat([internal_layout, all_relevant_external], ignore_index=True)
            else:
                combined_layout = internal_layout
        else:
            combined_layout = internal_layout
        
        print(f"Combined layout for baseline: {len(combined_layout)} turbines")
        
        input_config = pr.FLORIS_CONFIG
        fi = FlorisInterface(input_config)
        
        fi.reinitialize(layout=(combined_layout['x'], combined_layout['y']),
                        turbine_type=combined_layout['turb'],
                        wind_directions=self.wd_array,
                        wind_speeds=self.ws_array)
        
        print(f"FLORIS setup complete:")
        print(f"  Wind directions: {len(pr.analysis_directions)}")
        print(f"  Wind speeds: 5")
        print(f"  Total turbines: {len(combined_layout)}")
        
        # Set reference height
        ref_ht = fi.floris.farm.hub_heights[0]
        fi.floris.flow_field.reference_wind_height = ref_ht
        
        # Store for workflow
        self.fi = fi
        self.freq = freq
        self.internal_layout = internal_layout
        self.external_layout = external_layout
        
        # Plot initial layout
        self._plot_initial_layout()
        
        return fi, freq, internal_layout, external_layout
        
    def _setup_turbine_library(self, layout):
        """Setup turbine library files"""
        unique_turbs = layout['turb'].unique()
        
        for turb_name in unique_turbs:
            safe_name = sanitize_name(turb_name)
            yaml_file = f'{turb_lib_path}/{safe_name}.yaml'
            
            if not os.path.exists(yaml_file):
                print(f"Creating turbine definition for: {safe_name}")
                
                new_turb = {
                    'turbine_type': safe_name,
                    'generator_efficiency': 1.0,
                    'hub_height': 90.0,
                    'pP': 1.88,
                    'pT': 1.88,
                    'rotor_diameter': 107.0,
                    'TSR': 8.0,
                    'power_thrust_table': {
                        'power': [0.0, 0.15, 0.3, 0.45, 0.47, 0.47, 0.47, 0.47, 0.47, 0.47, 0.0],
                        'thrust': [1.1, 1.1, 0.9, 0.7, 0.4, 0.3, 0.2, 0.1, 0.05, 0.0, 0.0],
                        'wind_speed': [0.0, 3.0, 4.0, 5.0, 6.0, 8.0, 10.0, 12.0, 15.0, 25.0, 35.0]
                    }
                }
                
                with open(yaml_file, 'w') as f:
                    yaml.dump(new_turb, f)

    def run_floris_initial(self):
        """Step 1: Calculate baseline AEP with internal + relevant external turbines"""
        print("\n" + "="*60)
        print("STEP 1: BASELINE AEP CALCULATION (INTERNAL + EXTERNAL)")
        print("="*60)
        
        self.fi.calculate_wake()
        baseline_aep = self.fi.get_farm_AEP(freq=self.freq) / 1E9
        
        print(f"✅ Baseline AEP calculated: {baseline_aep:.3f} GWh")
        print(f"   Internal turbines: {len(self.internal_layout)}")
        if self.external_manager:
            all_relevant = self.external_manager.get_all_relevant_external()
            print(f"   Relevant external turbines: {len(all_relevant)}")
        print(f"   Wind conditions: {len(pr.analysis_directions)} directions")
        
        self.baseline_aep = baseline_aep
        self.results['baseline_aep'] = baseline_aep
        
        write_log(OverAllProgress, f"Step 1 - Baseline AEP (Internal+External): {baseline_aep:.3f} GWh")
        
        return baseline_aep
    
    def run_hybrid_optimization(self):
        """Step 2: Run 3-stage hybrid DEAP-GA layout optimization"""
        print("\n" + "="*60)
        print("STEP 2: HYBRID DEAP-GA LAYOUT OPTIMIZATION (3 STAGES)")
        print("="*60)
        
        # Load boundaries
        boundaries_df = pd.read_csv(pr.boundariesFile)
        boundaries = list(zip(boundaries_df['X'], boundaries_df['Y']))
        self.boundaries = boundaries
        
        # Get combined layout with external turbines for hybrid optimization
        if self.external_manager:
            all_relevant_external = self.external_manager.get_all_relevant_external()
            if len(all_relevant_external) > 0:
                # Set weights for internal (1) and external (0) turbines
                self.internal_layout['weight'] = 1
                all_relevant_external['weight'] = 0
                combined_layout = pd.concat([self.internal_layout, all_relevant_external], ignore_index=True)
                turbines_weights = combined_layout['weight'].values
                print(f"   Using turbine weights: {np.sum(turbines_weights == 1)} internal, {np.sum(turbines_weights == 0)} external")
            else:
                combined_layout = self.internal_layout
                turbines_weights = None
                print("   No relevant external turbines - optimizing internal only")
        else:
            combined_layout = self.internal_layout
            turbines_weights = None
        
        # Create DEAP problem wrapper with combined layout for wake effects
        problem = DEAPProblemWrapper(
            fi=self.fi,  # Use full FLORIS instance with all turbines
            freq=self.freq,
            layout=combined_layout,
            boundaries=boundaries,
            baseline_aep=self.baseline_aep,
            turbines_weights=turbines_weights  # Enable internal/external separation
        )
        
        # Create hybrid stage configurations
        stage_configs = self.create_hybrid_stage_configs()
        
        print(f"🔗 Hybrid Configuration:")
        for i, config in enumerate(stage_configs, 1):
            print(f"   Stage {i}: {config.stage_name}")
            print(f"     Generations: {config.n_gen}, Mutation: {config.mutation_prob:.3f}")
        print(f"   Total generations: {sum(c.n_gen for c in stage_configs)}")
        print(f"   Baseline AEP: {self.baseline_aep:.3f} GWh")
        
        # Start optimization timing
        optimization_start = timerpc()
        
        # Create and run hybrid stage manager
        hybrid_manager = DEAPGAHybridStageManager(problem, stage_configs)
        result = hybrid_manager.run_all_stages()
        
        optimization_time = result.get('total_time', timerpc() - optimization_start)
        
        # Extract optimized layout from final result
        best_x = result['x']
        
        if turbines_weights is not None:
            # With weights: extract only internal turbine coordinates
            x_norm = best_x[:problem.numberOfInternalWTGs]
            y_norm = best_x[problem.numberOfInternalWTGs:]
        else:
            # Without weights: all turbines were optimized
            x_norm = best_x[:problem.n_turbines]
            y_norm = best_x[problem.n_turbines:]
        
        # Denormalize coordinates  
        opt_x = problem._unnorm(x_norm, problem.xmin, problem.xmax)
        opt_y = problem._unnorm(y_norm, problem.ymin, problem.ymax)
        
        # Calculate optimized AEP using full layout (internal optimized + external fixed)
        if turbines_weights is not None:
            # Reconstruct full layout with optimized internal positions
            full_x = combined_layout['x'].values.copy()
            full_y = combined_layout['y'].values.copy()
            
            # Update only internal turbine positions
            internal_indices = np.where(turbines_weights == 1)[0]
            for i, idx in enumerate(internal_indices):
                full_x[idx] = opt_x[i]
                full_y[idx] = opt_y[i]
            
            self.fi.reinitialize(layout=(full_x, full_y))
        else:
            self.fi.reinitialize(layout=(opt_x, opt_y))
            
        optimized_aep = self.fi.get_farm_AEP(freq=self.freq) / 1E9
        
        # Calculate improvement over baseline
        improvement = optimized_aep - self.baseline_aep
        improvement_pct = (improvement / self.baseline_aep) * 100 if self.baseline_aep > 0 else 0
        
        print(f"✅ Hybrid optimization completed:")
        print(f"   Optimized AEP: {optimized_aep:.3f} GWh")
        print(f"   Improvement: {improvement:.3f} GWh ({improvement_pct:.2f}%)")
        print(f"   Total optimization time: {optimization_time:.2f} seconds")
        print(f"   Total function evaluations: {result.get('total_evals', 0)}")
        print(f"   Stages completed: {result.get('total_stages', 0)}")
        
        # Store optimized internal layout
        self.optimized_internal_layout = pd.DataFrame({
            'x': opt_x,
            'y': opt_y,
            'turb': self.internal_layout['turb'].values,
            'external': False
        })
        
        # Store results
        self.results.update({
            'optimized_internal_aep': optimized_aep,
            'internal_baseline_aep': self.baseline_aep,
            'internal_improvement': improvement,
            'internal_improvement_pct': improvement_pct,
            'optimization_time': optimization_time,
            'total_evals': result.get('total_evals', 0),
            'total_stages': result.get('total_stages', 0),
            'stage_results': result.get('stage_results', []),
            'hybrid_convergence': result.get('combined_convergence_history', {}),
            'optimized_internal_layout': self.optimized_internal_layout
        })
        
        write_log(OverAllProgress, f"Step 2 - Hybrid External Optimization: {optimized_aep:.3f} GWh (+{improvement_pct:.2f}%)")
        
        return optimized_aep
    
    def run_floris_internal(self):
        """Step 3: Simulate internal turbines only with optimized layout"""
        print("\n" + "="*60)
        print("STEP 3: INTERNAL TURBINES SIMULATION (HYBRID OPTIMIZED)")
        print("="*60)
        
        # Use optimized layout for internal simulation
        if self.optimized_internal_layout is not None:
            layout_for_sim = self.optimized_internal_layout
        else:
            layout_for_sim = self.internal_layout
            
        # Create internal-only FLORIS instance
        fi_internal = FlorisInterface(pr.FLORIS_CONFIG)
        fi_internal.reinitialize(
            layout=(layout_for_sim['x'], layout_for_sim['y']),
            turbine_type=layout_for_sim['turb'],
            wind_directions=self.wd_array,
            wind_speeds=self.ws_array
        )
        
        # Calculate AEP for internal turbines only
        fi_internal.calculate_wake()
        internal_aep = fi_internal.get_farm_AEP(freq=self.freq) / 1E9
        
        print(f"✅ Internal simulation completed:")
        print(f"   Internal AEP (hybrid optimized): {internal_aep:.3f} GWh")
        print(f"   Turbines simulated: {len(layout_for_sim)}")
        
        self.results['internal_aep'] = internal_aep
        
        write_log(OverAllProgress, f"Step 3 - Internal AEP (Hybrid Optimized): {internal_aep:.3f} GWh")
        
        return internal_aep
    
    def run_floris_full(self):
        """Step 4: Full wind farm simulation with optimized internal + external turbines"""
        print("\n" + "="*60)
        print("STEP 4: FULL WIND FARM SIMULATION (INTERNAL + EXTERNAL)")
        print("="*60)
        
        # Use optimized internal layout
        if self.optimized_internal_layout is not None:
            internal_for_sim = self.optimized_internal_layout
        else:
            internal_for_sim = self.internal_layout
        
        # Combine with relevant external turbines
        if self.external_manager:
            all_relevant_external = self.external_manager.get_all_relevant_external()
            if len(all_relevant_external) > 0:
                combined_layout = pd.concat([internal_for_sim, all_relevant_external], ignore_index=True)
                print(f"   Combined layout: {len(internal_for_sim)} internal + {len(all_relevant_external)} external")
            else:
                combined_layout = internal_for_sim
                print(f"   No relevant external turbines found")
        else:
            combined_layout = internal_for_sim
            print(f"   Internal only: {len(internal_for_sim)} turbines")
            
        # Full farm simulation
        fi_full = FlorisInterface(pr.FLORIS_CONFIG)
        fi_full.reinitialize(
            layout=(combined_layout['x'], combined_layout['y']),
            turbine_type=combined_layout['turb'],
            wind_directions=self.wd_array,
            wind_speeds=self.ws_array
        )
        
        fi_full.calculate_wake()
        full_aep = fi_full.get_farm_AEP(freq=self.freq) / 1E9
        
        print(f"✅ Full farm simulation completed:")
        print(f"   Full farm AEP: {full_aep:.3f} GWh")
        print(f"   Total turbines: {len(combined_layout)}")
        
        self.results['full_aep'] = full_aep
        self.results['total_turbines'] = len(combined_layout)
        
        # Calculate separate contributions if external turbines exist
        if self.external_manager and len(all_relevant_external) > 0:
            internal_contribution = self.results.get('internal_aep', 0.0)
            external_contribution = full_aep - internal_contribution
            
            self.results['internal_contribution'] = internal_contribution
            self.results['external_contribution'] = external_contribution
            
            print(f"   Internal contribution: {internal_contribution:.3f} GWh")
            print(f"   External contribution: {external_contribution:.3f} GWh")
        
        write_log(OverAllProgress, f"Step 4 - Full Farm AEP: {full_aep:.3f} GWh")
        
        return full_aep
    
    def generate_summary(self):
        """Generate comprehensive summary report for hybrid external optimization"""
        print("\n" + "="*60)
        print("DEAP-GA HYBRID EXTERNAL OPTIMIZATION COMPLETE")
        print("="*60)
        
        baseline = self.results.get('baseline_aep', 0.0)
        internal_aep = self.results.get('internal_aep', 0.0)
        full_aep = self.results.get('full_aep', 0.0)
        internal_baseline = self.results.get('internal_baseline_aep', 0.0)
        internal_improvement = self.results.get('internal_improvement', 0.0)
        internal_improvement_pct = self.results.get('internal_improvement_pct', 0.0)
        opt_time = self.results.get('optimization_time', 0.0)
        total_evals = self.results.get('total_evals', 0)
        total_stages = self.results.get('total_stages', 0)
        
        print(f"Step 1 - Baseline AEP (Internal+External): {baseline:.3f} GWh")
        print(f"Step 2 - Hybrid Internal Optimization:")
        print(f"         Internal Baseline:             {internal_baseline:.3f} GWh")
        print(f"         Optimized Internal:            {internal_aep:.3f} GWh")
        print(f"         Internal Improvement:          {internal_improvement:.3f} GWh ({internal_improvement_pct:.2f}%)")
        print(f"         Optimization Stages:           {total_stages}")
        print(f"Step 3 - Internal AEP (Hybrid Optimized): {internal_aep:.3f} GWh")
        print(f"Step 4 - Full Farm AEP:                   {full_aep:.3f} GWh")
        print("-" * 60)
        
        # Overall improvement calculation
        overall_improvement = full_aep - baseline
        overall_improvement_pct = (overall_improvement / baseline) * 100 if baseline > 0 else 0
        
        print(f"Overall Improvement:               {overall_improvement:.3f} GWh ({overall_improvement_pct:.2f}%)")
        
        if 'internal_contribution' in self.results:
            internal_contrib = self.results['internal_contribution']
            external_contrib = self.results['external_contribution']
            print(f"Gross AEP (Internal):              {internal_contrib:.3f} GWh")
            print(f"Gross AEP (External):              {external_contrib:.3f} GWh")
        
        print(f"Net AEP (Total):                   {full_aep:.3f} GWh")
        print(f"Hybrid Optimization Time:          {opt_time:.2f} seconds")
        print(f"Total Function Evaluations:        {total_evals}")
        
        total_turbines = self.results.get('total_turbines', 0)
        internal_count = len(self.internal_layout) if self.internal_layout is not None else 0
        external_count = total_turbines - internal_count
        
        print(f"Turbines Optimized (Internal):     {internal_count}")
        print(f"External Turbines Included:        {external_count}")
        print(f"Smart Distance Threshold:          {self.max_distance_km} km")
        print(f"Hybrid Strategy:                   Pure DEAP-GA ({total_stages} stages)")
        print("="*60)
        
        # Print stage breakdown
        if 'stage_results' in self.results:
            print(f"\n📊 STAGE BREAKDOWN:")
            for stage in self.results['stage_results']:
                stage_num = stage['stage']
                stage_config = stage['config']
                stage_time = stage['stage_time']
                stage_evals = stage['n_evals']
                print(f"   Stage {stage_num}: {stage_config.stage_name}")
                print(f"     Time: {stage_time:.2f}s, Evaluations: {stage_evals}")
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"deap_ga_hybrid_external_results_{timestamp}.json"
        
        results_summary = {
            'timestamp': timestamp,
            'algorithm': 'DEAP-GA-Hybrid',
            'optimization_type': 'hybrid_external',
            'max_distance_km': self.max_distance_km,
            'total_stages': total_stages,
            'workflow_results': self.results,
            'summary': {
                'baseline_aep_gwh': baseline,
                'full_aep_gwh': full_aep,
                'overall_improvement_gwh': overall_improvement,
                'overall_improvement_percentage': overall_improvement_pct,
                'internal_improvement_percentage': internal_improvement_pct,
                'optimization_time_seconds': opt_time,
                'total_function_evaluations': total_evals,
                'internal_turbines': internal_count,
                'external_turbines': external_count,
                'total_turbines': total_turbines,
                'hybrid_stages': total_stages
            }
        }
        
        import json
        with open(results_file, 'w') as f:
            json.dump(results_summary, f, indent=2, default=str)
        
        print(f"📄 Detailed results saved to: {results_file}")
        
        # Generate plots and export CSV files
        self._plot_optimized_layout()
        self._export_csv_files()
        
        write_log(OverAllProgress, f"DEAP-GA Hybrid External Optimization Complete - Final AEP: {full_aep:.3f} GWh (+{overall_improvement_pct:.2f}%)")
        
        return results_summary

    def run_complete_workflow(self):
        """Execute the complete 4-step hybrid external optimization workflow"""
        print("🚀 Starting DEAP-GA hybrid external wind farm optimization workflow...")
        
        # Step 0: Setup
        self.setup_floris_and_data()
        
        # Step 1: Calculate baseline AEP
        baseline_aep = self.run_floris_initial()
        
        # Step 2: Run hybrid optimization
        optimized_aep = self.run_hybrid_optimization()
        
        # Step 3: Simulate internal turbines only
        internal_aep = self.run_floris_internal()
        
        # Step 4: Full wind farm simulation
        full_aep = self.run_floris_full()
        
        # Summary
        results = self.generate_summary()
        
        return results


def main():
    """Main function - execute DEAP-GA hybrid external optimization workflow"""
    print("🔬 DEAP-GA Hybrid External Layout Optimization")
    print("Pure DEAP-GA hybrid approach with 3-stage optimization strategy")
    print("="*80)
    
    # Initialize workflow manager with smart external filtering
    max_distance_km = getattr(pr, 'max_distance_km', 20)
    workflow = DEAPGAHybridExternalWorkflowManager(max_distance_km=max_distance_km)
    
    try:
        # Run complete workflow
        results = workflow.run_complete_workflow()
        
        print("\n✅ DEAP-GA hybrid external optimization workflow completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ DEAP-GA hybrid external optimization workflow failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)