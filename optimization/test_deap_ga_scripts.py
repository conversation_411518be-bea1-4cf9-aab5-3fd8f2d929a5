#!/usr/bin/env python3
"""
Test script for the new DEAP-GA optimization scripts
Validates that all three scripts run correctly and produce expected outputs
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def run_script_test(script_name, timeout_minutes=10):
    """
    Test a DEAP-GA optimization script
    
    Args:
        script_name: Name of the script to test
        timeout_minutes: Maximum time to allow script to run
        
    Returns:
        (success, output, error, execution_time)
    """
    print(f"\n🧪 Testing {script_name}...")
    print("="*60)
    
    script_path = Path(script_name)
    if not script_path.exists():
        return False, "", f"Script {script_name} not found", 0
    
    start_time = time.time()
    
    try:
        # Run the script with timeout
        result = subprocess.run(
            [sys.executable, script_name],
            capture_output=True,
            text=True,
            timeout=timeout_minutes * 60,
            cwd=os.getcwd()
        )
        
        execution_time = time.time() - start_time
        
        success = result.returncode == 0
        output = result.stdout
        error = result.stderr
        
        if success:
            print(f"✅ {script_name} completed successfully in {execution_time:.2f} seconds")
        else:
            print(f"❌ {script_name} failed with return code {result.returncode}")
            print(f"Error output: {error[:500]}...")  # First 500 chars of error
        
        return success, output, error, execution_time
        
    except subprocess.TimeoutExpired:
        execution_time = time.time() - start_time
        print(f"⏰ {script_name} timed out after {timeout_minutes} minutes")
        return False, "", f"Script timed out after {timeout_minutes} minutes", execution_time
        
    except Exception as e:
        execution_time = time.time() - start_time
        print(f"💥 {script_name} crashed: {e}")
        return False, "", str(e), execution_time

def extract_results_from_output(output):
    """Extract key results from script output"""
    results = {}
    
    # Look for key metrics in output
    lines = output.split('\n')
    for line in lines:
        if 'Baseline AEP:' in line:
            try:
                results['baseline_aep'] = float(line.split(':')[1].strip().split()[0])
            except:
                pass
        elif 'Optimized AEP:' in line or 'Full Farm AEP:' in line:
            try:
                results['final_aep'] = float(line.split(':')[1].strip().split()[0])
            except:
                pass
        elif 'Improvement:' in line and '%' in line:
            try:
                # Extract percentage improvement
                pct_part = line.split('(')[1].split('%')[0]
                results['improvement_pct'] = float(pct_part)
            except:
                pass
        elif 'Optimization Time:' in line:
            try:
                results['optimization_time'] = float(line.split(':')[1].strip().split()[0])
            except:
                pass
        elif 'Function Evaluations:' in line:
            try:
                results['n_evals'] = int(line.split(':')[1].strip())
            except:
                pass
    
    return results

def test_all_scripts():
    """Test all three DEAP-GA optimization scripts"""
    
    scripts_to_test = [
        ('opt-deap-ga-windrose-freq-ts_corrected.py', 5),  # 5 minute timeout
        ('opt-deap-ga-windrose-freq-ts-external_smart.py', 8),  # 8 minute timeout  
        ('opt-deap-ga-windrose-freq-ts-external_hybrid.py', 12)  # 12 minute timeout
    ]
    
    print("🔬 Testing DEAP-GA Optimization Scripts")
    print("=" * 80)
    print("This will test all three new DEAP-GA scripts to ensure they work correctly")
    print("Each script will run with a timeout to prevent hanging")
    
    overall_results = {}
    
    for script_name, timeout_min in scripts_to_test:
        success, output, error, exec_time = run_script_test(script_name, timeout_min)
        
        # Extract results from output
        extracted_results = extract_results_from_output(output)
        
        overall_results[script_name] = {
            'success': success,
            'execution_time': exec_time,
            'extracted_results': extracted_results,
            'output_length': len(output),
            'error_length': len(error)
        }
        
        # Print summary for this script
        if success:
            print(f"   Results extracted:")
            for key, value in extracted_results.items():
                print(f"     {key}: {value}")
        
        # Save outputs for debugging
        output_file = f"test_output_{script_name.replace('.py', '')}.txt"
        with open(output_file, 'w') as f:
            f.write(f"SCRIPT: {script_name}\n")
            f.write(f"SUCCESS: {success}\n")
            f.write(f"EXECUTION_TIME: {exec_time:.2f}s\n")
            f.write(f"RETURN_CODE: {'0' if success else 'non-zero'}\n")
            f.write("\n" + "="*60 + "\n")
            f.write("STDOUT:\n")
            f.write("="*60 + "\n")
            f.write(output)
            f.write("\n" + "="*60 + "\n")
            f.write("STDERR:\n")
            f.write("="*60 + "\n")
            f.write(error)
        
        print(f"   📁 Output saved to: {output_file}")
    
    # Generate summary report
    print("\n" + "="*80)
    print("🏁 TESTING SUMMARY")
    print("="*80)
    
    total_scripts = len(scripts_to_test)
    successful_scripts = sum(1 for result in overall_results.values() if result['success'])
    
    print(f"Scripts tested: {total_scripts}")
    print(f"Successful: {successful_scripts}")
    print(f"Failed: {total_scripts - successful_scripts}")
    print(f"Success rate: {(successful_scripts/total_scripts)*100:.1f}%")
    
    print(f"\n📊 INDIVIDUAL RESULTS:")
    for script_name, result in overall_results.items():
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        exec_time = result['execution_time']
        print(f"   {script_name:<45} {status} ({exec_time:.1f}s)")
        
        if result['extracted_results']:
            for key, value in result['extracted_results'].items():
                print(f"     └─ {key}: {value}")
    
    # Check for expected result files
    print(f"\n📁 GENERATED FILES:")
    result_files = [
        'deap_ga_internal_results_*.json',
        'deap_ga_smart_external_results_*.json', 
        'deap_ga_hybrid_external_results_*.json'
    ]
    
    import glob
    for pattern in result_files:
        files = glob.glob(pattern)
        if files:
            for file in files[-1:]:  # Show only the latest file
                print(f"   ✅ {file}")
        else:
            print(f"   ❌ No files matching {pattern}")
    
    print(f"\n🎯 RECOMMENDATIONS:")
    if successful_scripts == total_scripts:
        print("   All scripts are working correctly! ✅")
        print("   Ready for production use with PyMoo workflow compatibility")
    else:
        print("   Some scripts failed - check error outputs for debugging")
        print("   Review test_output_*.txt files for detailed error information")
    
    return overall_results

if __name__ == "__main__":
    print("Starting DEAP-GA script testing...")
    
    try:
        results = test_all_scripts()
        
        # Return appropriate exit code
        success_count = sum(1 for r in results.values() if r['success'])
        total_count = len(results)
        
        if success_count == total_count:
            print("\n🎉 All tests passed!")
            exit(0)
        else:
            print(f"\n⚠️  {total_count - success_count} tests failed")
            exit(1)
            
    except Exception as e:
        print(f"💥 Testing framework failed: {e}")
        import traceback
        traceback.print_exc()
        exit(2)