#!/usr/bin/env python3
"""
Optimization Framework Manager
Unified interface for pymoo, DEAP, and Pagmo2 optimization frameworks

Features:
- Automatic framework detection and switching
- Unified problem interface across all frameworks
- Result standardization and comparison
- Performance monitoring and benchmarking
- Population transfer between frameworks
- Configurable hybrid optimization sequences
"""

import numpy as np
import time
import copy
import warnings
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
import json

# Framework imports
try:
    import params as pr
except ImportError:
    print("Warning: params.py not found - using default parameters")
    pr = None

warnings.filterwarnings('ignore')


@dataclass
class OptimizationResult:
    """Standardized optimization result across all frameworks"""
    x: np.ndarray                    # Best solution(s)
    f: np.ndarray                    # Objective value(s)
    n_evals: int                     # Number of evaluations
    exec_time: float                 # Execution time in seconds
    algorithm: str                   # Algorithm name
    framework: str                   # Framework used
    convergence_data: Optional[Dict] = None  # Convergence history
    additional_data: Optional[Dict] = None   # Framework-specific data


class OptimizationFrameworkManager:
    """Manager for multiple optimization frameworks"""
    
    def __init__(self, problem, config=None):
        """
        Initialize framework manager
        
        Args:
            problem: Optimization problem instance
            config: Configuration dictionary (uses params.py if None)
        """
        self.problem = problem
        self.config = config or self._load_config()
        
        # Available frameworks
        self.available_frameworks = self._detect_frameworks()
        
        # Results storage
        self.results_history = []
        self.framework_performance = {}
        
        # Shared evaluation cache for efficiency
        self.evaluation_cache = {}
        self.cache_enabled = self.config.get('framework_parallel_config', {}).get('shared_evaluation_cache', True)
        
        print(f"🔧 Optimization Framework Manager initialized")
        print(f"   Available frameworks: {self.available_frameworks}")
        print(f"   Optimization mode: {self.config.get('optimization_framework', 'hybrid')}")
        print(f"   Shared cache: {self.cache_enabled}")

    def _load_config(self):
        """Load configuration from params.py or defaults"""
        if pr is None:
            # Default configuration
            return {
                'optimization_framework': 'hybrid',
                'hybrid_sequence': [
                    {'framework': 'pymoo', 'algorithm': 'GA', 'generations': 10}
                ],
                'deap_config': {'verbose': True},
                'enable_framework_comparison': False,
                'framework_parallel_config': {'shared_evaluation_cache': True}
            }
        
        # Extract relevant configuration from params.py
        config = {}
        for attr in dir(pr):
            if not attr.startswith('_'):
                config[attr] = getattr(pr, attr)
        
        return config

    def _detect_frameworks(self):
        """Detect available optimization frameworks"""
        frameworks = []
        
        # Check pymoo
        try:
            import pymoo
            frameworks.append('pymoo')
        except ImportError:
            pass
            
        # Check DEAP
        try:
            import deap
            frameworks.append('deap')
        except ImportError:
            pass
            
        # Check Pagmo2 (will be implemented later)
        try:
            import pygmo as pg
            frameworks.append('pagmo2')
        except ImportError:
            pass
            
        return frameworks

    def optimize(self, framework=None, algorithm=None, **kwargs):
        """
        Run optimization using specified framework and algorithm
        
        Args:
            framework: Framework to use ('pymoo', 'deap', 'pagmo2', 'hybrid', None=auto)
            algorithm: Algorithm to use (None=default for framework)
            **kwargs: Additional optimization parameters
            
        Returns:
            OptimizationResult: Standardized result
        """
        # Determine framework and algorithm
        framework = framework or self.config.get('optimization_framework', 'hybrid')
        
        if framework == 'hybrid':
            return self._run_hybrid_optimization(**kwargs)
        elif framework in self.available_frameworks:
            return self._run_single_framework(framework, algorithm, **kwargs)
        else:
            raise ValueError(f"Framework '{framework}' not available. Available: {self.available_frameworks}")

    def _run_hybrid_optimization(self, **kwargs):
        """Run hybrid optimization sequence"""
        sequence = self.config.get('hybrid_sequence', [])
        
        if not sequence:
            # Default to single framework
            framework = self.available_frameworks[0] if self.available_frameworks else 'pymoo'
            return self._run_single_framework(framework, None, **kwargs)
        
        print(f"🔄 Starting hybrid optimization sequence: {len(sequence)} stages")
        
        # Initialize with first stage
        current_population = None
        all_results = []
        total_evals = 0
        total_time = 0
        
        for i, stage in enumerate(sequence):
            stage_framework = stage['framework']
            stage_algorithm = stage['algorithm']
            stage_generations = stage.get('generations', 10)
            
            print(f"\n🔄 Stage {i+1}/{len(sequence)}: {stage_framework} {stage_algorithm} ({stage_generations} gen)")
            
            # Prepare stage parameters
            stage_kwargs = kwargs.copy()
            stage_kwargs['n_gen'] = stage_generations
            stage_kwargs['initial_population'] = current_population
            
            # Run stage
            stage_result = self._run_single_framework(
                stage_framework, stage_algorithm, **stage_kwargs
            )
            
            # Update tracking
            all_results.append(stage_result)
            total_evals += stage_result.n_evals
            total_time += stage_result.exec_time
            
            # Extract population for next stage
            current_population = self._extract_population(stage_result)
            
            print(f"   ✅ Stage {i+1} complete: f = {stage_result.f}")
        
        # Return best result from all stages
        best_result = min(all_results, key=lambda r: np.min(r.f) if r.f.ndim > 0 else r.f)
        
        # Create combined result
        hybrid_result = OptimizationResult(
            x=best_result.x,
            f=best_result.f,
            n_evals=total_evals,
            exec_time=total_time,
            algorithm='Hybrid',
            framework='Hybrid',
            additional_data={
                'sequence': sequence,
                'stage_results': all_results,
                'best_stage': all_results.index(best_result)
            }
        )
        
        self.results_history.append(hybrid_result)
        print(f"\n✅ Hybrid optimization complete: f = {hybrid_result.f} ({total_evals} evals, {total_time:.1f}s)")
        
        return hybrid_result

    def _run_single_framework(self, framework, algorithm=None, **kwargs):
        """Run optimization on a single framework"""
        if framework == 'pymoo':
            return self._run_pymoo(algorithm, **kwargs)
        elif framework == 'deap':
            return self._run_deap(algorithm, **kwargs)
        elif framework == 'pagmo2':
            return self._run_pagmo2(algorithm, **kwargs)
        else:
            raise ValueError(f"Framework '{framework}' not supported")

    def _run_pymoo(self, algorithm=None, **kwargs):
        """Run pymoo optimization"""
        try:
            from hybrid_layout_optimization_pymoo_external import HybridLayoutOptimizationPymoo
            
            # Extract parameters
            pop_size = kwargs.get('pop_size', self.config.get('PopSize', 32))
            n_gen = kwargs.get('n_gen', self.config.get('MAXGEN', 10))
            
            # Create optimizer instance
            optimizer = HybridLayoutOptimizationPymoo(
                self.problem.parent.fi,
                self.problem.parent.freq,
                self.problem.parent.turbs_weights if hasattr(self.problem.parent, 'turbs_weights') else None,
                boundaries=getattr(self.problem.parent, 'boundaries', None),
                min_dist=self.config.get('min_dist', 630.0),
                pop_size=pop_size,
                n_gen=n_gen,
                n_workers=self.config.get('n_workers', 32),
                timeout_per_eval=self.config.get('timeout_per_eval', 300)
            )
            
            # Run optimization
            start_time = time.time()
            result = optimizer.optimize()
            exec_time = time.time() - start_time
            
            # Standardize result
            return OptimizationResult(
                x=result.X if hasattr(result, 'X') else np.array([]),
                f=result.F if hasattr(result, 'F') else np.array([0.0]),
                n_evals=getattr(result, 'n_evals', optimizer.eval_count if hasattr(optimizer, 'eval_count') else 0),
                exec_time=exec_time,
                algorithm=algorithm or 'Hybrid',
                framework='pymoo',
                additional_data={'result': result}
            )
            
        except Exception as e:
            print(f"Error running pymoo: {e}")
            raise

    def _run_deap(self, algorithm=None, **kwargs):
        """Run DEAP optimization"""
        try:
            from deap_algorithms_external import create_deap_algorithm
            
            # Extract parameters
            algorithm = algorithm or self.config.get('selected_algorithm', 'GA')
            pop_size = kwargs.get('pop_size', self.config.get('PopSize', 32))
            n_gen = kwargs.get('n_gen', self.config.get('MAXGEN', 10))
            
            # Get DEAP configuration
            deap_config = self.config.get('deap_config', {})
            
            # Create DEAP optimizer
            optimizer = create_deap_algorithm(
                self.problem,
                algorithm_name=algorithm,
                pop_size=pop_size,
                n_gen=n_gen,
                n_workers=self.config.get('n_workers', 32),
                timeout_per_eval=self.config.get('timeout_per_eval', 300),
                use_scoop=deap_config.get('use_scoop', False),
                verbose=deap_config.get('verbose', True)
            )
            
            # Run optimization
            result = optimizer.optimize()
            
            # Standardize result
            return OptimizationResult(
                x=result['x'],
                f=result['f'],
                n_evals=result['n_evals'],
                exec_time=result['exec_time'],
                algorithm=result['algorithm'],
                framework='deap',
                convergence_data=result.get('logbook'),
                additional_data={k: v for k, v in result.items() if k not in ['x', 'f', 'n_evals', 'exec_time', 'algorithm']}
            )
            
        except Exception as e:
            print(f"Error running DEAP: {e}")
            raise

    def _run_pagmo2(self, algorithm=None, **kwargs):
        """Run Pagmo2 optimization (placeholder for future implementation)"""
        print("Pagmo2 implementation coming soon - using pymoo fallback")
        return self._run_pymoo(algorithm, **kwargs)

    def _extract_population(self, result):
        """Extract population from optimization result for next stage"""
        # For now, return best solution replicated
        # TODO: Implement proper population extraction from each framework
        if result.x.ndim == 1:
            # Single solution - replicate with noise
            pop_size = self.config.get('PopSize', 32)
            population = []
            for _ in range(pop_size):
                individual = result.x + np.random.normal(0, 0.01, len(result.x))
                individual = np.clip(individual, 0.0, 1.0)  # Ensure bounds
                population.append(individual)
            return np.array(population)
        else:
            # Multiple solutions
            return result.x

    def compare_frameworks(self, frameworks=None, algorithms=None, runs=3):
        """
        Compare performance across multiple frameworks and algorithms
        
        Args:
            frameworks: List of frameworks to compare (None = all available)
            algorithms: List of algorithms to compare (None = default for each)
            runs: Number of runs per combination
            
        Returns:
            Dict: Comparison results
        """
        frameworks = frameworks or self.config.get('comparison_frameworks', self.available_frameworks)
        algorithms = algorithms or self.config.get('comparison_algorithms', ['GA', 'DE'])
        
        print(f"🔬 Starting framework comparison: {frameworks} × {algorithms} × {runs} runs")
        
        comparison_results = {}
        
        for framework in frameworks:
            if framework not in self.available_frameworks:
                continue
                
            framework_results = {}
            
            for algorithm in algorithms:
                algorithm_results = []
                
                print(f"   Running {framework} {algorithm}...")
                
                for run in range(runs):
                    try:
                        result = self._run_single_framework(
                            framework, 
                            algorithm,
                            pop_size=self.config.get('PopSize', 32),
                            n_gen=self.config.get('MAXGEN', 10)
                        )
                        algorithm_results.append(result)
                        
                    except Exception as e:
                        print(f"     Run {run+1} failed: {e}")
                        
                framework_results[algorithm] = algorithm_results
                
            comparison_results[framework] = framework_results
        
        # Analyze results
        analysis = self._analyze_comparison_results(comparison_results)
        
        # Save results if configured
        if self.config.get('benchmark_config', {}).get('save_results', False):
            self._save_comparison_results(comparison_results, analysis)
        
        return {
            'results': comparison_results,
            'analysis': analysis
        }

    def _analyze_comparison_results(self, results):
        """Analyze comparison results"""
        analysis = {}
        
        for framework, framework_results in results.items():
            framework_analysis = {}
            
            for algorithm, algorithm_results in framework_results.items():
                if not algorithm_results:
                    continue
                    
                # Extract metrics
                objectives = [r.f for r in algorithm_results]
                times = [r.exec_time for r in algorithm_results]
                evals = [r.n_evals for r in algorithm_results]
                
                # Calculate statistics
                algorithm_analysis = {
                    'objective': {
                        'mean': np.mean(objectives),
                        'std': np.std(objectives),
                        'best': np.min(objectives),
                        'worst': np.max(objectives)
                    },
                    'time': {
                        'mean': np.mean(times),
                        'std': np.std(times),
                        'total': np.sum(times)
                    },
                    'evaluations': {
                        'mean': np.mean(evals),
                        'std': np.std(evals),
                        'total': np.sum(evals)
                    },
                    'success_rate': len(algorithm_results) / len(algorithm_results)  # All successful if we get here
                }
                
                framework_analysis[algorithm] = algorithm_analysis
                
            analysis[framework] = framework_analysis
        
        return analysis

    def _save_comparison_results(self, results, analysis):
        """Save comparison results to file"""
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        
        # Save raw results
        results_file = f"framework_comparison_{timestamp}_results.json"
        # Note: Results contain non-serializable objects, so we save a summary
        
        # Save analysis
        analysis_file = f"framework_comparison_{timestamp}_analysis.json"
        try:
            with open(analysis_file, 'w') as f:
                json.dump(analysis, f, indent=2, default=str)
            print(f"   Comparison analysis saved to {analysis_file}")
        except Exception as e:
            print(f"   Failed to save analysis: {e}")

    def get_framework_recommendations(self, problem_size=None):
        """Get framework recommendations based on problem characteristics"""
        recommendations = {}
        
        if problem_size is None:
            problem_size = self.problem.n_var * self.config.get('PopSize', 32)
        
        # Simple heuristics for framework selection
        if problem_size < 1000:
            recommendations['best_framework'] = 'deap'
            recommendations['reason'] = 'Small problem - DEAP efficient for simple cases'
        elif problem_size < 10000:
            recommendations['best_framework'] = 'pymoo'
            recommendations['reason'] = 'Medium problem - pymoo good balance of features and performance'
        else:
            recommendations['best_framework'] = 'pagmo2'
            recommendations['reason'] = 'Large problem - pagmo2 archipelago ideal for parallelization'
        
        recommendations['hybrid_sequence'] = [
            {'framework': 'deap', 'algorithm': 'GA', 'generations': 5},
            {'framework': 'pymoo', 'algorithm': 'DE', 'generations': 10}
        ]
        
        return recommendations

    def get_performance_summary(self):
        """Get performance summary of all runs"""
        if not self.results_history:
            return "No optimization runs completed yet"
        
        summary = {
            'total_runs': len(self.results_history),
            'frameworks_used': list(set(r.framework for r in self.results_history)),
            'algorithms_used': list(set(r.algorithm for r in self.results_history)),
            'total_evaluations': sum(r.n_evals for r in self.results_history),
            'total_time': sum(r.exec_time for r in self.results_history),
            'best_result': min(self.results_history, key=lambda r: np.min(r.f) if r.f.ndim > 0 else r.f)
        }
        
        return summary


# Factory functions for easy usage
def create_optimization_manager(problem, config=None):
    """Create optimization framework manager"""
    return OptimizationFrameworkManager(problem, config)


def run_optimization(problem, framework=None, algorithm=None, **kwargs):
    """Quick optimization function"""
    manager = create_optimization_manager(problem)
    return manager.optimize(framework, algorithm, **kwargs)


# Test function
if __name__ == "__main__":
    print("Optimization Framework Manager")
    print("Available features:")
    print("- Multi-framework support (pymoo, DEAP, pagmo2)")
    print("- Hybrid optimization sequences")
    print("- Framework comparison and benchmarking")
    print("- Unified result interface")
    print("- Population transfer between frameworks")