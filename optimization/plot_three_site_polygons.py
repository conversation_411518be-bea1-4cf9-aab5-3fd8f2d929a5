#!/usr/bin/env python3
"""
Script to plot and compare three different site polygon geometry constraints:
1. Input/Boundaries.csv
2. Input/Boundaries-genPoints.csv 
3. shp/shp_coord_total.csv

Results are saved to Input/ directory
"""

import sys
import os
from pathlib import Path

# Add current directory to path for utilities import
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utilities import plot_three_polygon_comparison

def main():
    """
    Main function to run the three-polygon comparison
    """
    print("=" * 80)
    print("Wind Farm Site Polygon Comparison - Three Sources")
    print("=" * 80)
    
    # Define the three data sources
    boundaries_file = 'Input/Boundaries.csv'
    gen_points_file = 'Input/Boundaries-genPoints.csv'
    shp_file = 'shp/shp_coord_total.csv'
    
    # Check if files exist
    files_to_check = [boundaries_file, gen_points_file, shp_file]
    missing_files = []
    
    for file_path in files_to_check:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("Error: The following required files are missing:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("\nPlease ensure all required data files are present.")
        return 1
    
    print("Found all required data files:")
    for file_path in files_to_check:
        print(f"  ✓ {file_path}")
    
    print("\nStarting three-polygon comparison analysis...")
    
    try:
        # Run the comparison
        plot_three_polygon_comparison(
            input_dir='Input/',
            shp_dir='shp/',
            output_dir='Input'
        )
        
        print("\n" + "=" * 80)
        print("Three-polygon comparison completed successfully!")
        print("=" * 80)
        print("\nGenerated files in Input/ directory:")
        print("  - three_polygon_individual_plots.png")
        print("  - three_polygon_combined_view.png") 
        print("  - three_polygon_comparison.png")
        print("  - three_polygon_comparison_summary.csv")
        
        return 0
        
    except Exception as e:
        print(f"\nError during comparison: {str(e)}")
        print("Please check the data files and try again.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)