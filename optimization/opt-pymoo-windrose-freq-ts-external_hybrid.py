#!/usr/bin/env python3
"""
Hybrid External Turbine Layout Optimization Script
Complete 4-step workflow using hybrid algorithms (GA→DE→PSO) with your established patterns
Based on opt-pymoo-windrose-freq-ts-external_smart.py
"""

import numpy as np
import pandas as pd
import time
import os
import sys
import warnings
from pathlib import Path
warnings.filterwarnings('ignore')
# Specifically suppress FLORIS wind bounds warnings during optimization
warnings.filterwarnings("ignore", message="Some timestamps fell outside of the WS or WD bounds")

# Set up work directory and paths
print("RunFloris.py      Path:", Path(__file__).parent.absolute())
workDir = str(Path(__file__).parent.absolute())
OverAllProgress = workDir + "/OverAllProgress.txt"
        
print(f"OverAllProgress : {OverAllProgress}")

dirName = workDir
floris_path = dirName + '/FLORIS_311_VF1_Operational'
print(f"floris_path : {floris_path}")
turb_lib_path = dirName + '/FLORIS_311_VF1_Operational/core/turbine_library'
print(f"turb_lib_path : {turb_lib_path}")

# Add FLORIS to path
sys.path.append(floris_path)

# FLORIS imports
try:
    import oyaml as yaml
except ImportError:
    import yaml

# Additional warning suppression for FLORIS
import logging
logging.getLogger().setLevel(logging.ERROR)  # Suppress FLORIS warnings

# More comprehensive warning suppression
import os
os.environ['PYTHONWARNINGS'] = 'ignore'

from core.tools import FlorisInterface

# Import hybrid optimization components
from hybrid_layout_optimization_pymoo_external import HybridLayoutOptimizationPymoo
from smart_initialization_strategies import SmartInitializationManager
from hybrid_algorithms_external import get_algorithm_recommendations

# Import utilities and parameters
import params as pr
import optimizer_params as optimizer_etc
import utilities as util

# Global variable to store reference gross powers for consistency
REFERENCE_GROSS_POWERS = None
from utilities import (
    sanitize_name,
    ts_to_freq_df,
    plot_layout_beforeOptim,
    plot_external_comparison,
    plot_external_comparison_enhanced,
    plot_iteration_solution,
    write_log,
    create_enhanced_farm_manager,
    plot_enhanced_farm_analysis,
    plot_farm_sectors,
    calculate_gross_net_aep_efficient,
    plot_directional_coverage,
    load_boundaries
)

def normalize_turbine_types(turbine_types_array):
    """
    Normalize turbine type names to match FLORIS library file naming
    
    Args:
        turbine_types_array: Array of turbine type names
        
    Returns:
        Array of normalized turbine type names
    """
    turbine_type_mapping = {
        'Zeevonk East': 'zeevonk east',
        'Zeevonk West': 'zeevonk west',
        'SWT_3.6-107_PP_M102_std_R20121010': 'swt_3.6-107_pp_m102_std_r20121010'
    }
    
    normalized_types = []
    for turb_type in turbine_types_array:
        if turb_type in turbine_type_mapping:
            normalized_types.append(turbine_type_mapping[turb_type])
        else:
            normalized_types.append(turb_type)
    
    return normalized_types  # Return as list, not numpy array

def run_florisInitial():
    """Step 1: Run initial FLORIS simulation with original layout"""
    print("="*80)
    print("🔄 STEP 1: Running Initial FLORIS Simulation")
    print("="*80)
    
    start_time = time.time()
    
    # Load initial layout
    turbine_map = pd.read_csv(pr.inputLayoutFile, header=0)
    
    # Handle different column names and standardize
    if 'easting' in turbine_map.columns:
        turbine_map = turbine_map.rename(columns={'easting':'x','northing':'y'})
    if 'turb' in turbine_map.columns:
        turbine_map['turb'] = turbine_map['turb'].apply(sanitize_name)
    
    # CRITICAL FIX: Filter to internal turbines only for FLORIS initialization
    if 'external' in turbine_map.columns:
        internal_layout = turbine_map[turbine_map['external'] == False].copy()
        print(f"   🎯 Total turbines in layout: {len(turbine_map)}")
        print(f"   🎯 Internal turbines for optimization: {len(internal_layout)}")
        print(f"   🎯 External turbines (fixed): {len(turbine_map) - len(internal_layout)}")
    else:
        # If no external column, treat all turbines as internal
        internal_layout = turbine_map.copy()
        print(f"   🎯 All turbines treated as internal: {len(internal_layout)}")
    
    # Set up FLORIS interface
    fi = FlorisInterface(pr.FLORIS_CONFIG)
    
    # Read and process wind data using corrected pattern
    ts = pd.read_csv(pr.windRoseFile, sep=' ')
    
    # Process timestamp format properly
    if 'time' in ts.columns and ts.index.name is None:
        ts.index.name = 'date'
        ts.reset_index(inplace=True)
        ts['timestamp'] = pd.to_datetime(ts['date'].astype(str) + ' ' + ts['time'])
        ts = ts[['timestamp', 'wd', 'ws']]
    elif 'timestamp' not in ts.columns:
        if pd.api.types.is_datetime64_any_dtype(ts.index):
            ts['timestamp'] = ts.index
            ts.reset_index(drop=True, inplace=True)
        else:
            ts = ts[['time', 'wd', 'ws']].rename(columns={'time': 'timestamp'})
    
    # Convert to frequency table
    
    WR = ts_to_freq_df(ts)
    WR['wd'] = pd.to_numeric(WR['wd'], errors='coerce')
    WR['ws'] = pd.to_numeric(WR['ws'], errors='coerce')
    
    # Extract wind data arrays - FIXED: Ensure proper frequency array formatting
    wd_array = np.array(sorted(WR["wd"].unique()), dtype=float)
    ws_array = np.array(sorted(WR["ws"].unique()), dtype=float)
    
    # Create frequency matrix that matches FLORIS interface expectations
    freq = WR.set_index(['wd','ws']).unstack().values
    
    print(f"   Layout file: {pr.inputLayoutFile}")
    print(f"   Total turbines in file: {len(turbine_map)}")
    print(f"   Internal turbines for FLORIS: {len(internal_layout)}")
    print(f"   Wind conditions: {len(wd_array)} directions, {len(ws_array)} speeds")
    print(f"   Time series length: {len(ts)} timestamps")
    print(f"   Frequency table: {len(WR)} unique wind conditions")
    print(f"   Wind direction range: [{wd_array.min():.1f}°, {wd_array.max():.1f}°]")
    print(f"   Wind speed range: [{ws_array.min():.1f}, {ws_array.max():.1f}] m/s")
    
    # Initialize FLORIS with internal layout and wind conditions
    fi.reinitialize(
        layout=(internal_layout['x'], internal_layout['y']),
        turbine_type=internal_layout['turb'].tolist() if 'turb' in internal_layout.columns else None,
        wind_directions=wd_array,
        wind_speeds=ws_array
    )
    
    print(f"   FLORIS wind directions: {fi.floris.flow_field.n_wind_directions}")
    print(f"   FLORIS wind speeds: {fi.floris.flow_field.n_wind_speeds}")
    
    # Set reference wind height to match turbine hub heights
    log_file = 'OutputInitial/Initial.summary.txt'
    
    # Calculate reference height using internal turbines
    if 'turb' in internal_layout.columns:
        internal_turb = internal_layout['turb'].unique()
    else:
        internal_turb = ['default']
    
    if len(internal_turb) > 1:
        # Multiple internal turbine types - calculate average hub height
        hhs = []
        for i, turb in enumerate(internal_turb):
            hhs.append(fi.floris.farm.hub_heights[i])
        ref_ht = sum(hhs)/len(hhs)
        message = f'Multiple internal turbine types detected. Reference wind height set to average hub height: {ref_ht:.1f}m'
        print(f"   {message}")
        write_log(message, log_file)
    else:
        # Single internal turbine type - use its hub height
        ref_ht = fi.floris.farm.hub_heights[0]
        message = f'Single internal turbine type detected. Reference wind height set to hub height: {ref_ht:.1f}m'
        print(f"   {message}")
        write_log(message, log_file)
    
    # Apply the reference height to FLORIS
    fi.floris.flow_field.reference_wind_height = ref_ht
    print(f"   ✓ Reference wind height updated to {ref_ht:.1f}m")
    
    # Calculate AEP 
    initial_aep = fi.get_farm_AEP(freq=freq) / 1E9  # Convert to GWh
    
    # Calculate some basic turbine performance metrics
    fi.calculate_wake()
    turbine_powers = fi.get_turbine_powers() / 1E6  # Convert to MW
    
    # Calculate average power across all wind conditions
    avg_total_power = np.mean(np.sum(turbine_powers, axis=2))  # Sum across turbines, average across conditions
    avg_power_per_turbine = np.mean(turbine_powers)
    capacity_factor = avg_power_per_turbine / 3.6 if avg_power_per_turbine > 0 else 0  # Assuming 3.6 MW rated
    
    # Try to calculate gross AEP (without wakes) for comparison
    try:
        # Calculate gross AEP by temporarily disabling wakes
        fi.calculate_no_wake()
        gross_powers = fi.get_turbine_powers() / 1E6
        gross_aep = fi.get_farm_AEP(freq=freq) / 1E9
        wake_losses = ((gross_aep - initial_aep) / gross_aep) * 100 if gross_aep > 0 else 0
        
        # Restore wake calculations for rest of workflow
        fi.calculate_wake()
        
        print(f"   🌀 Net AEP (with wakes): {initial_aep:.2f} GWh")
        print(f"   💨 Gross AEP (no wakes): {gross_aep:.2f} GWh")
        print(f"   📉 Wake losses: {wake_losses:.1f}%")
        print(f"   ⚡ Average farm power: {avg_total_power:.1f} MW")
        print(f"   📊 Average power per turbine: {avg_power_per_turbine:.2f} MW")
        print(f"   📈 Estimated capacity factor: {capacity_factor:.3f}")
        print(f"   🎯 AEP per turbine: {initial_aep/len(internal_layout):.3f} GWh")
        
    except Exception as e:
        print(f"   🌀 Net AEP (with wakes): {initial_aep:.2f} GWh")
        print(f"   ⚠️  Could not calculate gross AEP: {e}")
        print(f"   📊 Using net AEP for optimization")
        print(f"   ⚡ Average farm power: {avg_total_power:.1f} MW")
        print(f"   📊 Average power per turbine: {avg_power_per_turbine:.2f} MW")
        print(f"   📈 Estimated capacity factor: {capacity_factor:.3f}")
        print(f"   🎯 AEP per turbine: {initial_aep/len(internal_layout):.3f} GWh")
    
    # Save initial results
    os.makedirs('OutputInitial', exist_ok=True)
    
    # Save layout (internal turbines only)
    result_df = internal_layout.copy()
    result_df['aep_initial'] = initial_aep / len(internal_layout)  # Per turbine
    result_df.to_csv('OutputInitial/Initial.layout.csv', index=False)
    
    # Save summary
    with open('OutputInitial/Initial.summary.txt', 'w') as f:
        f.write(f"Initial FLORIS Simulation Results\n")
        f.write(f"================================\n")
        f.write(f"Total AEP: {initial_aep:.2f} GWh\n")
        f.write(f"Number of internal turbines: {len(internal_layout)}\n")
        f.write(f"AEP per turbine: {initial_aep/len(internal_layout):.3f} GWh\n")
        f.write(f"Simulation time: {time.time() - start_time:.1f} seconds\n")
    
    step_time = time.time() - start_time
    print(f"✅ Initial simulation complete: {initial_aep:.2f} GWh in {step_time:.1f}s")
    
    return fi, freq, internal_layout, initial_aep

def run_layoutOpt(fi, freq, turbine_map, initial_aep, 
                  plot_iterations=None, iteration_interval=None, use_external_turbines=None, 
                  plot_farm_analysis=None, farm_sector_width=None, farm_grouping=None, 
                  analysis_directions=None, max_distance_km=None, use_enhanced_filtering=None):
    """Step 2: Run hybrid layout optimization"""
    print("="*80)
    print("🚀 STEP 2: Running Hybrid Layout Optimization")
    print("="*80)
    
    start_time = time.time()
    
    # Read all parameters from params.py if not provided
    if max_distance_km is None:
        max_distance_km = pr.max_distance_km
    if plot_iterations is None:
        plot_iterations = pr.plot_iterations
    if iteration_interval is None:
        iteration_interval = pr.iteration_interval
    if use_external_turbines is None:
        use_external_turbines = pr.use_external_turbines
    if plot_farm_analysis is None:
        plot_farm_analysis = pr.plot_farm_analysis
    if farm_sector_width is None:
        farm_sector_width = pr.farm_sector_width
    if farm_grouping is None:
        farm_grouping = pr.farm_grouping
    if analysis_directions is None:
        analysis_directions = pr.analysis_directions
    if use_enhanced_filtering is None:
        use_enhanced_filtering = pr.use_enhanced_filtering
    
    print(f"   Parameters loaded from params.py:")
    print(f"     - Distance threshold: {max_distance_km} km")
    print(f"     - External turbines: {use_external_turbines}")
    print(f"     - Plot farm analysis: {plot_farm_analysis}")
    print(f"     - Sector width: {farm_sector_width}°")
    print(f"     - Farm grouping: {farm_grouping}")
    print(f"     - Enhanced filtering: {use_enhanced_filtering}")
    
    # Prepare turbine weights for external optimization
    turbine_weights = np.ones(len(turbine_map))  # All turbines optimizable initially
    
    # Check if we should use external turbines from external layout file
    external_layout_file = './Input/External.layout.csv'
    if use_external_turbines and os.path.exists(external_layout_file):
        external_df = pd.read_csv(external_layout_file, header=0)
        print(f"   External turbines loaded: {len(external_df)}")
        
        # Standardize column names for external layout
        if 'easting' in external_df.columns and 'northing' in external_df.columns:
            external_df = external_df.rename(columns={'easting': 'x', 'northing': 'y'})
            print("   Renamed easting/northing to x/y in external layout")
        
        # Ensure external flag
        if 'external' not in external_df.columns:
            external_df['external'] = True
        
        # Combine layouts
        internal_df = turbine_map.copy()
        internal_df['weight'] = 1  # Internal turbines to optimize
        if 'external' not in internal_df.columns:
            internal_df['external'] = False
        
        external_df['weight'] = 0  # External turbines fixed
        
        # Apply distance filtering to external turbines
        if max_distance_km and max_distance_km > 0:
            # Calculate distances from internal centroid
            internal_cx = np.mean(internal_df['x'])
            internal_cy = np.mean(internal_df['y'])
            distances = np.sqrt((external_df['x'] - internal_cx)**2 + (external_df['y'] - internal_cy)**2) / 1000
            
            # Filter external turbines within distance threshold
            mask = distances <= max_distance_km
            filtered_external_df = external_df[mask].copy()
            
            print(f"   Distance filtering: {np.sum(~mask)} external turbines removed (>{max_distance_km}km)")
            print(f"   External turbines within {max_distance_km}km: {len(filtered_external_df)}")
            
            external_df = filtered_external_df
        
        # Create combined layout
        combined_df = pd.concat([internal_df, external_df], ignore_index=True)
        
        # Update FLORIS with combined layout
        layout_x = combined_df['x'].values
        layout_y = combined_df['y'].values
        turbine_weights = combined_df['weight'].values
        
        # Get turbine types if available and normalize them
        if 'turb' in combined_df.columns:
            combined_turbine_types = combined_df['turb'].values
            normalized_types = normalize_turbine_types(combined_turbine_types)
            
            # Show mapping results
            for orig, norm in zip(combined_turbine_types, normalized_types):
                if orig != norm:
                    print(f"   Mapped turbine type: {orig} → {norm}")
            
            fi.reinitialize(layout=(layout_x, layout_y), turbine_type=normalized_types)
            print(f"   Combined layout: {len(layout_x)} turbines with normalized types")
        else:
            fi.reinitialize(layout=(layout_x, layout_y))
            print(f"   Combined layout: {len(layout_x)} turbines without types")
        
        # Update reference wind height for combined layout
        log_file = 'Output/Optimization.summary.txt'
        
        # For combined layout, use internal turbines for reference height calculation
        if 'turb' in combined_df.columns:
            internal_indices = combined_df.loc[combined_df['weight'] > 0].index
            internal_turb_types = combined_df.loc[combined_df['weight'] > 0, 'turb'].unique()
            
            if len(internal_turb_types) > 1:
                # Multiple internal turbine types - calculate average hub height
                hhs = [fi.floris.farm.hub_heights[idx] for idx in internal_indices]
                ref_ht = sum(hhs)/len(hhs)
                message = f'Combined layout: Multiple internal turbine types. Reference wind height set to average: {ref_ht:.1f}m'
            else:
                # Single internal turbine type - use its hub height
                ref_ht = fi.floris.farm.hub_heights[internal_indices[0]]
                message = f'Combined layout: Single internal turbine type. Reference wind height set to: {ref_ht:.1f}m'
        else:
            # No turbine type column, use first internal turbine
            internal_indices = np.where(turbine_weights > 0)[0]
            ref_ht = fi.floris.farm.hub_heights[internal_indices[0]]
            message = f'Combined layout: Reference wind height set to first internal turbine hub height: {ref_ht:.1f}m'
        
        fi.floris.flow_field.reference_wind_height = ref_ht
        print(f"   ✓ {message}")
        write_log(message, log_file)
        
        print(f"   Internal turbines to optimize: {np.sum(turbine_weights > 0)}")
        print(f"   External turbines (fixed): {np.sum(turbine_weights == 0)}")
    else:
        print(f"   Internal-only optimization: {len(turbine_map)} turbines")
        # Reference height already set in run_florisInitial, no need to change
    
    # Load boundaries
    boundaries = util.load_boundaries(pr.boundariesFile)
    
    # Plot initial layout before optimization starts
    print("   Creating initial layout visualization...")
    x0 = fi.layout_x
    y0 = fi.layout_y
    plot_layout_beforeOptim(x0, y0, boundaries, "Input/0.png", "Initial Layout Before Optimization")
    
    # Plot external turbine comparison if external turbines exist
    if use_external_turbines and os.path.exists(external_layout_file):
        # Load original external layout for comparison plots
        original_external_df = pd.read_csv(external_layout_file, header=0)
        
        # Standardize column names
        if 'easting' in original_external_df.columns and 'northing' in original_external_df.columns:
            original_external_df = original_external_df.rename(columns={'easting': 'x', 'northing': 'y'})
        
        # Ensure external flags
        if 'external' not in turbine_map.columns:
            turbine_map['external'] = False
        if 'external' not in original_external_df.columns:
            original_external_df['external'] = True
        
        # Extract internal and external coordinates
        internal_x = turbine_map['x'].values
        internal_y = turbine_map['y'].values
        all_external_x = original_external_df['x'].values
        all_external_y = original_external_df['y'].values
        
        # Apply distance filtering for plotting (if not already done in optimization setup)
        if max_distance_km and max_distance_km > 0:
            # Calculate distances from internal centroid
            internal_cx = np.mean(internal_x)
            internal_cy = np.mean(internal_y)
            distances = np.sqrt((original_external_df['x'] - internal_cx)**2 + (original_external_df['y'] - internal_cy)**2) / 1000
            filtered_external_df = original_external_df[distances <= max_distance_km].copy()
            
            filtered_external_x = filtered_external_df['x'].values if len(filtered_external_df) > 0 else np.array([])
            filtered_external_y = filtered_external_df['y'].values if len(filtered_external_df) > 0 else np.array([])
        else:
            filtered_external_x = all_external_x
            filtered_external_y = all_external_y
            filtered_external_df = original_external_df
        
        # Create enhanced comparison plot
        plot_external_comparison_enhanced(
            internal_x, internal_y, 
            all_external_x, all_external_y,
            filtered_external_x, filtered_external_y,
            boundaries, "Output", "Pre-Optimization",
            max_distance_km=max_distance_km
        )
        
        # Also create standard comparison plot for compatibility
        plot_external_comparison(internal_x, internal_y, all_external_x, all_external_y, boundaries, "Output", "Pre-Optimization")
        
        # Add enhanced farm analysis if enabled
        if plot_farm_analysis:
            print("   Creating enhanced farm analysis visualizations...")
            plot_enhanced_farm_analysis(
                turbine_map, original_external_df,
                boundaries=boundaries,
                output_dir="Output",
                max_distance_km=max_distance_km,
                sector_width=farm_sector_width,
                farm_grouping=farm_grouping,
                analysis_directions=analysis_directions
            )
            print("   Enhanced farm analysis plots created")
        
        print(f"   Enhanced layout comparison plots created:")
        print(f"     - Internal turbines: {len(internal_x)}")
        print(f"     - All external turbines: {len(all_external_x)}")
        print(f"     - External within {max_distance_km}km: {len(filtered_external_x)}")
        
        # Optional: Create enhanced farm manager for directional filtering
        if use_enhanced_filtering:
            print("   Setting up enhanced directional farm filtering...")
            try:
                enhanced_manager = create_enhanced_farm_manager(
                    turbine_map, original_external_df,
                    max_distance_km=max_distance_km,
                    sector_width=farm_sector_width,
                    farm_grouping=farm_grouping
                )
                
                # Get filtering statistics
                stats = enhanced_manager.get_filtering_statistics()
                print(f"   Enhanced filtering statistics:")
                print(f"     - Detected {stats['n_farms']} external farms")
                print(f"     - Average external turbines per direction: {np.mean([d['n_turbines'] for d in stats['by_direction'].values()]):.1f}")
                print(f"     - Reduction vs all external: {100 * (1 - np.mean([d['n_turbines'] for d in stats['by_direction'].values()]) / len(external_df)):.1f}%")
                
                # Store for potential use in optimization
                # Note: Current hybrid optimizer doesn't use per-direction filtering
                # This is for visualization and analysis purposes
                
            except Exception as e:
                print(f"   Warning: Enhanced filtering setup failed: {e}")
                print("   Continuing with standard distance-based filtering...")
    
    # Debug turbine weights before passing to optimizer
    print(f"   Turbine weights debug: {turbine_weights}")
    print(f"   Turbine weights shape: {len(turbine_weights)}")
    print(f"   Unique values in weights: {np.unique(turbine_weights)}")
    
    # Get algorithm recommendations based on problem size
    n_internal = int(np.sum(turbine_weights > 0))
    n_external = int(np.sum(turbine_weights == 0))
    
    print(f"   Debug: n_internal = {n_internal}, n_external = {n_external}")
    
    recommendations = get_algorithm_recommendations(n_internal, n_external, pr.nGenerations)
    print(f"   Algorithm recommendation: {recommendations['rationale']}")
    
    # Initialize smart initialization manager
    smart_init = SmartInitializationManager(
        n_workers=pr.n_workers,
        timeout_per_eval=pr.timeout_per_eval,
        use_monitoring=True
    )
    
    # Create hybrid optimizer with recommended parameters
    hybrid_optimizer = HybridLayoutOptimizationPymoo(
        fi=fi,
        boundaries=boundaries,
        freq=freq,
        min_dist=pr.min_dist,
        n_gen=pr.nGenerations,
        pop_size=recommendations['recommended_pop_size'],
        turbines_weights=turbine_weights,
        n_workers=pr.n_workers,
        timeout_per_eval=pr.timeout_per_eval,
        hybrid_stages=recommendations['stages'],
        stage_generations=recommendations['stage_generations'],
        stage_transitions='adaptive'
    )
    
    # Store plotting parameters in the optimizer for potential use
    hybrid_optimizer.plot_iterations = plot_iterations
    hybrid_optimizer.iteration_interval = iteration_interval
    hybrid_optimizer.boundaries = boundaries
    
    print(f"   Hybrid stages: {' → '.join(recommendations['stages'])}")
    print(f"   Stage generations: {recommendations['stage_generations']}")
    print(f"   Population size: {recommendations['recommended_pop_size']}")
    print(f"   Parallel workers: {pr.n_workers}")
    
    # Run optimization
    opt_result = hybrid_optimizer.optimize()
    
    # Get optimized layout
    opt_x, opt_y = hybrid_optimizer.get_optimized_locs()
    
    # Calculate optimized AEP
    fi.reinitialize(layout=(opt_x, opt_y))
    opt_aep = fi.get_farm_AEP(freq=freq) / 1E9  # Convert to GWh
    
    # Save optimization results
    os.makedirs('Output', exist_ok=True)
    
    # Save optimized layout
    if use_external_turbines and os.path.exists(external_layout_file):
        # Combined layout case
        opt_df = combined_df.copy()
        opt_df['x'] = opt_x
        opt_df['y'] = opt_y
        opt_df['aep_optimized'] = opt_aep / len(opt_df)
    else:
        # Internal-only case
        opt_df = turbine_map.copy()
        opt_df['x'] = opt_x
        opt_df['y'] = opt_y
        opt_df['aep_optimized'] = opt_aep / len(opt_df)
    
    opt_df.to_csv('Output/Optimized.layout.csv', index=False)
    
    # Calculate improvement
    improvement = ((opt_aep - initial_aep) / initial_aep) * 100
    
    # Save optimization summary
    with open('Output/Optimization.summary.txt', 'w') as f:
        f.write(f"Hybrid Layout Optimization Results\n")
        f.write(f"=================================\n")
        f.write(f"Initial AEP: {initial_aep:.2f} GWh\n")
        f.write(f"Optimized AEP: {opt_aep:.2f} GWh\n")
        f.write(f"Improvement: {improvement:.2f}%\n")
        f.write(f"Gain: {opt_aep - initial_aep:.2f} GWh\n")
        f.write(f"\nOptimization Settings:\n")
        f.write(f"Hybrid stages: {' → '.join(recommendations['stages'])}\n")
        f.write(f"Stage generations: {recommendations['stage_generations']}\n")
        f.write(f"Population size: {recommendations['recommended_pop_size']}\n")
        f.write(f"Internal turbines: {n_internal}\n")
        f.write(f"External turbines: {n_external}\n")
        f.write(f"Parallel workers: {pr.n_workers}\n")
        f.write(f"Optimization time: {time.time() - start_time:.1f} seconds\n")
        
        # Add stage performance if available
        if hasattr(hybrid_optimizer, 'monitor_data') and 'stage_performance' in hybrid_optimizer.monitor_data:
            f.write(f"\nStage Performance:\n")
            for stage_name, stage_data in hybrid_optimizer.monitor_data['stage_performance'].items():
                f.write(f"  {stage_name}: {stage_data['best_f']:.6f} in {stage_data['time']:.1f}s\n")
    
    # Plot final optimized layout
    print("   Creating final optimized layout visualization...")
    plot_layout_beforeOptim(opt_x, opt_y, boundaries, "Output/optimized_layout.png", "Final Optimized Layout")
    
    # Plot final external comparison if external turbines exist
    if use_external_turbines and os.path.exists(external_layout_file):
        external_df = pd.read_csv(external_layout_file, header=0)
        
        # Standardize column names
        if 'easting' in external_df.columns and 'northing' in external_df.columns:
            external_df = external_df.rename(columns={'easting': 'x', 'northing': 'y'})
        
        # For final plot, we need to separate internal and external from combined layout
        if len(turbine_weights) > len(turbine_map):
            # Combined layout case
            n_internal = len(turbine_map)
            final_internal_x = opt_x[:n_internal]
            final_internal_y = opt_y[:n_internal]
            final_external_x = opt_x[n_internal:]
            final_external_y = opt_y[n_internal:]
        else:
            # Internal-only case, get external separately
            final_internal_x = opt_x
            final_internal_y = opt_y
            final_external_x = external_df['x'].values
            final_external_y = external_df['y'].values
        
        # Create filtered external for enhanced comparison
        filtered_external_df = external_df.copy()
        if max_distance_km and max_distance_km > 0:
            # Calculate distances from optimized internal centroid
            internal_cx = np.mean(final_internal_x)
            internal_cy = np.mean(final_internal_y)
            distances = np.sqrt((external_df['x'] - internal_cx)**2 + (external_df['y'] - internal_cy)**2) / 1000
            filtered_external_df = external_df[distances <= max_distance_km].copy()
        
        final_filtered_external_x = filtered_external_df['x'].values if len(filtered_external_df) > 0 else np.array([])
        final_filtered_external_y = filtered_external_df['y'].values if len(filtered_external_df) > 0 else np.array([])
        
        # Create enhanced comparison plot
        plot_external_comparison_enhanced(
            final_internal_x, final_internal_y, 
            final_external_x, final_external_y,
            final_filtered_external_x, final_filtered_external_y,
            boundaries, "Output", "Post-Optimization",
            max_distance_km=max_distance_km
        )
        
        # Also create standard comparison plot for compatibility
        plot_external_comparison(final_internal_x, final_internal_y, final_external_x, final_external_y, boundaries, "Output", "Post-Optimization")
        print(f"   Final enhanced layout comparison plots created")
    
    step_time = time.time() - start_time
    print(f"✅ Hybrid optimization complete: {improvement:.2f}% improvement in {step_time:.1f}s")
    
    # Extract turbine types for Step 3 and normalize them
    if use_external_turbines and os.path.exists(external_layout_file):
        if 'turb' in opt_df.columns:
            raw_types = opt_df['turb'].values
            turbine_types = normalize_turbine_types(raw_types)
        else:
            turbine_types = None
    else:
        if 'turb' in turbine_map.columns:
            raw_types = turbine_map['turb'].values
            turbine_types = normalize_turbine_types(raw_types)
        else:
            turbine_types = None
    
    return opt_x, opt_y, opt_aep, improvement, turbine_types

def run_florisInternal(fi, opt_x, opt_y, freq, turbine_types=None):
    """Step 3: Run detailed FLORIS simulation with INTERNAL turbines only"""
    print("="*80)
    print("🔍 STEP 3: Running Detailed Internal FLORIS Simulation")
    print("="*80)
    
    start_time = time.time()
    
    # Filter to get only internal turbines (first 10)
    n_internal = 10
    internal_x = opt_x[:n_internal]
    internal_y = opt_y[:n_internal]
    internal_types = turbine_types[:n_internal] if turbine_types is not None else None
    
    # Set optimized layout - INTERNAL ONLY
    if internal_types is not None:
        fi.reinitialize(layout=(internal_x, internal_y), turbine_type=internal_types)
        print(f"   Reinitializing FLORIS with {len(internal_x)} INTERNAL turbines and turbine types")
    else:
        fi.reinitialize(layout=(internal_x, internal_y))
        print(f"   Reinitializing FLORIS with {len(internal_x)} INTERNAL turbines (no turbine types)")
    
    # Calculate detailed AEP
    detailed_aep = fi.get_farm_AEP(freq=freq) / 1E9  # Convert to GWh
    
    # Calculate wake losses for each turbine using corrected method
    fi.calculate_wake()
    turbine_powers_3d = fi.get_turbine_powers() / 1E6  # Convert to MW, shape: (n_wd, n_ws, n_turbines)
    
    # Calculate average power per turbine across all wind conditions
    # Weight by frequency to get realistic average powers
    total_power_per_turbine = np.zeros(len(internal_x))
    total_frequency = 0
    
    # Get wind arrays for indexing
    WR = ts_to_freq_df(pd.read_csv(pr.windRoseFile, sep=' '))
    WR['wd'] = pd.to_numeric(WR['wd'], errors='coerce')
    WR['ws'] = pd.to_numeric(WR['ws'], errors='coerce')
    wd_array = np.array(sorted(WR["wd"].unique()), dtype=float)
    ws_array = np.array(sorted(WR["ws"].unique()), dtype=float)
    
    for _, row in WR.iterrows():
        ws_idx = np.where(ws_array == row['ws'])[0][0]
        wd_idx = np.where(wd_array == row['wd'])[0][0]
        powers = turbine_powers_3d[wd_idx, ws_idx, :]  # Power for this wind condition
        total_power_per_turbine += powers * row['freq']
        total_frequency += row['freq']
    
    # Average power per turbine
    avg_power_per_turbine = total_power_per_turbine / total_frequency
    
    # Save detailed results
    os.makedirs('OutputInternal', exist_ok=True)
    
    # Save detailed layout with power data
    detailed_df = pd.DataFrame({
        'x': internal_x,
        'y': internal_y,
        'power_mw': avg_power_per_turbine,
        'aep_detailed': detailed_aep / len(internal_x)
    })
    detailed_df.to_csv('OutputInternal/Detailed.layout.csv', index=False)
    
    # Save detailed summary
    with open('OutputInternal/Detailed.summary.txt', 'w') as f:
        f.write(f"Detailed Internal FLORIS Simulation Results\n")
        f.write(f"==========================================\n")
        f.write(f"Total AEP: {detailed_aep:.2f} GWh\n")
        f.write(f"Average power per turbine: {np.mean(avg_power_per_turbine):.2f} MW\n")
        f.write(f"Power range: {np.min(avg_power_per_turbine):.2f} - {np.max(avg_power_per_turbine):.2f} MW\n")
        f.write(f"Power std dev: {np.std(avg_power_per_turbine):.2f} MW\n")
        f.write(f"Simulation time: {time.time() - start_time:.1f} seconds\n")
    
    step_time = time.time() - start_time
    print(f"✅ Detailed simulation complete: {detailed_aep:.2f} GWh in {step_time:.1f}s")
    
    return detailed_aep

def run_floris(fi, opt_x, opt_y, freq, turbine_types=None):
    """Step 4: Run final FLORIS simulation and generate comprehensive results"""
    print("="*80)
    print("📊 STEP 4: Running Final FLORIS Simulation and Analysis")
    print("="*80)
    
    start_time = time.time()
    
    # Set final layout
    if turbine_types is not None:
        fi.reinitialize(layout=(opt_x, opt_y), turbine_type=turbine_types)
        print(f"   Reinitializing FLORIS with {len(opt_x)} turbines and turbine types")
    else:
        fi.reinitialize(layout=(opt_x, opt_y))
        print(f"   Reinitializing FLORIS with {len(opt_x)} turbines (no turbine types)")
    
    # Calculate final AEP
    final_aep = fi.get_farm_AEP(freq=freq) / 1E9  # Convert to GWh
    
    # Generate comprehensive analysis
    fi.calculate_wake()
    
    # Get turbine data using corrected method
    turbine_powers_3d = fi.get_turbine_powers() / 1E6  # MW, shape: (n_wd, n_ws, n_turbines)
    wind_directions = fi.floris.flow_field.wind_directions
    wind_speeds = fi.floris.flow_field.wind_speeds
    
    # Calculate average power per turbine across all wind conditions
    # Weight by frequency to get realistic average powers
    total_power_per_turbine = np.zeros(len(opt_x))
    total_frequency = 0
    
    # Get wind arrays for indexing
    WR = ts_to_freq_df(pd.read_csv(pr.windRoseFile, sep=' '))
    WR['wd'] = pd.to_numeric(WR['wd'], errors='coerce')
    WR['ws'] = pd.to_numeric(WR['ws'], errors='coerce')
    wd_array = np.array(sorted(WR["wd"].unique()), dtype=float)
    ws_array = np.array(sorted(WR["ws"].unique()), dtype=float)
    
    for _, row in WR.iterrows():
        ws_idx = np.where(ws_array == row['ws'])[0][0]
        wd_idx = np.where(wd_array == row['wd'])[0][0]
        powers = turbine_powers_3d[wd_idx, ws_idx, :]  # Power for this wind condition
        total_power_per_turbine += powers * row['freq']
        total_frequency += row['freq']
    
    # Average power per turbine
    avg_power_per_turbine = total_power_per_turbine / total_frequency
    
    # Save final results
    final_df = pd.DataFrame({
        'turbine_id': range(len(opt_x)),
        'x': opt_x,
        'y': opt_y,
        'power_mw': avg_power_per_turbine,
        'aep_contribution': final_aep / len(opt_x),
        'capacity_factor': avg_power_per_turbine / 3.6  # Assuming 3.6 MW rated power
    })
    final_df.to_csv('Output/Final.layout.csv', index=False)
    
    # Generate comprehensive summary
    with open('Output/Final.summary.txt', 'w') as f:
        f.write(f"Final Hybrid Optimization Results Summary\n")
        f.write(f"========================================\n")
        f.write(f"Final AEP: {final_aep:.2f} GWh\n")
        f.write(f"Number of turbines: {len(opt_x)}\n")
        f.write(f"Farm capacity factor: {np.mean(final_df['capacity_factor']):.3f}\n")
        f.write(f"Power statistics:\n")
        f.write(f"  Mean: {np.mean(avg_power_per_turbine):.2f} MW\n")
        f.write(f"  Min:  {np.min(avg_power_per_turbine):.2f} MW\n")
        f.write(f"  Max:  {np.max(avg_power_per_turbine):.2f} MW\n")
        f.write(f"  Std:  {np.std(avg_power_per_turbine):.2f} MW\n")
        f.write(f"\nWind conditions analyzed:\n")
        f.write(f"  Wind directions: {len(np.unique(wind_directions))}\n")
        f.write(f"  Wind speeds: {len(np.unique(wind_speeds))}\n")
        f.write(f"Final simulation time: {time.time() - start_time:.1f} seconds\n")
    
    step_time = time.time() - start_time
    print(f"✅ Final analysis complete: {final_aep:.2f} GWh in {step_time:.1f}s")
    
    return final_aep

def extract_internal_aep_from_combined(fi, freq, n_internal=10, reference_gross_powers=None):
    """Extract AEP contribution from internal turbines only from a combined layout using utilities"""
    return calculate_gross_net_aep_efficient(fi, freq, n_internal=n_internal, reference_gross_powers=reference_gross_powers)

def calculate_internal_only_aep(fi, opt_x, opt_y, freq, turbine_types=None, reference_gross_powers=None):
    """Calculate AEP for internal turbines only (without external turbines) using utilities"""
    print("\n📊 Calculating internal-only AEP (no external turbines)...")
    start_time = time.time()
    
    # Filter to get only internal turbines
    n_internal = 10  # From the original layout file
    internal_x = opt_x[:n_internal]
    internal_y = opt_y[:n_internal]
    internal_types = turbine_types[:n_internal] if turbine_types is not None else None
    
    # Initialize FLORIS with internal turbines only
    if internal_types is not None:
        fi.reinitialize(layout=(internal_x, internal_y), turbine_type=internal_types)
    else:
        fi.reinitialize(layout=(internal_x, internal_y))
    
    # Use efficient calculation from utilities
    gross_aep, net_aep, wake_losses = calculate_gross_net_aep_efficient(fi, freq, n_internal=n_internal, reference_gross_powers=reference_gross_powers)
    
    step_time = time.time() - start_time
    print(f"✅ Internal-only analysis complete in {step_time:.1f}s")
    return gross_aep, net_aep, wake_losses

def main(plot_iterations=None, iteration_interval=None):
    """Main execution function - Complete 4-step hybrid optimization workflow
    
    Args:
        plot_iterations: Enable iteration plotting during optimization (None = use params.py)
        iteration_interval: Plot every N generations (None = use params.py)
    """
    # Read parameters from params.py if not provided
    if plot_iterations is None:
        plot_iterations = pr.plot_iterations
    if iteration_interval is None:
        iteration_interval = pr.iteration_interval
    print("🌪️  HYBRID EXTERNAL TURBINE LAYOUT OPTIMIZATION")
    print("=" * 80)
    print(f"Configuration: {pr.FLORIS_CONFIG}")
    print(f"Layout file: {pr.inputLayoutFile}")
    print(f"Wind rose: {pr.windRoseFile}")
    print(f"Boundaries: {pr.boundariesFile}")
    print(f"Generations: {pr.nGenerations}")
    print(f"Population: {pr.PopSize}")
    print(f"Workers: {pr.n_workers}")
    print("=" * 80)
    
    overall_start = time.time()
    
    try:
        # Step 1: Initial FLORIS simulation
        fi, freq, turbine_map, initial_aep = run_florisInitial()
        
        # Calculate reference gross powers (constant for all layouts)
        global REFERENCE_GROSS_POWERS
        print("📊 Calculating reference gross powers for consistent analysis...")
        fi.calculate_no_wake()
        REFERENCE_GROSS_POWERS = fi.get_turbine_powers() / 1E6  # MW, shape: (n_wd, n_ws, n_turbines)
        print(f"✅ Reference gross powers calculated for {REFERENCE_GROSS_POWERS.shape[-1]} turbines")
        
        # Step 2: Hybrid layout optimization - all parameters from params.py
        opt_x, opt_y, opt_aep, improvement, turbine_types = run_layoutOpt(
            fi, freq, turbine_map, initial_aep
        )
        
        # Step 3: Detailed internal simulation
        detailed_aep = run_florisInternal(fi, opt_x, opt_y, freq, turbine_types)
        
        # Step 4: Final comprehensive analysis (with external turbines)
        final_aep = run_floris(fi, opt_x, opt_y, freq, turbine_types)
        
        # Step 5: Extract internal AEP from optimized combined layout with gross/net analysis
        print("\n📊 Extracting internal park AEP from optimized combined layout...")
        opt_gross_aep, opt_net_aep, opt_wake_losses = extract_internal_aep_from_combined(fi, freq, n_internal=10, reference_gross_powers=REFERENCE_GROSS_POWERS)
        
        # Step 6: Calculate internal-only AEP with gross/net analysis
        internal_gross_aep, internal_net_aep, internal_wake_losses = calculate_internal_only_aep(fi, opt_x, opt_y, freq, turbine_types, reference_gross_powers=REFERENCE_GROSS_POWERS)
        
        # Step 7: Calculate baseline internal AEP from initial layout
        print("\n📊 Calculating baseline internal park AEP...")
        # Re-run initial layout but extract internal AEP (now returns internal-only layout)
        fi_baseline, freq_baseline, turbine_map_baseline, _ = run_florisInitial()
        # Since run_florisInitial now returns internal-only layout, use efficient calculation
        baseline_gross, baseline_net, baseline_wake_loss = calculate_gross_net_aep_efficient(fi_baseline, freq_baseline, n_internal=len(turbine_map_baseline), reference_gross_powers=REFERENCE_GROSS_POWERS)
        
        # Calculate metrics
        # External wake impact (comparing internal-only vs combined layout)
        external_wake_impact = internal_net_aep - opt_net_aep
        external_wake_impact_pct = (external_wake_impact / internal_net_aep) * 100 if internal_net_aep > 0 else 0
        
        # Layout optimization improvement (net AEP)
        optimization_improvement = opt_net_aep - baseline_net
        optimization_improvement_pct = (optimization_improvement / baseline_net) * 100 if baseline_net > 0 else 0
        
        # Wake loss improvement
        wake_loss_improvement = baseline_wake_loss - opt_wake_losses
        
        print(f"✅ Comprehensive wake loss analysis complete")
        print(f"   Baseline: Gross {baseline_gross:.2f} GWh, Net {baseline_net:.2f} GWh, Losses {baseline_wake_loss:.2f} GWh")
        print(f"   Optimized: Gross {opt_gross_aep:.2f} GWh, Net {opt_net_aep:.2f} GWh, Losses {opt_wake_losses:.2f} GWh")
        print(f"   Internal-only: Gross {internal_gross_aep:.2f} GWh, Net {internal_net_aep:.2f} GWh, Losses {internal_wake_losses:.2f} GWh")
        
        # Overall summary - Enhanced with comprehensive wake loss analysis
        total_time = time.time() - overall_start
        print("=" * 80)
        print("🎉 HYBRID EXTERNAL OPTIMIZATION WORKFLOW COMPLETE!")
        print("=" * 80)
        print(f"Baseline AEP (initial internal):      {baseline_net:.2f} GWh")
        print(f"Optimized AEP (internal park):        {opt_net_aep:.2f} GWh")
        print(f"Internal-only AEP (no external):      {internal_net_aep:.2f} GWh")
        print(f"Combined AEP (all turbines):          {final_aep:.2f} GWh")
        print()
        print(f"Improvement from optimization:        {optimization_improvement:.2f} GWh ({optimization_improvement_pct:.1f}%)")
        print(f"External wake impact:                 {external_wake_impact:.2f} GWh ({external_wake_impact_pct:.1f}%)")
        print()
        print("Wake Loss Analysis (Gross vs Net):")
        print(f"Baseline internal gross/net:          {baseline_gross:.2f}/{baseline_net:.2f} GWh (losses: {baseline_wake_loss:.2f} GWh)")
        print(f"Optimized internal gross/net:         {opt_gross_aep:.2f}/{opt_net_aep:.2f} GWh (losses: {opt_wake_losses:.2f} GWh)")
        print(f"Internal-only gross/net:              {internal_gross_aep:.2f}/{internal_net_aep:.2f} GWh (losses: {internal_wake_losses:.2f} GWh)")
        print(f"Wake loss improvement:                {wake_loss_improvement:.2f} GWh")
        print()
        print(f"Total workflow runtime:               {total_time:.1f} seconds")
        print("=" * 80)
        
        # Save overall progress
        with open('OverAllProgress.txt', 'w') as f:
            f.write(f"Hybrid External Turbine Layout Optimization - Complete Workflow\n")
            f.write(f"=============================================================\n")
            f.write(f"Execution time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Configuration: {pr.FLORIS_CONFIG}\n")
            f.write(f"Layout file: {pr.inputLayoutFile}\n")
            f.write(f"Wind rose: {pr.windRoseFile}\n")
            f.write(f"\nResults Summary:\n")
            f.write(f"Baseline AEP (initial internal):      {baseline_net:.2f} GWh\n")
            f.write(f"Optimized AEP (internal park):        {opt_net_aep:.2f} GWh\n")
            f.write(f"Internal-only AEP (no external):      {internal_net_aep:.2f} GWh\n")
            f.write(f"Combined AEP (all turbines):          {final_aep:.2f} GWh\n")
            f.write(f"Improvement from optimization:        {optimization_improvement:.2f} GWh ({optimization_improvement_pct:.1f}%)\n")
            f.write(f"External wake impact:                 {external_wake_impact:.2f} GWh ({external_wake_impact_pct:.1f}%)\n")
            f.write(f"\nWake Loss Analysis (Gross vs Net):\n")
            f.write(f"Baseline internal gross/net:          {baseline_gross:.2f}/{baseline_net:.2f} GWh (losses: {baseline_wake_loss:.2f} GWh)\n")
            f.write(f"Optimized internal gross/net:         {opt_gross_aep:.2f}/{opt_net_aep:.2f} GWh (losses: {opt_wake_losses:.2f} GWh)\n")
            f.write(f"Internal-only gross/net:              {internal_gross_aep:.2f}/{internal_net_aep:.2f} GWh (losses: {internal_wake_losses:.2f} GWh)\n")
            f.write(f"Wake loss improvement:                {wake_loss_improvement:.2f} GWh\n")
            f.write(f"\nTiming:\n")
            f.write(f"Total execution time: {total_time:.1f} seconds\n")
            f.write(f"Average time per step: {total_time/5:.1f} seconds\n")
            f.write(f"\nHybrid Algorithm Performance:\n")
            f.write(f"Algorithms used: GA → DE → PSO\n")
            f.write(f"Multi-stage optimization approach\n")
            f.write(f"External turbine handling: Smart filtering\n")
            f.write(f"Parallel workers: {pr.n_workers}\n")
        
        print(f"📁 Results saved to: Output/, OutputInitial/, OutputInternal/")
        print(f"📄 Overall progress: OverAllProgress.txt")
        
        return True
        
    except Exception as e:
        print(f"❌ OPTIMIZATION FAILED: {e}")
        import traceback
        traceback.print_exc()
        
        # Save error log
        with open('error_log.txt', 'w') as f:
            f.write(f"Hybrid Optimization Error\n")
            f.write(f"======================\n")
            f.write(f"Time: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"Error: {str(e)}\n")
            f.write(f"Traceback:\n")
            traceback.print_exc(file=f)
        
        return False

if __name__ == "__main__":
    # All parameters now read from params.py
    # Override here if needed for testing: main(plot_iterations=True, iteration_interval=1)
    success = main()
    sys.exit(0 if success else 1)
