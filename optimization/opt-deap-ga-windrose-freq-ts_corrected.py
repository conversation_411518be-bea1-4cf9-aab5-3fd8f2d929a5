#!/usr/bin/env python3
"""
DEAP-GA Internal Layout Optimization Script
==========================================
Replicates the exact 4-step workflow from opt-pymoo-windrose-freq-ts_corrected.py using DEAP-GA algorithm
Maintains identical structure, reporting format, and utility function integration

4-Step Workflow:
1. Calculate baseline AEP
2. Run layout optimization (DEAP-GA)
3. Simulate internal turbines only
4. Full wind farm simulation
5. Summary with gross/net AEP reporting
"""

import inspect
import sys
import copy
import os
import re
import shutil
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from multiprocessing import Process
from numpy import genfromtxt
from scipy.spatial.distance import cdist
from scipy.interpolate import NearestNDInterpolator
from datetime import datetime
from time import perf_counter as timerpc
from shapely.geometry import Polygon, LineString
import matplotlib.ticker as ticker
import matplotlib.patches as patches
from matplotlib.lines import Line2D
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# Project-specific imports
import params as pr
import optimizer_params as optimizer_etc
import wind_dir_dominant as winddir

# Import core utilities (maintaining compatibility with PyMoo workflow)
from utilities import (
    sanitize_name,
    GetAEPParallel,
    memory_footprint,
    write_log,
    custom_round,
    ts_to_freq_df,
    plot_layout_beforeOptim,
    plot_iteration_solution
)

# Import DEAP optimization components
from deap_optimization_manager import DEAPOptimizationManager, DEAPProblemWrapper, OptimizationConfig

# Set up work directory and paths (identical to PyMoo version)
workDir = str(Path(__file__).parent.absolute())
print(f"DEAP-GA Internal Layout Optimization Path: {workDir}")
OverAllProgress = f"{workDir}/OverAllProgress.txt"
print(f"OverAllProgress: {OverAllProgress}")

# Set FLORIS paths
floris_path = f"{workDir}/FLORIS_311_VF1_Operational"
turb_lib_path = f"{floris_path}/core/turbine_library"
print(f"floris_path: {floris_path}")
print(f"turb_lib_path: {turb_lib_path}")

# Add FLORIS to path
sys.path.append(floris_path)
try:
    import oyaml as yaml
except ImportError:
    import yaml

from core.tools import FlorisInterface

# Global variables for consistent workflow
fi = None
freq = None
baseline_aep = 0.0
optimized_aep = 0.0
internal_aep = 0.0
full_aep = 0.0
initial_layout = None
optimized_layout = None


class DEAPGAWorkflowManager:
    """
    Manages the complete 4-step DEAP-GA optimization workflow
    Replicates PyMoo workflow structure exactly
    """
    
    def __init__(self):
        self.fi = None
        self.freq = None
        self.layout = None
        self.boundaries = None
        self.baseline_aep = 0.0
        self.results = {}
        
        # Create output directories
        self._create_output_directories()
        
    def _create_output_directories(self):
        """Create output directories matching PyMoo structure"""
        self.output_dirs = {
            'input': workDir + '/Input/',
            'output': workDir + '/Output/',
            'output_initial': workDir + '/OutputInitial/',
            'output_internal': workDir + '/OutputInternal/'
        }
        
        for dir_path in self.output_dirs.values():
            os.makedirs(dir_path, exist_ok=True)
            
    def _plot_initial_layout(self):
        """Plot initial layout before optimization (matching PyMoo)"""
        try:
            boundaries_df = pd.read_csv(pr.boundariesFile)
            boundaries = list(zip(boundaries_df['X'], boundaries_df['Y']))
            
            x0 = self.layout['x'].values
            y0 = self.layout['y'].values
            
            plot_layout_beforeOptim(
                x0, y0, boundaries, 
                os.path.join(self.output_dirs['input'], "0.png"),
                "Initial Layout Before Optimization"
            )
            print(f"📊 Initial layout plot saved: {self.output_dirs['input']}0.png")
        except Exception as e:
            print(f"Warning: Could not create initial layout plot: {e}")
            
    def _plot_optimized_layout(self):
        """Plot optimized layout after optimization (matching PyMoo)"""
        try:
            if self.optimized_layout is not None:
                boundaries_df = pd.read_csv(pr.boundariesFile)
                boundaries = list(zip(boundaries_df['X'], boundaries_df['Y']))
                
                opt_x = self.optimized_layout['x'].values
                opt_y = self.optimized_layout['y'].values
                
                plot_layout_beforeOptim(
                    opt_x, opt_y, boundaries,
                    os.path.join(self.output_dirs['output'], "optimized_layout.png"),
                    "Final Optimized Layout"
                )
                print(f"📊 Optimized layout plot saved: {self.output_dirs['output']}optimized_layout.png")
        except Exception as e:
            print(f"Warning: Could not create optimized layout plot: {e}")
            
    def _export_csv_files(self):
        """Export CSV files matching PyMoo structure"""
        try:
            # Export initial layout
            if self.layout is not None:
                initial_df = self.layout.copy()
                initial_df.to_csv(os.path.join(self.output_dirs['output_initial'], "Initial.layout.csv"), index=False)
                print(f"📄 Initial layout CSV saved: {self.output_dirs['output_initial']}Initial.layout.csv")
            
            # Export optimized layout
            if self.optimized_layout is not None:
                opt_df = self.optimized_layout.copy()
                opt_df.to_csv(os.path.join(self.output_dirs['output'], "Optimized.layout.csv"), index=False)
                print(f"📄 Optimized layout CSV saved: {self.output_dirs['output']}Optimized.layout.csv")
                
                # Also save as internal layout (PyMoo compatibility)
                opt_df.to_csv(os.path.join(self.output_dirs['output_internal'], "InternalLayout.csv"), index=False)
                print(f"📄 Internal layout CSV saved: {self.output_dirs['output_internal']}InternalLayout.csv")
                
                # Final layout (same as optimized for internal-only optimization)
                opt_df.to_csv(os.path.join(self.output_dirs['output'], "Final.layout.csv"), index=False)
                print(f"📄 Final layout CSV saved: {self.output_dirs['output']}Final.layout.csv")
                
        except Exception as e:
            print(f"Warning: Could not export CSV files: {e}")
    
    def setup_floris_and_data(self):
        """Step 0: Setup FLORIS and load data (matches PyMoo exactly)"""
        print("\n🔄 Setting up FLORIS and loading time series data...")
        
        # Load layout data
        layout = pd.read_csv(pr.inputLayoutFile)
        layout = layout.rename(columns={'easting':'x','northing':'y'})
        layout['turb'] = layout['turb'].apply(sanitize_name)
        
        # Filter to internal turbines only for this optimization
        internal_layout = layout[layout['external'] == False].copy()
        print(f"Internal turbines for optimization: {len(internal_layout)}")
        
        # Create turbine library files if needed
        self._setup_turbine_library(internal_layout)
        
        # Load time series data and convert to frequency (matches PyMoo pattern)
        try:
            # Use proper file path - timeseries.txt, not windRoseFile
            inputs_loc = workDir + "/Input/"
            ts = pd.read_csv(inputs_loc + "timeseries.txt", sep=' ')
            print(f"Time series data loaded: {len(ts)} records")
            
            # Handle timestamp format properly (matches PyMoo exactly)
            if 'time' in ts.columns and ts.index.name is None:
                ts.index.name = 'date'
                ts.reset_index(inplace=True)
                ts['timestamp'] = pd.to_datetime(ts['date'].astype(str) + ' ' + ts['time'])
                ts = ts[['timestamp', 'wd', 'ws']]
            elif 'timestamp' not in ts.columns:
                if pd.api.types.is_datetime64_any_dtype(ts.index):
                    ts['timestamp'] = ts.index
                    ts.reset_index(drop=True, inplace=True)
                else:
                    # Assume first column is time if no timestamp
                    ts = ts[['time', 'wd', 'ws']].rename(columns={'time': 'timestamp'})
            
            # Convert to frequency DataFrame using utilities function
            WR = ts_to_freq_df(ts)
            WR['wd'] = pd.to_numeric(WR['wd'], errors='coerce')
            WR['ws'] = pd.to_numeric(WR['ws'], errors='coerce')
            print(f"Size of WR: {len(WR.index)}")
            
            # Extract wind data arrays - Ensure proper frequency array formatting
            wd_array = np.array(sorted(WR["wd"].unique()), dtype=float)
            ws_array = np.array(sorted(WR["ws"].unique()), dtype=float)
            
            # Create frequency matrix that matches FLORIS interface expectations
            freq = WR.set_index(['wd','ws']).unstack().values
            print(f"Frequency data shape: {freq.shape}")
            
            # Store wind arrays as instance variables for use throughout the class
            self.wd_array = wd_array
            self.ws_array = ws_array
            
        except Exception as e:
            print(f"Warning: Could not load time series data: {e}")
            print("Using simplified frequency data for testing")
            # Create simple frequency data for testing
            wind_directions = np.array(pr.analysis_directions)
            wind_speeds = np.array([6., 8., 10., 12., 14.])
            freq = np.ones((len(wind_directions), len(wind_speeds)))
            freq = freq / np.sum(freq)  # Normalize
        
        # Initialize FLORIS
        input_config = pr.FLORIS_CONFIG
        fi = FlorisInterface(input_config)
        
        fi.reinitialize(layout=(internal_layout['x'], internal_layout['y']),
                        turbine_type=internal_layout['turb'],
                        wind_directions=self.wd_array,
                        wind_speeds=self.ws_array)
        
        print(f"FLORIS setup complete:")
        print(f"  Wind directions: {len(self.wd_array)}")
        print(f"  Wind speeds: {len(self.ws_array)}")
        print(f"  Turbines: {len(internal_layout)}")
        
        # Set reference height
        ref_ht = fi.floris.farm.hub_heights[0]
        fi.floris.flow_field.reference_wind_height = ref_ht
        
        # Store for workflow
        self.fi = fi
        self.freq = freq
        self.layout = internal_layout
        
        # Plot initial layout
        self._plot_initial_layout()
        
        return fi, freq, internal_layout
        
    def _setup_turbine_library(self, layout):
        """Setup turbine library files (matches PyMoo exactly)"""
        unique_turbs = layout['turb'].unique()
        
        for turb_name in unique_turbs:
            safe_name = sanitize_name(turb_name)
            yaml_file = f'{turb_lib_path}/{safe_name}.yaml'
            
            if not os.path.exists(yaml_file):
                # Create turbine definition if missing
                print(f"Creating turbine definition for: {safe_name}")
                
                # Use default turbine parameters
                new_turb = {
                    'turbine_type': safe_name,
                    'generator_efficiency': 1.0,
                    'hub_height': 90.0,
                    'pP': 1.88,
                    'pT': 1.88,
                    'rotor_diameter': 107.0,
                    'TSR': 8.0,
                    'power_thrust_table': {
                        'power': [0.0, 0.15, 0.3, 0.45, 0.47, 0.47, 0.47, 0.47, 0.47, 0.47, 0.0],
                        'thrust': [1.1, 1.1, 0.9, 0.7, 0.4, 0.3, 0.2, 0.1, 0.05, 0.0, 0.0],
                        'wind_speed': [0.0, 3.0, 4.0, 5.0, 6.0, 8.0, 10.0, 12.0, 15.0, 25.0, 35.0]
                    }
                }
                
                with open(yaml_file, 'w') as f:
                    yaml.dump(new_turb, f)

    def run_floris_initial(self):
        """Step 1: Calculate baseline AEP"""
        print("\n" + "="*60)
        print("STEP 1: BASELINE AEP CALCULATION")
        print("="*60)
        
        # Calculate baseline AEP with current layout
        self.fi.calculate_wake()
        baseline_aep = self.fi.get_farm_AEP(freq=self.freq) / 1E9  # Convert to GWh
        
        print(f"✅ Baseline AEP calculated: {baseline_aep:.3f} GWh")
        print(f"   Internal turbines: {len(self.layout)}")
        print(f"   Wind conditions: {len(pr.analysis_directions)} directions")
        
        self.baseline_aep = baseline_aep
        self.results['baseline_aep'] = baseline_aep
        
        # Log results
        write_log(OverAllProgress, f"Step 1 - Baseline AEP: {baseline_aep:.3f} GWh")
        
        return baseline_aep
    
    def run_layout_optimization(self):
        """Step 2: Run DEAP-GA layout optimization"""
        print("\n" + "="*60)
        print("STEP 2: DEAP-GA LAYOUT OPTIMIZATION")
        print("="*60)
        
        # Load boundaries
        boundaries_df = pd.read_csv(pr.boundariesFile)
        boundaries = list(zip(boundaries_df['X'], boundaries_df['Y']))
        self.boundaries = boundaries
        
        # Create DEAP problem wrapper
        problem = DEAPProblemWrapper(
            fi=self.fi,
            freq=self.freq,
            layout=self.layout,
            boundaries=boundaries,
            baseline_aep=self.baseline_aep
        )
        
        # Configure DEAP-GA (matching PyMoo parameters)
        config = OptimizationConfig(
            algorithm='GA',
            pop_size=pr.PopSize,           # Match PyMoo population size
            n_gen=pr.MAXGEN,               # Match PyMoo generations
            crossover_prob=pr.pCross_real,  # Match PyMoo crossover
            eta_c=pr.eta_c,               # Match PyMoo SBX parameter
            eta_m=pr.eta_m,               # Match PyMoo mutation parameter
            verbose=True,                 # Show optimization progress
            enable_early_termination=True, # PyMoo-style convergence
            convergence_tol=pr.ftol,      # Match PyMoo tolerance
            patience=5                    # Match PyMoo patience
        )
        
        print(f"🧬 DEAP-GA Configuration:")
        print(f"   Population size: {config.pop_size}")
        print(f"   Generations: {config.n_gen}")
        print(f"   Crossover prob: {config.crossover_prob}")
        print(f"   Convergence tol: {config.convergence_tol}")
        
        # Start optimization timing
        optimization_start = timerpc()
        
        # Create and run DEAP optimizer
        manager = DEAPOptimizationManager(problem, config)
        result = manager.optimize()
        
        optimization_time = timerpc() - optimization_start
        
        # Extract optimized layout
        best_x = result['x']
        x_norm = best_x[:problem.n_turbines]
        y_norm = best_x[problem.n_turbines:]
        
        # Denormalize coordinates  
        opt_x = problem._unnorm(x_norm, problem.xmin, problem.xmax)
        opt_y = problem._unnorm(y_norm, problem.ymin, problem.ymax)
        
        # Validate optimized layout for constraint violations
        constraint_results = problem.evaluate(best_x)
        constraint_violations = np.sum(np.maximum(0, constraint_results['G']))
        
        # Calculate final AEP with optimized layout
        self.fi.reinitialize(layout=(opt_x, opt_y))
        optimized_aep = self.fi.get_farm_AEP(freq=self.freq) / 1E9  # Convert to GWh
        
        # Get actual fitness without constraint penalty for comparison
        actual_fitness = result['f'][0]  # This includes constraint penalties
        
        # Extract raw objective properly (handle both scalar and array cases)
        raw_objective_normalized = constraint_results['F']
        if hasattr(raw_objective_normalized, 'shape') and raw_objective_normalized.shape:
            raw_objective_normalized = raw_objective_normalized[0][0] if raw_objective_normalized.ndim > 1 else raw_objective_normalized[0]
        raw_objective = float(raw_objective_normalized) * self.baseline_aep  # Raw AEP without penalty
        
        # Calculate improvement
        improvement = optimized_aep - self.baseline_aep
        improvement_pct = (improvement / self.baseline_aep) * 100 if self.baseline_aep > 0 else 0
        
        print(f"✅ Optimization completed:")
        print(f"   Raw objective AEP: {raw_objective:.3f} GWh")
        print(f"   Final evaluated AEP: {optimized_aep:.3f} GWh")
        print(f"   Constraint violations: {constraint_violations:.6f}")
        print(f"   Final fitness (with penalties): {float(actual_fitness):.6f}")
        print(f"   Improvement: {improvement:.3f} GWh ({improvement_pct:.2f}%)")
        print(f"   Optimization time: {optimization_time:.2f} seconds")
        print(f"   Function evaluations: {result['n_evals']}")
        
        if constraint_violations > 1e-6:
            print(f"   ⚠️  WARNING: Solution violates constraints!")
            print(f"   ⚠️  Total violation: {constraint_violations:.6f}")
            print(f"   ⚠️  This explains why fitness dropped to ~{float(actual_fitness)*self.baseline_aep:.1f} GWh during optimization")
            
            # Analyze constraint types
            space_constraints = constraint_results['G'][:problem.n_turbines]
            boundary_constraints = constraint_results['G'][problem.n_turbines:]
            
            space_violations = np.sum(np.maximum(0, space_constraints))
            boundary_violations = np.sum(np.maximum(0, boundary_constraints))
            
            print(f"   ⚠️  Space constraint violations: {space_violations:.3f}")
            print(f"   ⚠️  Boundary constraint violations: {boundary_violations:.3f}")
            
            # Count how many turbines violate constraints
            space_violating_turbines = np.sum(space_constraints > 1e-6)
            boundary_violating_turbines = np.sum(boundary_constraints > 1e-6)
            
            print(f"   ⚠️  Turbines violating space constraints: {space_violating_turbines}/{problem.n_turbines}")
            print(f"   ⚠️  Turbines violating boundary constraints: {boundary_violating_turbines}/{problem.n_turbines}")
        else:
            print(f"   ✅ Solution is feasible (no constraint violations)!")
        
        # Store optimized layout
        self.optimized_layout = pd.DataFrame({
            'x': opt_x,
            'y': opt_y,
            'turb': self.layout['turb'].values,
            'external': False
        })
        
        # Store results
        self.results.update({
            'optimized_aep': optimized_aep,
            'improvement': improvement,
            'improvement_pct': improvement_pct,
            'optimization_time': optimization_time,
            'n_evals': result['n_evals'],
            'optimized_layout': self.optimized_layout
        })
        
        # Log results
        write_log(OverAllProgress, f"Step 2 - Optimized AEP: {optimized_aep:.3f} GWh (+{improvement_pct:.2f}%)")
        
        return optimized_aep
    
    def run_floris_internal(self):
        """Step 3: Simulate internal turbines only with optimized layout"""
        print("\n" + "="*60)
        print("STEP 3: INTERNAL TURBINES SIMULATION")
        print("="*60)
        
        # Use optimized layout for internal simulation
        if self.optimized_layout is not None:
            layout_for_sim = self.optimized_layout
        else:
            layout_for_sim = self.layout
            
        # Reinitialize with optimized internal layout
        self.fi.reinitialize(layout=(layout_for_sim['x'], layout_for_sim['y']))
        
        # Calculate AEP for internal turbines only
        self.fi.calculate_wake()
        internal_aep = self.fi.get_farm_AEP(freq=self.freq) / 1E9  # Convert to GWh
        
        print(f"✅ Internal simulation completed:")
        print(f"   Internal AEP: {internal_aep:.3f} GWh")
        print(f"   Turbines simulated: {len(layout_for_sim)}")
        
        self.results['internal_aep'] = internal_aep
        
        # Log results
        write_log(OverAllProgress, f"Step 3 - Internal AEP: {internal_aep:.3f} GWh")
        
        return internal_aep
    
    def run_floris_full(self):
        """Step 4: Full wind farm simulation"""
        print("\n" + "="*60)
        print("STEP 4: FULL WIND FARM SIMULATION")
        print("="*60)
        
        # For internal-only optimization, this is the same as Step 3
        # In external versions, this would include external turbines
        
        # Use optimized layout
        if self.optimized_layout is not None:
            layout_for_sim = self.optimized_layout
        else:
            layout_for_sim = self.layout
            
        # Full farm simulation (internal only for this script)
        self.fi.reinitialize(layout=(layout_for_sim['x'], layout_for_sim['y']))
        self.fi.calculate_wake()
        full_aep = self.fi.get_farm_AEP(freq=self.freq) / 1E9  # Convert to GWh
        
        print(f"✅ Full farm simulation completed:")
        print(f"   Full farm AEP: {full_aep:.3f} GWh")
        print(f"   Total turbines: {len(layout_for_sim)}")
        
        self.results['full_aep'] = full_aep
        
        # Log results
        write_log(OverAllProgress, f"Step 4 - Full Farm AEP: {full_aep:.3f} GWh")
        
        return full_aep
    
    def generate_summary(self):
        """Generate comprehensive summary report"""
        print("\n" + "="*60)
        print("DEAP-GA OPTIMIZATION WORKFLOW COMPLETE")
        print("="*60)
        
        baseline = self.results.get('baseline_aep', 0.0)
        optimized = self.results.get('optimized_aep', 0.0)
        internal = self.results.get('internal_aep', 0.0)
        full = self.results.get('full_aep', 0.0)
        improvement = self.results.get('improvement', 0.0)
        improvement_pct = self.results.get('improvement_pct', 0.0)
        opt_time = self.results.get('optimization_time', 0.0)
        n_evals = self.results.get('n_evals', 0)
        
        print(f"Step 1 - Baseline AEP:     {baseline:.3f} GWh")
        print(f"Step 2 - Optimized AEP:    {optimized:.3f} GWh")
        print(f"Step 3 - Internal AEP:     {internal:.3f} GWh")
        print(f"Step 4 - Full Farm AEP:    {full:.3f} GWh")
        print("-" * 60)
        print(f"Improvement:               {improvement:.3f} GWh ({improvement_pct:.2f}%)")
        print(f"Gross AEP (Internal):      {full:.3f} GWh")
        print(f"Net AEP (Total):           {full:.3f} GWh")
        print(f"Optimization Time:         {opt_time:.2f} seconds")
        print(f"Function Evaluations:      {n_evals}")
        print("="*60)
        
        # Save detailed results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"deap_ga_internal_results_{timestamp}.json"
        
        results_summary = {
            'timestamp': timestamp,
            'algorithm': 'DEAP-GA',
            'optimization_type': 'internal_only',
            'workflow_results': self.results,
            'summary': {
                'baseline_aep_gwh': baseline,
                'optimized_aep_gwh': optimized,
                'improvement_gwh': improvement,
                'improvement_percentage': improvement_pct,
                'optimization_time_seconds': opt_time,
                'function_evaluations': n_evals,
                'turbines_optimized': len(self.layout) if self.layout is not None else 0
            }
        }
        
        import json
        with open(results_file, 'w') as f:
            json.dump(results_summary, f, indent=2, default=str)
        
        print(f"📄 Detailed results saved to: {results_file}")
        
        # Generate plots and export CSV files
        self._plot_optimized_layout()
        self._export_csv_files()
        
        # Final log entry
        write_log(OverAllProgress, f"DEAP-GA Internal Optimization Complete - Final AEP: {full:.3f} GWh (+{improvement_pct:.2f}%)")
        
        return results_summary

    def run_complete_workflow(self):
        """Execute the complete 4-step optimization workflow"""
        print("🚀 Starting DEAP-GA wind farm optimization workflow...")
        
        # Step 0: Setup
        self.setup_floris_and_data()
        
        # Step 1: Calculate baseline AEP
        baseline_aep = self.run_floris_initial()
        
        # Step 2: Run layout optimization
        optimized_aep = self.run_layout_optimization()
        
        # Step 3: Simulate internal turbines only
        internal_aep = self.run_floris_internal()
        
        # Step 4: Full wind farm simulation
        full_aep = self.run_floris_full()
        
        # Summary
        results = self.generate_summary()
        
        return results


def main():
    """Main function - execute DEAP-GA optimization workflow"""
    print("🔬 DEAP-GA Internal Layout Optimization")
    print("Replicating PyMoo workflow with DEAP Genetic Algorithm")
    print("="*80)
    
    # Initialize workflow manager
    workflow = DEAPGAWorkflowManager()
    
    try:
        # Run complete workflow
        results = workflow.run_complete_workflow()
        
        print("\n✅ DEAP-GA optimization workflow completed successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ DEAP-GA optimization workflow failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
