#!/usr/bin/env python3
"""
Enhanced Directional External Farm Filtering
Test implementation for farm-level directional filtering with multiple cases support
"""

import numpy as np
import pandas as pd
import os
import sys
from typing import Dict, List, Tuple, Optional

# Import the base SmartExternalLayoutManager
sys.path.append('/project/bii69/ps/ps/optimization')
from utilities import load_boundaries

# Import the real SmartExternalLayoutManager with distance filtering
try:
    # Try to import from the main optimization script
    import importlib.util
    spec = importlib.util.spec_from_file_location("smart_manager", 
                                                "/project/bii69/ps/ps/optimization/opt-pymoo-windrose-freq-ts-external_smart.py")
    smart_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(smart_module)
    SmartExternalLayoutManager = smart_module.SmartExternalLayoutManager
    print("Successfully imported real SmartExternalLayoutManager with distance filtering")
except Exception as e:
    print(f"Warning: Could not import real SmartExternalLayoutManager: {e}")
    # Fallback to full implementation
    class SmartExternalLayoutManager:
        """Full implementation with distance filtering"""
        def __init__(self, internal_layout, external_layout, max_distance_km=20):
            self.internal_layout = internal_layout.copy()
            self.max_distance_m = max_distance_km * 1000
            self.internal_centroid = self._calculate_centroid(internal_layout)
            
            # Pre-filter external turbines by distance
            if len(external_layout) > 0:
                self.external_layout = self._filter_by_distance(external_layout)
            else:
                self.external_layout = pd.DataFrame()
            
            print(f"Smart Layout Manager initialized:")
            print(f"  Internal turbines: {len(self.internal_layout)}")
            print(f"  External turbines (total): {len(external_layout)}")
            print(f"  External turbines (within {max_distance_km}km): {len(self.external_layout)}")
            print(f"  Internal centroid: ({self.internal_centroid[0]:.0f}, {self.internal_centroid[1]:.0f})")
        
        def _filter_by_distance(self, external_layout):
            """Filter external turbines within max_distance from internal centroid"""
            external_coords = external_layout[['x', 'y']].values
            distances = np.sqrt(
                (external_coords[:, 0] - self.internal_centroid[0])**2 + 
                (external_coords[:, 1] - self.internal_centroid[1])**2
            )
            
            mask = distances <= self.max_distance_m
            filtered_external = external_layout[mask].copy()
            
            print(f"Distance filtering: {np.sum(~mask)} external turbines removed (>{self.max_distance_m/1000:.0f}km)")
            
            return filtered_external
        
        def _calculate_centroid(self, layout):
            """Calculate centroid of turbine layout"""
            if len(layout) == 0:
                return (0, 0)
            center_x = np.mean(layout['x'].values)
            center_y = np.mean(layout['y'].values)
            return (center_x, center_y)


class EnhancedDirectionalFarmManager(SmartExternalLayoutManager):
    """
    Enhanced manager with farm-aware directional filtering
    Handles multiple cases: no external, single farm, multiple farms, mixed turbines
    """
    
    def __init__(self, internal_layout: pd.DataFrame, external_layout: pd.DataFrame, 
                 max_distance_km: float = 20, sector_width: float = 90, 
                 farm_grouping: str = 'auto'):
        """
        Initialize enhanced manager with smart farm detection
        
        Args:
            internal_layout: Internal turbines DataFrame
            external_layout: External turbines DataFrame (can be empty)
            max_distance_km: Maximum distance threshold
            sector_width: Angular width for directional sectors (degrees)
            farm_grouping: 'auto', 'by_name', 'by_cluster', or 'individual'
        """
        # Handle empty external layout case
        if external_layout is None or len(external_layout) == 0:
            print("No external turbines detected - internal only optimization")
            self.has_external = False
            self.external_farms = {}
            self.sector_width = sector_width
            # Initialize with internal only
            super().__init__(internal_layout, pd.DataFrame(), max_distance_km)
            return
            
        self.has_external = True
        self.sector_width = sector_width
        self.farm_grouping = farm_grouping
        
        # Initialize parent class
        super().__init__(internal_layout, external_layout, max_distance_km)
        
        # Detect and group external farms
        self._detect_external_farms(farm_grouping)
        
        # Print summary
        self._print_farm_summary()
        
    def _detect_external_farms(self, grouping_method: str):
        """Intelligently detect and group external farms"""
        if grouping_method == 'auto':
            # Auto-detect based on turbine names
            if 'turb' in self.external_layout.columns and len(self.external_layout) > 0:
                unique_names = self.external_layout['turb'].unique()
                print(f"Auto-detecting farm grouping: {len(unique_names)} unique turbine names found")
                
                # Heuristic: if few unique names and they look like farm names, group by name
                if len(unique_names) <= 10 and any(' ' in str(name) for name in unique_names):
                    grouping_method = 'by_name'
                    print("  -> Detected farm names, grouping by name")
                else:
                    # Check if names follow pattern suggesting individual turbines
                    sample_names = [str(n) for n in unique_names[:5]]
                    if any('_' in name and any(char.isdigit() for char in name) for name in sample_names):
                        grouping_method = 'by_cluster'
                        print("  -> Detected individual turbine IDs, will use clustering")
                    else:
                        grouping_method = 'by_name'
            else:
                grouping_method = 'individual'
                print("  -> No turbine names found, treating as individual turbines")
                
        if grouping_method == 'by_name':
            self._group_by_name()
        elif grouping_method == 'by_cluster':
            self._group_by_cluster()
        else:  # individual
            self._group_individual()
        
        # Calculate bearings for all farms
        self._calculate_farm_bearings()
        
    def _group_by_name(self):
        """Group external turbines by their farm name"""
        self.external_farms = {}
        
        if 'turb' not in self.external_layout.columns:
            print("Warning: No 'turb' column found, treating all as one farm")
            self.external_farms['External_Farm'] = {
                'turbines': self.external_layout,
                'centroid': self._calculate_centroid(self.external_layout),
                'bearing': None,
                'distance': None
            }
            return
            
        for farm_name in self.external_layout['turb'].unique():
            farm_turbines = self.external_layout[self.external_layout['turb'] == farm_name].copy()
            centroid = self._calculate_centroid(farm_turbines)
            
            # Calculate distance from internal centroid
            distance = np.sqrt(
                (centroid[0] - self.internal_centroid[0])**2 + 
                (centroid[1] - self.internal_centroid[1])**2
            )
            
            self.external_farms[str(farm_name)] = {
                'turbines': farm_turbines,
                'centroid': centroid,
                'bearing': None,
                'distance': distance,
                'n_turbines': len(farm_turbines)
            }
            
    def _group_by_cluster(self):
        """Use spatial clustering to detect farms"""
        try:
            from sklearn.cluster import DBSCAN
        except ImportError:
            print("Warning: sklearn not available, falling back to individual grouping")
            self._group_individual()
            return
            
        coords = self.external_layout[['x', 'y']].values
        
        # Adaptive clustering based on turbine spacing
        # Estimate typical turbine spacing
        if len(coords) > 1:
            from scipy.spatial.distance import pdist
            distances = pdist(coords)
            typical_spacing = np.percentile(distances[distances > 0], 10)
            eps = typical_spacing * 3  # Turbines within 3x typical spacing are same farm
        else:
            eps = 1000  # Default 1km
            
        clustering = DBSCAN(eps=eps, min_samples=3).fit(coords)
        
        self.external_farms = {}
        
        for cluster_id in set(clustering.labels_):
            if cluster_id == -1:  # Noise points - treat as individual
                mask = clustering.labels_ == -1
                noise_turbines = self.external_layout[mask]
                for idx, (_, row) in enumerate(noise_turbines.iterrows()):
                    self.external_farms[f"Individual_{idx}"] = {
                        'turbines': pd.DataFrame([row]),
                        'centroid': (row['x'], row['y']),
                        'bearing': None,
                        'distance': np.sqrt(
                            (row['x'] - self.internal_centroid[0])**2 + 
                            (row['y'] - self.internal_centroid[1])**2
                        ),
                        'n_turbines': 1
                    }
            else:
                mask = clustering.labels_ == cluster_id
                farm_turbines = self.external_layout[mask].copy()
                centroid = self._calculate_centroid(farm_turbines)
                distance = np.sqrt(
                    (centroid[0] - self.internal_centroid[0])**2 + 
                    (centroid[1] - self.internal_centroid[1])**2
                )
                
                farm_name = f"Cluster_{cluster_id}"
                self.external_farms[farm_name] = {
                    'turbines': farm_turbines,
                    'centroid': centroid,
                    'bearing': None,
                    'distance': distance,
                    'n_turbines': len(farm_turbines)
                }
                
    def _group_individual(self):
        """Treat each turbine as its own farm"""
        self.external_farms = {}
        
        for idx, (_, row) in enumerate(self.external_layout.iterrows()):
            farm_name = f"Turbine_{idx}"
            distance = np.sqrt(
                (row['x'] - self.internal_centroid[0])**2 + 
                (row['y'] - self.internal_centroid[1])**2
            )
            
            self.external_farms[farm_name] = {
                'turbines': pd.DataFrame([row]),
                'centroid': (row['x'], row['y']),
                'bearing': None,
                'distance': distance,
                'n_turbines': 1
            }
            
    def _calculate_farm_bearings(self):
        """Calculate bearing from internal centroid to each farm centroid"""
        for farm_name, farm_data in self.external_farms.items():
            dx = farm_data['centroid'][0] - self.internal_centroid[0]
            dy = farm_data['centroid'][1] - self.internal_centroid[1]
            
            # Calculate bearing (0° = North, 90° = East)
            # Using meteorological convention
            bearing = np.degrees(np.arctan2(dx, dy)) % 360
            farm_data['bearing'] = bearing
            
    def _print_farm_summary(self):
        """Print summary of detected farms"""
        if not self.has_external:
            return
            
        print(f"\nExternal Farm Summary:")
        print(f"  Total external turbines: {len(self.external_layout)}")
        print(f"  Number of farms detected: {len(self.external_farms)}")
        print(f"  Grouping method: {self.farm_grouping}")
        print(f"\n  Farm Details:")
        
        for farm_name, farm_data in sorted(self.external_farms.items(), 
                                          key=lambda x: x[1]['distance']):
            print(f"    {farm_name}:")
            print(f"      Turbines: {farm_data['n_turbines']}")
            print(f"      Distance: {farm_data['distance']/1000:.1f} km")
            print(f"      Bearing: {farm_data['bearing']:.1f}°")
            print(f"      Centroid: ({farm_data['centroid'][0]:.0f}, {farm_data['centroid'][1]:.0f})")
            
    def get_relevant_farms_for_direction(self, wind_direction: float) -> List[str]:
        """
        Get list of farms that can affect internal site for given wind direction
        
        Args:
            wind_direction: Wind direction in degrees (0=N, 90=E, 180=S, 270=W)
            
        Returns:
            List of farm names that are upwind
        """
        if not self.has_external:
            return []
            
        relevant_farms = []
        
        for farm_name, farm_data in self.external_farms.items():
            # Wind comes FROM this direction, so farms in opposite direction are upwind
            # If wind is from North (0°), farms to the North are upwind
            upwind_bearing = wind_direction
            
            # Calculate angular difference
            bearing_to_farm = farm_data['bearing']
            
            # Angular difference between wind direction and bearing to farm
            angle_diff = abs((bearing_to_farm - upwind_bearing + 180) % 360 - 180)
            
            # Include farm if it's within the upwind sector
            if angle_diff <= self.sector_width / 2:
                relevant_farms.append(farm_name)
                
        return relevant_farms
        
    def get_layout_with_farm_filtering(self, wind_direction: float) -> pd.DataFrame:
        """
        Get layout with farm-level directional filtering
        
        Args:
            wind_direction: Wind direction in degrees
            
        Returns:
            Combined DataFrame with internal and relevant external turbines
        """
        if not self.has_external:
            result = self.internal_layout.copy()
            result['weight'] = 1
            return result
            
        # Get relevant farms for this wind direction
        relevant_farms = self.get_relevant_farms_for_direction(wind_direction)
        
        # Collect all turbines from relevant farms
        relevant_externals = []
        for farm_name in relevant_farms:
            relevant_externals.append(self.external_farms[farm_name]['turbines'])
            
        if relevant_externals:
            external_subset = pd.concat(relevant_externals, ignore_index=True)
            combined = pd.concat([self.internal_layout, external_subset], 
                                ignore_index=True)
        else:
            combined = self.internal_layout.copy()
            
        # Set weights
        combined.loc[combined['external'] == False, 'weight'] = 1
        combined.loc[combined['external'] == True, 'weight'] = 0
        
        return combined
        
    def get_filtering_statistics(self) -> Dict:
        """Get statistics about filtering performance"""
        stats = {
            'n_internal': len(self.internal_layout),
            'n_external_total': len(self.external_layout),
            'n_farms': len(self.external_farms),
            'farms': {}
        }
        
        # Statistics per wind direction
        wind_dirs = np.arange(0, 360, 30)  # Every 30 degrees
        direction_stats = {}
        
        for wd in wind_dirs:
            relevant_farms = self.get_relevant_farms_for_direction(wd)
            n_turbines = sum(self.external_farms[f]['n_turbines'] for f in relevant_farms)
            direction_stats[f"{wd}°"] = {
                'n_farms': len(relevant_farms),
                'n_turbines': n_turbines,
                'farms': relevant_farms
            }
            
        stats['by_direction'] = direction_stats
        
        # Farm-specific statistics
        for farm_name, farm_data in self.external_farms.items():
            stats['farms'][farm_name] = {
                'n_turbines': farm_data['n_turbines'],
                'distance_km': farm_data['distance'] / 1000,
                'bearing': farm_data['bearing']
            }
            
        return stats

    def get_cache_stats(self) -> str:
        """Get cache statistics in string format for logging"""
        if not self.has_external:
            return "No external turbines - no caching performed"
        
        stats = self.get_filtering_statistics()
        
        result = f"Smart filtering cache statistics:\n"
        result += f"  Total external turbines: {stats['n_external_total']}\n"
        result += f"  Number of farms detected: {stats['n_farms']}\n"
        result += f"  Grouping method: {getattr(self, 'farm_grouping', 'auto')}\n"
        
        # Show average filtering efficiency
        total_possible = stats['n_external_total'] * 12  # 12 directions sampled
        total_used = sum(d['n_turbines'] for d in stats['by_direction'].values())
        efficiency = (1 - total_used / total_possible) * 100 if total_possible > 0 else 0
        
        result += f"  Filtering efficiency: {efficiency:.1f}% reduction\n"
        result += f"  Average turbines per direction: {total_used/12:.1f}\n"
        
        return result


def test_basic_functionality():
    """Test basic functionality with sample data"""
    print("="*60)
    print("Testing Enhanced Directional Farm Filtering")
    print("="*60)
    
    # Create sample internal layout
    internal_data = {
        'x': [530000, 531000, 532000, 533000, 534000],
        'y': [5850000, 5850000, 5850000, 5850000, 5850000],
        'external': [False] * 5,
        'turb': ['Internal'] * 5
    }
    internal_df = pd.DataFrame(internal_data)
    
    # Test Case 1: No external turbines
    print("\nTest Case 1: No external turbines")
    print("-" * 40)
    empty_external = pd.DataFrame()
    manager1 = EnhancedDirectionalFarmManager(internal_df, empty_external)
    layout1 = manager1.get_layout_with_farm_filtering(0)
    print(f"Result: {len(layout1)} turbines in layout")
    
    # Test Case 2: Single external farm
    print("\nTest Case 2: Single external farm to the North")
    print("-" * 40)
    external_data = {
        'x': [531000, 532000, 533000] * 3,
        'y': [5852000, 5852000, 5852000, 5853000, 5853000, 5853000, 5854000, 5854000, 5854000],
        'external': [True] * 9,
        'turb': ['North Farm'] * 9
    }
    external_df = pd.DataFrame(external_data)
    manager2 = EnhancedDirectionalFarmManager(internal_df, external_df)
    
    # Test different wind directions
    for wd in [0, 90, 180, 270]:
        farms = manager2.get_relevant_farms_for_direction(wd)
        layout = manager2.get_layout_with_farm_filtering(wd)
        n_external = len(layout[layout['external'] == True])
        print(f"  Wind from {wd}°: {len(farms)} farms, {n_external} external turbines")
    
    # Test Case 3: Multiple external farms
    print("\nTest Case 3: Multiple external farms in different directions")
    print("-" * 40)
    multi_external = pd.DataFrame({
        'x': [532000]*5 + [528000]*5 + [532000]*5 + [536000]*5,
        'y': [5855000]*5 + [5850000]*5 + [5845000]*5 + [5850000]*5,
        'external': [True] * 20,
        'turb': ['North Farm']*5 + ['West Farm']*5 + ['South Farm']*5 + ['East Farm']*5
    })
    manager3 = EnhancedDirectionalFarmManager(multi_external, multi_external, sector_width=120)
    
    stats = manager3.get_filtering_statistics()
    print(f"\nDetected {stats['n_farms']} farms from {stats['n_external_total']} turbines")
    
    # Show which farms are active for each cardinal direction
    for wd in [0, 90, 180, 270]:
        wd_stats = stats['by_direction'][f'{wd}°']
        print(f"  Wind from {wd}°: {wd_stats['n_farms']} farms active ({wd_stats['farms']})")
    
    return manager1, manager2, manager3


if __name__ == "__main__":
    # Run basic tests
    managers = test_basic_functionality()
    
    print("\n" + "="*60)
    print("Testing complete! Use the returned managers for visualization.")
    print("="*60)