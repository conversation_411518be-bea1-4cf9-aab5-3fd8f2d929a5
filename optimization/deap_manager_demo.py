#!/usr/bin/env python3
"""
DEAP Optimization Manager Demonstration Script

This script showcases the flexible DEAP optimization manager with:
- Easy algorithm switching between all DEAP methods
- Consistent normalization, initialization, and constraint handling  
- Multi-algorithm comparison and benchmarking
- Hybrid optimization sequences
- Comprehensive configuration through params.py

Run examples:
    python3 deap_manager_demo.py --algorithm GA
    python3 deap_manager_demo.py --compare GA DE PSO
    python3 deap_manager_demo.py --hybrid exploration_exploitation
    python3 deap_manager_demo.py --benchmark
"""

import sys
import argparse
import time
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime

# Add project paths
workDir = str(Path(__file__).parent.absolute())
sys.path.append(workDir)
sys.path.append(f"{workDir}/FLORIS_311_VF1_Operational")

# Import project modules
import params as pr
from deap_optimization_manager import DEAPOptimizationManager, DEAPProblemWrapper, OptimizationConfig

# Import utilities
from utilities import sanitize_name, memory_footprint

# FLORIS imports
try:
    from core.tools import FlorisInterface
except ImportError:
    print("FLORIS not available - using mock interface")
    FlorisInterface = None


def setup_floris_and_data():
    """Setup FLORIS interface and data for demonstration"""
    print("\n🔄 Setting up FLORIS and wind farm data...")
    
    if FlorisInterface is None:
        print("⚠️  FLORIS not available - cannot run real optimization")
        return None, None, None, None
    
    # Create simplified wind rose data
    wind_directions = np.array([0., 45., 90., 135., 180., 225., 270., 315.])
    wind_speeds = np.array([6., 8., 10., 12., 14.])
    freq = np.ones((len(wind_directions), len(wind_speeds))) / (len(wind_directions) * len(wind_speeds))
    
    print(f"Wind rose: {len(wind_directions)} directions, {len(wind_speeds)} speeds")
    
    # Load turbine layout
    try:
        layout = pd.read_csv("Input/Initial.layout.csv")
        layout = layout.rename(columns={'easting':'x','northing':'y'})
        layout['turb'] = layout['turb'].apply(sanitize_name)
        
        # Filter to internal turbines only for demo
        internal_layout = layout[layout['external'] == False].copy()
        print(f"Internal turbines for optimization: {len(internal_layout)}")
        
        if len(internal_layout) == 0:
            print("⚠️  No internal turbines found - using first 10 turbines")
            internal_layout = layout.head(10).copy()
            internal_layout['external'] = False
        
        # Initialize FLORIS
        fi = FlorisInterface(pr.FLORIS_CONFIG)
        fi.reinitialize(layout=(internal_layout['x'], internal_layout['y']),
                        turbine_type=internal_layout['turb'],
                        wind_directions=wind_directions,
                        wind_speeds=wind_speeds)
        
        # Calculate baseline AEP
        fi.calculate_wake()
        baseline_aep = fi.get_farm_AEP(freq=freq) / 1E9  # Convert to GWh
        print(f"FLORIS setup complete - Baseline AEP: {baseline_aep:.3f} GWh")
        
        return fi, freq, internal_layout, baseline_aep
        
    except Exception as e:
        print(f"⚠️  Error setting up FLORIS: {e}")
        return None, None, None, None


def demo_single_algorithm(algorithm_name: str):
    """Demonstrate single algorithm optimization"""
    print(f"\n🧬 Single Algorithm Demo: {algorithm_name}")
    print("=" * 60)
    
    # Setup problem
    fi, freq, layout, baseline_aep = setup_floris_and_data()
    if fi is None:
        print("Cannot run demo without FLORIS")
        return
    
    # Create problem wrapper
    problem = DEAPProblemWrapper(
        fi=fi,
        freq=freq,
        layout=layout,
        boundaries=pr.boundariesFile,
        baseline_aep=baseline_aep
    )
    
    # Create configuration
    config = OptimizationConfig(
        algorithm=algorithm_name,
        pop_size=16,  # Small for demo
        n_gen=5,      # Few generations for demo
        verbose=True
    )
    
    # Create optimization manager
    manager = DEAPOptimizationManager(problem, config)
    
    # Show algorithm info
    algo_info = manager.algorithm_info
    print(f"\n📋 Algorithm: {algo_info['name']} - {algo_info['description']}")
    
    # Run optimization
    start_time = time.time()
    result = manager.optimize()
    total_time = time.time() - start_time
    
    # Display results
    print(f"\n✅ Optimization completed in {total_time:.2f} seconds")
    print(f"   Best AEP: {result['f'][0]:.6f} (normalized)")
    print(f"   Evaluations: {result['n_evals']}")
    print(f"   Best solution shape: {result['x'].shape}")
    
    return result


def demo_algorithm_comparison(algorithms: list):
    """Demonstrate multi-algorithm comparison"""
    print(f"\n🔬 Multi-Algorithm Comparison Demo")
    print(f"Algorithms: {', '.join(algorithms)}")
    print("=" * 60)
    
    # Setup problem
    fi, freq, layout, baseline_aep = setup_floris_and_data()
    if fi is None:
        print("Cannot run demo without FLORIS")
        return
    
    # Create problem wrapper
    problem = DEAPProblemWrapper(
        fi=fi,
        freq=freq,
        layout=layout,
        boundaries=pr.boundariesFile,
        baseline_aep=baseline_aep
    )
    
    # Create configuration (small for demo)
    config = OptimizationConfig(
        pop_size=16,
        n_gen=5,
        verbose=True
    )
    
    # Create optimization manager
    manager = DEAPOptimizationManager(problem, config)
    
    # Run comparison
    start_time = time.time()
    comparison_results = manager.compare_algorithms(algorithms, runs_per_algorithm=1)
    total_time = time.time() - start_time
    
    # Display comparison results
    print(f"\n📊 Comparison completed in {total_time:.2f} seconds")
    print("\nResults Summary:")
    print("-" * 50)
    
    for algorithm, results in comparison_results.items():
        stats = results['statistics']
        print(f"{algorithm:12} | Best: {stats['fitness']['max']:.6f} | "
              f"Avg: {stats['fitness']['mean']:.6f}±{stats['fitness']['std']:.6f} | "
              f"Time: {stats['time']['mean']:.2f}s")
    
    # Find best algorithm
    best_algorithm = max(comparison_results.keys(), 
                        key=lambda x: comparison_results[x]['statistics']['fitness']['max'])
    best_fitness = comparison_results[best_algorithm]['statistics']['fitness']['max']
    
    print(f"\n🏆 Best performing algorithm: {best_algorithm} (AEP: {best_fitness:.6f})")
    
    return comparison_results


def demo_hybrid_optimization(sequence_name: str):
    """Demonstrate hybrid optimization sequence"""
    print(f"\n🔗 Hybrid Optimization Demo: {sequence_name}")
    print("=" * 60)
    
    # Setup problem
    fi, freq, layout, baseline_aep = setup_floris_and_data()
    if fi is None:
        print("Cannot run demo without FLORIS")
        return
    
    # Create problem wrapper
    problem = DEAPProblemWrapper(
        fi=fi,
        freq=freq,
        layout=layout,
        boundaries=pr.boundariesFile,
        baseline_aep=baseline_aep
    )
    
    # Get hybrid sequence from params
    if sequence_name not in pr.deap_hybrid_sequences:
        print(f"⚠️  Unknown sequence: {sequence_name}")
        print(f"Available sequences: {list(pr.deap_hybrid_sequences.keys())}")
        return
    
    sequence = pr.deap_hybrid_sequences[sequence_name]
    
    # Scale down generations for demo
    demo_sequence = []
    for stage in sequence:
        demo_stage = stage.copy()
        demo_stage['generations'] = min(stage['generations'], 3)  # Max 3 generations per stage
        demo_sequence.append(demo_stage)
    
    print(f"Hybrid sequence ({len(demo_sequence)} stages):")
    for i, stage in enumerate(demo_sequence):
        purpose = stage.get('purpose', 'optimization')
        print(f"  Stage {i+1}: {stage['algorithm']} for {stage['generations']} gen ({purpose})")
    
    # Create configuration
    config = OptimizationConfig(
        pop_size=16,
        verbose=True
    )
    
    # Create optimization manager
    manager = DEAPOptimizationManager(problem, config)
    
    # Run hybrid optimization
    start_time = time.time()
    result = manager.run_hybrid_sequence(demo_sequence)
    total_time = time.time() - start_time
    
    # Display results
    print(f"\n✅ Hybrid optimization completed in {total_time:.2f} seconds")
    print(f"   Final best AEP: {result['final_best_f'][0]:.6f}")
    print(f"   Total evaluations: {result['total_evaluations']}")
    
    print("\nStage-by-stage results:")
    for stage_result in result['stage_results']:
        stage_aep = stage_result['result']['f'][0]
        stage_time = stage_result['stage_time']
        print(f"  Stage {stage_result['stage']} ({stage_result['algorithm']}): "
              f"AEP={stage_aep:.6f}, Time={stage_time:.2f}s")
    
    return result


def demo_operator_switching():
    """Demonstrate flexible operator switching"""
    print(f"\n🛠️  Operator Switching Demo")
    print("=" * 60)
    
    # Setup problem (simplified for demo)
    fi, freq, layout, baseline_aep = setup_floris_and_data()
    if fi is None:
        print("Cannot run demo without FLORIS")
        return
    
    # Create problem wrapper
    problem = DEAPProblemWrapper(
        fi=fi,
        freq=freq,
        layout=layout,
        boundaries=pr.boundariesFile,
        baseline_aep=baseline_aep
    )
    
    # Test different operator combinations
    operator_combinations = [
        {'selection': 'tournament', 'crossover': 'blend', 'mutation': 'gaussian'},
        {'selection': 'roulette', 'crossover': 'sbx', 'mutation': 'polynomial'},
        {'selection': 'tournament', 'crossover': 'uniform', 'mutation': 'uniform'}
    ]
    
    results = []
    
    for i, operators in enumerate(operator_combinations):
        print(f"\n🔧 Testing operator combination {i+1}:")
        print(f"   Selection: {operators['selection']}")
        print(f"   Crossover: {operators['crossover']}")
        print(f"   Mutation: {operators['mutation']}")
        
        # Create configuration
        config = OptimizationConfig(
            algorithm='GA',
            pop_size=16,
            n_gen=3,  # Very short for demo
            verbose=False,
            **operators
        )
        
        # Create and run optimization
        manager = DEAPOptimizationManager(problem, config)
        result = manager.optimize()
        
        results.append({
            'operators': operators,
            'fitness': result['f'][0],
            'time': result['exec_time'],
            'evals': result['n_evals']
        })
        
        print(f"   Result: AEP={result['f'][0]:.6f}, Time={result['exec_time']:.2f}s")
    
    # Find best combination
    best_combo = max(results, key=lambda x: x['fitness'])
    print(f"\n🏆 Best operator combination:")
    print(f"   Operators: {best_combo['operators']}")
    print(f"   AEP: {best_combo['fitness']:.6f}")
    
    return results


def demo_configuration_flexibility():
    """Demonstrate configuration flexibility"""
    print(f"\n⚙️  Configuration Flexibility Demo")
    print("=" * 60)
    
    # Show available algorithms
    from deap_optimization_manager import DEAPOptimizationManager
    print("📋 Available algorithms:")
    for algo, desc in DEAPOptimizationManager.SUPPORTED_ALGORITHMS.items():
        print(f"   {algo:12} - {desc}")
    
    # Show available operators
    print(f"\n🛠️  Available operators:")
    print("Selection methods:")
    for method, info in pr.deap_operators_config['selection'].items():
        print(f"   {method:12} - {info['description']}")
    
    print("Crossover methods:")
    for method, info in pr.deap_operators_config['crossover'].items():
        print(f"   {method:12} - {info['description']}")
    
    print("Mutation methods:")
    for method, info in pr.deap_operators_config['mutation'].items():
        print(f"   {method:12} - {info['description']}")
    
    # Show hybrid sequences
    print(f"\n🔗 Available hybrid sequences:")
    for seq_name, sequence in pr.deap_hybrid_sequences.items():
        total_gens = sum(stage['generations'] for stage in sequence)
        algorithms = [stage['algorithm'] for stage in sequence]
        print(f"   {seq_name:20} - {' → '.join(algorithms)} ({total_gens} total generations)")


def create_benchmark_report(comparison_results):
    """Create detailed benchmark report"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    # Create report
    report = []
    report.append("=" * 80)
    report.append("DEAP OPTIMIZATION MANAGER BENCHMARK REPORT")
    report.append("=" * 80)
    report.append(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    report.append("")
    
    # Algorithm performance summary
    report.append("📊 ALGORITHM PERFORMANCE SUMMARY:")
    report.append("-" * 50)
    
    # Sort by best fitness
    sorted_algorithms = sorted(comparison_results.items(), 
                             key=lambda x: x[1]['statistics']['fitness']['max'], 
                             reverse=True)
    
    for rank, (algorithm, results) in enumerate(sorted_algorithms, 1):
        stats = results['statistics']
        report.append(f"{rank}. {algorithm}")
        report.append(f"   Best AEP: {stats['fitness']['max']:.6f}")
        report.append(f"   Average: {stats['fitness']['mean']:.6f} ± {stats['fitness']['std']:.6f}")
        report.append(f"   Runtime: {stats['time']['mean']:.2f}s ± {stats['time']['std']:.2f}s")
        report.append(f"   Evaluations: {stats['evaluations']['mean']:.0f}")
        report.append("")
    
    # Save report
    report_text = "\n".join(report)
    report_filename = f"deap_benchmark_report_{timestamp}.txt"
    
    with open(report_filename, 'w') as f:
        f.write(report_text)
    
    print(f"📄 Benchmark report saved: {report_filename}")
    return report_filename


def main():
    """Main demonstration function"""
    parser = argparse.ArgumentParser(description="DEAP Optimization Manager Demo")
    parser.add_argument('--algorithm', '-a', type=str, default='GA',
                       help='Single algorithm to demonstrate')
    parser.add_argument('--compare', '-c', nargs='+', default=None,
                       help='Compare multiple algorithms')
    parser.add_argument('--hybrid', '-h', type=str, default=None,
                       help='Run hybrid optimization sequence')
    parser.add_argument('--operators', '-o', action='store_true',
                       help='Demonstrate operator switching')
    parser.add_argument('--config', action='store_true',
                       help='Show configuration options')
    parser.add_argument('--benchmark', '-b', action='store_true',
                       help='Run comprehensive benchmark')
    
    args = parser.parse_args()
    
    print("🚀 DEAP Optimization Manager Demonstration")
    print("=" * 80)
    print("Flexible Multi-Algorithm Wind Farm Layout Optimization")
    print("Features: Consistent normalization, initialization, constraint handling")
    print("Supported: GA, DE, PSO, CMA-ES, NSGA-II, NSGA-III, SPEA2, ES")
    
    try:
        if args.config:
            demo_configuration_flexibility()
            
        elif args.operators:
            demo_operator_switching()
            
        elif args.compare:
            comparison_results = demo_algorithm_comparison(args.compare)
            if comparison_results:
                create_benchmark_report(comparison_results)
                
        elif args.hybrid:
            demo_hybrid_optimization(args.hybrid)
            
        elif args.benchmark:
            print("\n🔬 Running comprehensive benchmark...")
            algorithms_to_test = ['GA', 'DE', 'PSO']  # Skip CMA-ES if not installed
            comparison_results = demo_algorithm_comparison(algorithms_to_test)
            if comparison_results:
                create_benchmark_report(comparison_results)
                
        else:
            demo_single_algorithm(args.algorithm)
            
        print("\n✅ Demo completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⚠️  Demo interrupted by user")
    except Exception as e:
        print(f"\n❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())